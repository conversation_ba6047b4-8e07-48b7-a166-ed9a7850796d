#!/bin/bash

# 课程评价系统环境检查脚本

echo "🔍 正在检查系统环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✅ $1 已安装${NC}"
        if [ "$1" = "node" ]; then
            echo -e "   版本: $(node --version)"
        elif [ "$1" = "npm" ]; then
            echo -e "   版本: $(npm --version)"
        elif [ "$1" = "psql" ]; then
            echo -e "   版本: $(psql --version | head -n1)"
        fi
        return 0
    else
        echo -e "${RED}❌ $1 未安装${NC}"
        return 1
    fi
}

# 检查文件是否存在
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1 存在${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 不存在${NC}"
        return 1
    fi
}

# 检查目录是否存在
check_directory() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✅ $1 目录存在${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 目录不存在${NC}"
        return 1
    fi
}

echo -e "${BLUE}📋 环境要求检查${NC}"
echo "================================"

# 检查Node.js
check_command "node"
NODE_CHECK=$?

# 检查npm
check_command "npm"
NPM_CHECK=$?

# 检查PostgreSQL
echo -e "\n${BLUE}🗄️  数据库检查${NC}"
echo "================================"
check_command "psql"
PSQL_CHECK=$?

if [ $PSQL_CHECK -eq 0 ]; then
    echo -e "${YELLOW}💡 请确保PostgreSQL服务正在运行${NC}"
    echo -e "   启动命令: sudo systemctl start postgresql"
    echo -e "   或: brew services start postgresql (macOS)"
fi

# 检查项目文件结构
echo -e "\n${BLUE}📁 项目文件检查${NC}"
echo "================================"

# 检查主要文件
check_file "package.json"
check_file "README.md"
check_file "start.sh"

# 检查前端文件
echo -e "\n${YELLOW}前端文件:${NC}"
check_directory "client"
check_file "client/package.json"
check_file "client/vite.config.js"
check_file "client/index.html"
check_directory "client/src"

# 检查后端文件
echo -e "\n${YELLOW}后端文件:${NC}"
check_directory "server"
check_file "server/package.json"
check_file "server/.env.example"
check_directory "server/src"
check_directory "server/prisma"
check_file "server/prisma/schema.prisma"

# 检查环境配置
echo -e "\n${BLUE}⚙️  配置文件检查${NC}"
echo "================================"

if [ -f "server/.env" ]; then
    echo -e "${GREEN}✅ server/.env 配置文件存在${NC}"
    echo -e "${YELLOW}💡 请确保数据库连接信息正确${NC}"
else
    echo -e "${YELLOW}⚠️  server/.env 配置文件不存在${NC}"
    echo -e "   将从 server/.env.example 复制示例配置"
    if [ -f "server/.env.example" ]; then
        cp server/.env.example server/.env
        echo -e "${GREEN}✅ 已创建 server/.env 文件${NC}"
        echo -e "${YELLOW}📝 请编辑 server/.env 文件配置数据库连接${NC}"
    fi
fi

# 检查依赖安装
echo -e "\n${BLUE}📦 依赖检查${NC}"
echo "================================"

if [ -d "node_modules" ]; then
    echo -e "${GREEN}✅ 根目录依赖已安装${NC}"
else
    echo -e "${YELLOW}⚠️  根目录依赖未安装${NC}"
fi

if [ -d "client/node_modules" ]; then
    echo -e "${GREEN}✅ 前端依赖已安装${NC}"
else
    echo -e "${YELLOW}⚠️  前端依赖未安装${NC}"
fi

if [ -d "server/node_modules" ]; then
    echo -e "${GREEN}✅ 后端依赖已安装${NC}"
else
    echo -e "${YELLOW}⚠️  后端依赖未安装${NC}"
fi

# 总结
echo -e "\n${BLUE}📊 检查总结${NC}"
echo "================================"

if [ $NODE_CHECK -eq 0 ] && [ $NPM_CHECK -eq 0 ]; then
    echo -e "${GREEN}✅ 基础环境检查通过${NC}"
    
    echo -e "\n${YELLOW}🚀 下一步操作:${NC}"
    echo "1. 配置数据库连接 (编辑 server/.env)"
    echo "2. 安装项目依赖: npm run install:all"
    echo "3. 运行数据库迁移: npm run db:migrate"
    echo "4. 填充演示数据: npm run db:seed"
    echo "5. 启动开发服务器: npm run dev"
    echo ""
    echo -e "${BLUE}或者直接运行一键启动脚本:${NC}"
    echo "./start.sh"
    
else
    echo -e "${RED}❌ 环境检查失败${NC}"
    echo -e "\n${YELLOW}请先安装以下软件:${NC}"
    
    if [ $NODE_CHECK -ne 0 ]; then
        echo "- Node.js 18+ (https://nodejs.org/)"
    fi
    
    if [ $NPM_CHECK -ne 0 ]; then
        echo "- npm (通常随Node.js一起安装)"
    fi
    
    if [ $PSQL_CHECK -ne 0 ]; then
        echo "- PostgreSQL 13+ (https://www.postgresql.org/)"
    fi
fi

echo -e "\n${BLUE}📚 更多信息:${NC}"
echo "- 项目文档: README.md"
echo "- 使用指南: USAGE.md"
echo "- 项目总结: PROJECT_SUMMARY.md"
echo ""
echo -e "${GREEN}🎓 课程评价系统 - 让教育更有温度！${NC}"
