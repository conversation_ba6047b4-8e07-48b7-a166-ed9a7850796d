# 🚀 教育评价平台 - 完整安装部署调试指南

## 📋 项目概览

这是一个基于 **Next.js + PostgreSQL** 的教育评价平台，支持完全的环境隔离：
- **开发环境**：热重载、调试模式、端口 3001
- **生产环境**：优化构建、安全配置、端口 3000

## 🛠️ 环境准备

### 1. 系统要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 2GB 可用内存

### 2. 检查环境
```bash
# 检查 Docker 版本
docker --version
docker-compose --version

# 检查系统资源
free -h
df -h
```

## 🔧 PostgreSQL 配置

### 1. 创建 PostgreSQL 项目
2. 创建新项目
3. 获取以下信息：
   - Project URL
   - Anon Key
   - Service Role Key

### 2. 初始化数据库
在 PostgreSQL SQL Editor 中依次执行：
```sql
-- 1. 执行 scripts/01-create-tables.sql
-- 2. 执行 scripts/02-seed-data.sql
-- 3. 执行其他脚本文件
```

## 🚀 快速部署

### 1. 配置环境变量

**开发环境：**
```bash
# 复制并编辑开发环境配置
cp .env.local.example .env.local
nano .env.local
```

**生产环境：**
```bash
# 复制并编辑生产环境配置
cp .env.production.example .env.production
nano .env.production
```

### 2. 一键部署

**启动开发环境（推荐用于调试）：**
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 启动开发环境
./deploy.sh dev
```

**部署生产环境：**
```bash
# 部署生产环境
./deploy.sh prod
```

## 🔍 调试指南

### 1. 开发环境调试

**启动开发环境：**
```bash
./deploy.sh dev
# 访问: http://localhost:3001
# 支持热重载，代码修改自动生效
```

**查看实时日志：**
```bash
# 查看应用日志
./deploy.sh logs

# 或直接使用 docker-compose
docker-compose --profile dev logs -f app-dev
```

**进入容器调试：**
```bash
# 进入开发容器
docker-compose --profile dev exec app-dev sh

# 在容器内执行命令
npm run dev
npm run lint
```

### 2. 数据库连接调试

**测试 PostgreSQL 连接：**
```bash
# 测试数据库连接
docker-compose --profile dev exec app-dev node -e "
const { createClient } = require('@supabase/supabase-js');
const client = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
client.from('users').select('count').then(console.log).catch(console.error);
"
```

**检查环境变量：**
```bash
# 查看容器内环境变量
docker-compose --profile dev exec app-dev env | grep SUPABASE
```

### 3. 网络和端口调试

**检查端口占用：**
```bash
# 检查端口状态
netstat -tulpn | grep :300

# 检查容器端口映射
docker-compose ps
```

**测试应用访问：**
```bash
# 健康检查
./deploy.sh health

# 手动测试
curl -f http://localhost:3001  # 开发环境
curl -f http://localhost:3000  # 生产环境
```

## 🔒 环境隔离最佳实践

### 1. 文件隔离
```
.env.local          # 开发环境（不提交到版本控制）
.env.production     # 生产环境（不提交到版本控制）
.env.*.example      # 模板文件（可提交到版本控制）
```

### 2. 容器隔离
- **开发容器**：`app-dev` (端口 3001)
- **生产容器**：`app` (端口 3000)
- **独立网络**：`app-network`

### 3. 数据隔离
建议为开发和生产环境使用不同的 PostgreSQL 项目：
- 开发项目：用于测试和调试
- 生产项目：用于正式环境

## 🛠️ 常用调试命令

```bash
# 查看所有容器状态
docker-compose ps

# 重启开发环境
docker-compose --profile dev restart app-dev

# 查看容器资源使用
docker stats

# 清理所有资源
./deploy.sh cleanup

# 查看特定时间的日志
docker-compose --profile dev logs --since="1h" app-dev

# 实时监控日志
docker-compose --profile dev logs -f --tail=100 app-dev
```

## 🚨 故障排除

### 1. 容器启动失败
```bash
# 检查日志
docker-compose --profile dev logs app-dev

# 常见问题：
# - 端口被占用：修改 docker-compose.yml 中的端口映射
# - 环境变量错误：检查 .env.local 文件
# - 内存不足：释放系统内存或增加 swap
```

### 2. PostgreSQL 连接问题
```bash
# 检查网络连接
docker-compose --profile dev exec app-dev ping your-project.supabase.co

# 验证环境变量
docker-compose --profile dev exec app-dev env | grep SUPABASE

# 测试 API 调用
docker-compose --profile dev exec app-dev curl -H "apikey: $NEXT_PUBLIC_SUPABASE_ANON_KEY" "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/users"
```

### 3. 热重载不工作
```bash
# 检查文件挂载
docker-compose --profile dev exec app-dev ls -la /app

# 重启开发容器
docker-compose --profile dev restart app-dev

# 检查文件权限
ls -la .
```

## 📈 性能优化

### 1. 开发环境优化
```yaml
# 在 docker-compose.yml 中添加资源限制
services:
  app-dev:
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
```

### 2. 生产环境优化
- 启用 Nginx 反向代理
- 配置 CDN（如 Cloudflare）
- 启用 gzip 压缩

## 🔐 安全建议

1. **环境变量安全**
   - 不要将 `.env.*` 文件提交到版本控制
   - 定期轮换 API 密钥
   - 使用强密码

2. **容器安全**
   - 定期更新基础镜像
   - 使用非 root 用户运行应用
   - 限制容器资源使用

3. **网络安全**
   - 配置防火墙规则
   - 使用 HTTPS
   - 启用 PostgreSQL RLS（行级安全）

## 📞 技术支持

如遇问题，请按以下顺序检查：
1. 查看容器日志：`./deploy.sh logs`
2. 检查环境变量配置
3. 验证 PostgreSQL 连接
4. 检查系统资源使用情况
5. 查看防火墙和网络配置
