{"name": "@prisma/get-platform", "version": "5.22.0", "description": "This package is intended for Prisma's internal use", "main": "dist/index.js", "types": "dist/index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "homepage": "https://www.prisma.io", "repository": {"type": "git", "url": "https://github.com/prisma/prisma.git", "directory": "packages/get-platform"}, "bugs": "https://github.com/prisma/prisma/issues", "devDependencies": {"@codspeed/benchmark.js-plugin": "3.1.1", "@swc/core": "1.6.13", "@swc/jest": "0.2.36", "@types/jest": "29.5.12", "@types/node": "18.19.31", "benchmark": "2.1.4", "jest": "29.7.0", "jest-junit": "16.0.0", "typescript": "5.4.5", "escape-string-regexp": "4.0.0", "execa": "5.1.1", "fs-jetpack": "5.1.0", "kleur": "4.1.5", "replace-string": "3.1.0", "strip-ansi": "6.0.1", "tempy": "1.0.1", "terminal-link": "2.1.1", "ts-pattern": "5.2.0"}, "dependencies": {"@prisma/debug": "5.22.0"}, "files": ["README.md", "dist"], "sideEffects": false, "scripts": {"dev": "DEV=true tsx helpers/build.ts", "build": "tsx helpers/build.ts", "test": "jest"}}