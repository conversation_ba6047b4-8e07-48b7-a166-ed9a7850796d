// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  nickname  String?
  phone     String?  @unique
  password  String
  role      Role     @default(STUDENT)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 教师关系
  teacherCourses Course[] @relation("TeacherCourses")

  // 学生关系
  enrollments        Enrollment[]
  studentSessions    Session[]    @relation("StudentSessions")
  studentEvaluations Evaluation[] @relation("StudentEvaluations")

  // 回复关系
  replies Reply[]

  @@map("users")
}

// 用户角色枚举
enum Role {
  ADMIN
  TEACHER
  STUDENT
}

// 课程表
model Course {
  id          String     @id @default(cuid())
  name        String
  description String?
  type        CourseType
  price       Int // 价格（分为单位）
  duration    Int // 课程时长（分钟）
  isActive    Boolean    @default(true)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  // 关系
  teacher     User         @relation("TeacherCourses", fields: [teacherId], references: [id])
  teacherId   String
  enrollments Enrollment[]
  sessions    Session[]
  evaluations Evaluation[]
  materials   Material[]

  @@map("courses")
}

// 日语课程类型枚举
enum CourseType {
  FREE_CONVERSATION // 自由会话
  SPEAKING // 口语课程
  BUSINESS // 商务日语
  WRITING // 写作课程
  GRAMMAR // 语法课程
  JLPT // JLPT N5-1 对策
  GRADUATE_INTERVIEW // 研究生面试对策
  JOB_INTERVIEW // 求职就职面试
  SPEECH_GUIDANCE // 演讲指导
  GAOKAO_JAPANESE // 高考日语
  OTHER // 其他课程
}

// 选课关系表
model Enrollment {
  id         String   @id @default(cuid())
  student    User     @relation(fields: [studentId], references: [id])
  studentId  String
  course     Course   @relation(fields: [courseId], references: [id])
  courseId   String
  enrolledAt DateTime @default(now())
  isActive   Boolean  @default(true)

  @@unique([studentId, courseId])
  @@map("enrollments")
}

// 课程安排表
model Session {
  id          String        @id @default(cuid())
  title       String
  description String?
  sessionDate DateTime
  duration    Int // 课程时长（分钟）
  location    String? // 上课地点
  status      SessionStatus @default(SCHEDULED)
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // 关系
  course    Course @relation(fields: [courseId], references: [id])
  courseId  String
  student   User   @relation("StudentSessions", fields: [studentId], references: [id])
  studentId String

  @@map("sessions")
}

// 课程状态枚举
enum SessionStatus {
  SCHEDULED // 已安排
  COMPLETED // 已完成
  CANCELLED // 已取消
  RESCHEDULED // 已重新安排
}

// 评价表
model Evaluation {
  id        String   @id @default(cuid())
  title     String
  content   String
  rating    Int // 评分 1-5
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  course    Course  @relation(fields: [courseId], references: [id])
  courseId  String
  student   User    @relation("StudentEvaluations", fields: [studentId], references: [id])
  studentId String
  replies   Reply[]

  @@map("evaluations")
}

// 回复表
model Reply {
  id        String   @id @default(cuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  evaluation   Evaluation @relation(fields: [evaluationId], references: [id])
  evaluationId String
  author       User       @relation(fields: [authorId], references: [id])
  authorId     String

  @@map("replies")
}

// 课程材料表
model Material {
  id           String       @id @default(cuid())
  title        String
  description  String?
  filename     String
  originalName String
  mimeType     String
  size         Int
  type         MaterialType @default(DOCUMENT)
  createdAt    DateTime     @default(now())

  // 关系
  course   Course @relation(fields: [courseId], references: [id])
  courseId String

  @@map("materials")
}

// 材料类型枚举
enum MaterialType {
  DOCUMENT // 文档
  IMAGE // 图片
  AUDIO // 音频
  VIDEO // 视频
  OTHER // 其他
}

// 公告表
model Announcement {
  id        String   @id @default(cuid())
  title     String
  content   String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("announcements")
}
