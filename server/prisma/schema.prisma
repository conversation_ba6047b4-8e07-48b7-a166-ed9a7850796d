// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  name      String
  password  String
  role      Role     @default(STUDENT)
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  teacherCourses Course[] @relation("TeacherCourses")
  enrollments    Enrollment[]
  evaluations    Evaluation[]
  replies        Reply[]

  @@map("users")
}

// 用户角色枚举
enum Role {
  TEACHER
  STUDENT
  ADMIN
}

// 课程表
model Course {
  id          String   @id @default(cuid())
  name        String
  description String?
  code        String   @unique
  credits     Int      @default(3)
  semester    String
  year        Int
  teacherId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关系
  teacher     User         @relation("TeacherCourses", fields: [teacherId], references: [id])
  enrollments Enrollment[]
  sessions    Session[]
  evaluations Evaluation[]
  materials   Material[]
  announcements Announcement[]

  @@map("courses")
}

// 选课表
model Enrollment {
  id        String   @id @default(cuid())
  studentId String
  courseId  String
  createdAt DateTime @default(now())

  // 关系
  student User   @relation(fields: [studentId], references: [id])
  course  Course @relation(fields: [courseId], references: [id])

  @@unique([studentId, courseId])
  @@map("enrollments")
}

// 课次表
model Session {
  id          String    @id @default(cuid())
  courseId    String
  title       String
  description String?
  sessionDate DateTime
  duration    Int       @default(90) // 分钟
  location    String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // 关系
  course      Course      @relation(fields: [courseId], references: [id])
  evaluations Evaluation[]

  @@map("sessions")
}

// 评价表
model Evaluation {
  id        String   @id @default(cuid())
  courseId  String
  sessionId String?
  teacherId String
  studentId String
  title     String
  content   String
  rating    Int?     @default(5)
  isPublic  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  course  Course   @relation(fields: [courseId], references: [id])
  session Session? @relation(fields: [sessionId], references: [id])
  teacher User     @relation(fields: [teacherId], references: [id])
  student User     @relation(fields: [studentId], references: [id])
  replies Reply[]

  @@map("evaluations")
}

// 回复表
model Reply {
  id           String   @id @default(cuid())
  evaluationId String
  userId       String
  content      String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关系
  evaluation Evaluation @relation(fields: [evaluationId], references: [id])
  user       User       @relation(fields: [userId], references: [id])

  @@map("replies")
}

// 课程材料表
model Material {
  id          String   @id @default(cuid())
  courseId    String
  title       String
  description String?
  fileName    String
  filePath    String
  fileSize    Int
  mimeType    String
  uploadedBy  String
  createdAt   DateTime @default(now())

  // 关系
  course Course @relation(fields: [courseId], references: [id])

  @@map("materials")
}

// 课程公告表
model Announcement {
  id        String   @id @default(cuid())
  courseId  String
  title     String
  content   String
  priority  Priority @default(NORMAL)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  course Course @relation(fields: [courseId], references: [id])

  @@map("announcements")
}

// 公告优先级枚举
enum Priority {
  LOW
  NORMAL
  HIGH
  URGENT
}
