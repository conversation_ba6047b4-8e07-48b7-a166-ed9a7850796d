// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  phone     String?  @unique // 改为手机号
  password  String
  role      Role     @default(STUDENT)
  avatar    String?
  nickname  String?  // 昵称，替代真实姓名
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  teacherCourses Course[] @relation("TeacherCourses")
  enrollments    Enrollment[]
  studentSessions Session[] @relation("StudentSessions")
  teacherEvaluations Evaluation[] @relation("TeacherEvaluations")
  studentEvaluations Evaluation[] @relation("StudentEvaluations")
  replies        Reply[]

  @@map("users")
}

// 用户角色枚举
enum Role {
  TEACHER
  STUDENT
  ADMIN
}

// 日语课程表
model Course {
  id          String      @id @default(cuid())
  name        String      // 课程名称
  type        CourseType  // 课程类型枚举
  description String?     // 课程描述
  price       Int         // 课程价格（分为单位）
  duration    Int         @default(60) // 课程时长（分钟）
  isActive    Boolean     @default(true) // 是否启用
  teacherId   String      // 教师ID（固定为单一教师）
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // 关系
  teacher     User         @relation("TeacherCourses", fields: [teacherId], references: [id])
  enrollments Enrollment[]
  sessions    Session[]
  evaluations Evaluation[]
  materials   Material[]
  announcements Announcement[]

  @@map("courses")
}

// 日语课程类型枚举
enum CourseType {
  FREE_CONVERSATION      // 自由会话（免费体验）
  SPEAKING              // 口语课程
  BUSINESS              // 商务课程
  WRITING               // 写作课程
  GRAMMAR               // 语法课程
  JLPT                  // JLPT N5-1 对策
  GRADUATE_INTERVIEW    // 研究生面试对策
  JOB_INTERVIEW         // 求职就职面试
  SPEECH_GUIDANCE       // 演讲指导
  GAOKAO_JAPANESE       // 高考日语
  OTHER                 // 其他课程
}

// 选课表
model Enrollment {
  id        String   @id @default(cuid())
  studentId String
  courseId  String
  createdAt DateTime @default(now())

  // 关系
  student User   @relation(fields: [studentId], references: [id])
  course  Course @relation(fields: [courseId], references: [id])

  @@unique([studentId, courseId])
  @@map("enrollments")
}

// 课次表
model Session {
  id          String       @id @default(cuid())
  courseId    String
  studentId   String       // 添加学生ID，支持一对一课程
  title       String
  description String?
  sessionDate DateTime     // 上课时间
  duration    Int          @default(60) // 课程时长（分钟）
  status      SessionStatus @default(SCHEDULED) // 课程状态
  location    String?      // 上课地点（线上/线下）
  notes       String?      // 课程备注
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // 关系
  course      Course       @relation(fields: [courseId], references: [id])
  student     User         @relation("StudentSessions", fields: [studentId], references: [id])
  evaluations Evaluation[]

  @@map("sessions")
}

// 课程状态枚举
enum SessionStatus {
  SCHEDULED   // 已安排
  COMPLETED   // 已完成
  CANCELLED   // 已取消
  RESCHEDULED // 已改期
}

// 评价表
model Evaluation {
  id        String   @id @default(cuid())
  courseId  String
  sessionId String?
  teacherId String
  studentId String?  // 可为空，支持对整个课程的评价
  title     String
  content   String   @db.Text // 支持富文本内容
  rating    Int?     @default(5)
  isPublic  Boolean  @default(false)
  isGroupEvaluation Boolean @default(false) // 是否为群体评价
  attachments String[] // 附件文件路径数组
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  course  Course   @relation(fields: [courseId], references: [id])
  session Session? @relation(fields: [sessionId], references: [id])
  teacher User     @relation("TeacherEvaluations", fields: [teacherId], references: [id])
  student User?    @relation("StudentEvaluations", fields: [studentId], references: [id])
  replies Reply[]

  @@map("evaluations")
}

// 回复表
model Reply {
  id           String   @id @default(cuid())
  evaluationId String
  userId       String
  content      String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关系
  evaluation Evaluation @relation(fields: [evaluationId], references: [id])
  user       User       @relation(fields: [userId], references: [id])

  @@map("replies")
}

// 课程材料表
model Material {
  id          String   @id @default(cuid())
  courseId    String
  title       String
  description String?
  fileName    String
  filePath    String
  fileSize    Int
  mimeType    String
  uploadedBy  String
  createdAt   DateTime @default(now())

  // 关系
  course Course @relation(fields: [courseId], references: [id])

  @@map("materials")
}

// 课程公告表
model Announcement {
  id        String   @id @default(cuid())
  courseId  String
  title     String
  content   String
  priority  Priority @default(NORMAL)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  course Course @relation(fields: [courseId], references: [id])

  @@map("announcements")
}

// 公告优先级枚举
enum Priority {
  LOW
  NORMAL
  HIGH
  URGENT
}

// 注册验证码表
model RegistrationCode {
  id        String   @id @default(cuid())
  code      String   @unique // 验证码
  isUsed    Boolean  @default(false) // 是否已使用
  usedBy    String?  // 使用者用户名
  createdAt DateTime @default(now())
  expiresAt DateTime? // 过期时间（可选）

  @@map("registration_codes")
}
