import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('开始填充数据库种子数据...')

  // 创建管理员用户
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      name: '系统管理员',
      password: adminPassword,
      role: 'ADMIN'
    }
  })

  // 创建教师用户
  const teacherPassword = await bcrypt.hash('123456', 12)
  const teacher1 = await prisma.user.upsert({
    where: { username: 'teacher' },
    update: {},
    create: {
      username: 'teacher',
      email: '<EMAIL>',
      name: '张教授',
      password: teacherPassword,
      role: 'TEACHER'
    }
  })

  const teacher2 = await prisma.user.upsert({
    where: { username: 'teacher2' },
    update: {},
    create: {
      username: 'teacher2',
      email: '<EMAIL>',
      name: '李老师',
      password: teacherPassword,
      role: 'TEACHER'
    }
  })

  // 创建学生用户
  const studentPassword = await bcrypt.hash('123456', 12)
  const students = []
  
  for (let i = 1; i <= 10; i++) {
    const student = await prisma.user.upsert({
      where: { username: `student${i}` },
      update: {},
      create: {
        username: i === 1 ? 'student' : `student${i}`,
        email: `student${i}@example.com`,
        name: `学生${i}号`,
        password: studentPassword,
        role: 'STUDENT'
      }
    })
    students.push(student)
  }

  // 创建课程
  const course1 = await prisma.course.upsert({
    where: { code: 'MATH001' },
    update: {},
    create: {
      name: '高等数学',
      description: '高等数学是理工科学生的基础课程，主要学习微积分、线性代数等内容。',
      code: 'MATH001',
      credits: 4,
      semester: '春季学期',
      year: 2024,
      teacherId: teacher1.id
    }
  })

  const course2 = await prisma.course.upsert({
    where: { code: 'MATH002' },
    update: {},
    create: {
      name: '线性代数',
      description: '线性代数是数学的一个重要分支，研究向量空间和线性映射。',
      code: 'MATH002',
      credits: 3,
      semester: '春季学期',
      year: 2024,
      teacherId: teacher1.id
    }
  })

  const course3 = await prisma.course.upsert({
    where: { code: 'PHYS001' },
    update: {},
    create: {
      name: '大学物理',
      description: '大学物理涵盖力学、热学、电磁学、光学和近代物理等内容。',
      code: 'PHYS001',
      credits: 4,
      semester: '春季学期',
      year: 2024,
      teacherId: teacher2.id
    }
  })

  // 创建选课记录
  const enrollments = []
  for (const student of students.slice(0, 8)) {
    // 前8个学生选择高等数学
    await prisma.enrollment.upsert({
      where: {
        studentId_courseId: {
          studentId: student.id,
          courseId: course1.id
        }
      },
      update: {},
      create: {
        studentId: student.id,
        courseId: course1.id
      }
    })

    // 前6个学生选择线性代数
    if (students.indexOf(student) < 6) {
      await prisma.enrollment.upsert({
        where: {
          studentId_courseId: {
            studentId: student.id,
            courseId: course2.id
          }
        },
        update: {},
        create: {
          studentId: student.id,
          courseId: course2.id
        }
      })
    }

    // 前5个学生选择大学物理
    if (students.indexOf(student) < 5) {
      await prisma.enrollment.upsert({
        where: {
          studentId_courseId: {
            studentId: student.id,
            courseId: course3.id
          }
        },
        update: {},
        create: {
          studentId: student.id,
          courseId: course3.id
        }
      })
    }
  }

  // 创建课程会话
  const session1 = await prisma.session.create({
    data: {
      courseId: course1.id,
      title: '第一章：函数与极限',
      description: '介绍函数的基本概念和极限理论',
      sessionDate: new Date('2024-03-01T09:00:00Z'),
      duration: 90,
      location: '教学楼A101'
    }
  })

  const session2 = await prisma.session.create({
    data: {
      courseId: course1.id,
      title: '第二章：导数与微分',
      description: '学习导数的定义和计算方法',
      sessionDate: new Date('2024-03-08T09:00:00Z'),
      duration: 90,
      location: '教学楼A101'
    }
  })

  // 创建评价
  for (let i = 0; i < 5; i++) {
    await prisma.evaluation.create({
      data: {
        courseId: course1.id,
        sessionId: i < 2 ? (i === 0 ? session1.id : session2.id) : null,
        teacherId: teacher1.id,
        studentId: students[i].id,
        title: `第${i + 1}次课程评价`,
        content: `学生${i + 1}号在本次课程中表现良好，积极参与课堂讨论，作业完成质量较高。建议继续保持学习热情，加强基础知识的巩固。`,
        rating: 4 + (i % 2),
        isPublic: i % 2 === 0
      }
    })
  }

  // 创建课程公告
  await prisma.announcement.create({
    data: {
      courseId: course1.id,
      title: '期中考试通知',
      content: '高等数学期中考试将于下周三进行，请同学们做好复习准备。考试范围为第1-4章内容。',
      priority: 'HIGH'
    }
  })

  await prisma.announcement.create({
    data: {
      courseId: course1.id,
      title: '作业提交提醒',
      content: '请同学们按时提交本周作业，截止时间为周日晚上11:59。',
      priority: 'NORMAL'
    }
  })

  console.log('数据库种子数据填充完成！')
  console.log('创建的用户：')
  console.log('- 管理员: admin / admin123')
  console.log('- 教师: teacher / 123456')
  console.log('- 学生: student / 123456')
  console.log('- 其他学生: student2-student10 / 123456')
}

main()
  .catch((e) => {
    console.error('填充种子数据时出错:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
