import { PrismaClient } from '@prisma/client'
import dotenv from 'dotenv'

dotenv.config()

const prisma = new PrismaClient()

async function testDatabase() {
  try {
    console.log('🔍 测试数据库连接...')
    
    // 测试连接
    await prisma.$connect()
    console.log('✅ 数据库连接成功')
    
    // 查询所有表
    const users = await prisma.user.findMany()
    const courses = await prisma.course.findMany()
    const enrollments = await prisma.enrollment.findMany()
    const sessions = await prisma.session.findMany()
    const evaluations = await prisma.evaluation.findMany()
    const registrationCodes = await prisma.registrationCode.findMany()
    
    console.log('\n📊 数据库状态：')
    console.log(`- 用户数量: ${users.length}`)
    console.log(`- 课程数量: ${courses.length}`)
    console.log(`- 选课记录: ${enrollments.length}`)
    console.log(`- 课程安排: ${sessions.length}`)
    console.log(`- 评价数量: ${evaluations.length}`)
    console.log(`- 验证码数量: ${registrationCodes.length}`)
    
    if (users.length > 0) {
      console.log('\n👥 用户列表：')
      users.forEach((user, index) => {
        console.log(`${index + 1}. ${user.username} (${user.role}) - ${user.nickname || '无昵称'}`)
      })
    }
    
    if (courses.length > 0) {
      console.log('\n📚 课程列表：')
      courses.forEach((course, index) => {
        console.log(`${index + 1}. ${course.name} (${course.type}) - ¥${course.price/100}`)
      })
    }
    
    if (registrationCodes.length > 0) {
      console.log('\n🔑 验证码列表：')
      registrationCodes.forEach((code, index) => {
        console.log(`${index + 1}. ${code.code} - ${code.isUsed ? '已使用' : '未使用'}`)
      })
    }
    
  } catch (error) {
    console.error('❌ 数据库测试失败:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase()
