import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import dotenv from 'dotenv'
import rateLimit from 'express-rate-limit'
import path from 'path'
import { fileURLToPath } from 'url'

// 导入路由
import authRoutes from './routes/auth.js'
import courseRoutes from './routes/courses.js'
import evaluationRoutes from './routes/evaluations.js'
import userRoutes from './routes/users.js'
import uploadRoutes from './routes/upload.js'

// 导入中间件
import { errorHandler } from './middleware/errorHandler.js'
import { notFound } from './middleware/notFound.js'

// 配置环境变量
dotenv.config()

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app = express()
const PORT = process.env.PORT || 3000

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  }
})

// 中间件
app.use(helmet())
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:5173',
  credentials: true
}))
app.use(morgan('combined'))
app.use(limiter)
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')))

// API路由
app.use('/api/auth', authRoutes)
app.use('/api/courses', courseRoutes)
app.use('/api/evaluations', evaluationRoutes)
app.use('/api/users', userRoutes)
app.use('/api/upload', uploadRoutes)

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// API文档
app.get('/api', (req, res) => {
  res.json({
    name: '课程评价系统 API',
    version: '1.0.0',
    description: '基于Node.js和PostgreSQL的课程评价系统后端API',
    endpoints: {
      auth: '/api/auth',
      courses: '/api/courses',
      evaluations: '/api/evaluations',
      users: '/api/users',
      upload: '/api/upload',
      health: '/api/health'
    }
  })
})

// 错误处理中间件
app.use(notFound)
app.use(errorHandler)

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 服务器运行在 http://localhost:${PORT}`)
  console.log(`📚 API文档: http://localhost:${PORT}/api`)
  console.log(`🏥 健康检查: http://localhost:${PORT}/api/health`)
})

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...')
  process.exit(0)
})

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...')
  process.exit(0)
})
