import express from 'express'
import { body, validationResult } from 'express-validator'
import { PrismaClient } from '@prisma/client'
import { authenticateToken, requireTeacher } from '../middleware/auth.js'

const router = express.Router()
const prisma = new PrismaClient()

// 获取所有课程（需要认证）
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search } = req.query
    const skip = (page - 1) * limit

    const where = {}
    
    // 如果是学生，只显示已选课程
    if (req.user.role === 'STUDENT') {
      where.enrollments = {
        some: {
          studentId: req.user.id
        }
      }
    }
    
    // 如果是教师，只显示自己的课程
    if (req.user.role === 'TEACHER') {
      where.teacherId = req.user.id
    }

    // 搜索功能
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ]
    }

    const [courses, total] = await Promise.all([
      prisma.course.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          teacher: {
            select: {
              id: true,
              nickname: true,
              username: true
            }
          },
          _count: {
            select: {
              enrollments: true,
              evaluations: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.course.count({ where })
    ])

    res.json({
      success: true,
      data: courses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('获取课程列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 创建课程（仅教师）
router.post('/', [
  authenticateToken,
  requireTeacher,
  body('name').notEmpty().withMessage('课程名称不能为空'),
  body('type').isIn([
    'FREE_CONVERSATION', 'SPEAKING', 'BUSINESS', 'WRITING', 'GRAMMAR',
    'JLPT', 'GRADUATE_INTERVIEW', 'JOB_INTERVIEW', 'SPEECH_GUIDANCE',
    'GAOKAO_JAPANESE', 'OTHER'
  ]).withMessage('课程类型无效'),
  body('price').isInt({ min: 0 }).withMessage('价格必须是非负整数'),
  body('duration').isInt({ min: 15, max: 180 }).withMessage('课程时长必须在15-180分钟之间')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    const { name, type, description, price, duration } = req.body

    const course = await prisma.course.create({
      data: {
        name,
        type,
        description,
        price: parseInt(price),
        duration: parseInt(duration),
        teacherId: req.user.id
      },
      include: {
        teacher: {
          select: {
            id: true,
            nickname: true,
            username: true
          }
        }
      }
    })

    res.status(201).json({
      success: true,
      message: '课程创建成功',
      data: course
    })
  } catch (error) {
    console.error('创建课程错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取单个课程详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params

    const course = await prisma.course.findUnique({
      where: { id },
      include: {
        teacher: {
          select: {
            id: true,
            nickname: true,
            username: true,
            phone: true
          }
        },
        enrollments: {
          include: {
            student: {
              select: {
                id: true,
                nickname: true,
                username: true
              }
            }
          }
        },
        sessions: {
          orderBy: {
            sessionDate: 'asc'
          }
        },
        announcements: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        },
        materials: {
          orderBy: {
            createdAt: 'desc'
          }
        },
        _count: {
          select: {
            enrollments: true,
            evaluations: true,
            sessions: true
          }
        }
      }
    })

    if (!course) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      })
    }

    // 检查权限
    const isTeacher = req.user.role === 'TEACHER' && course.teacherId === req.user.id
    const isEnrolled = course.enrollments.some(e => e.studentId === req.user.id)
    
    if (!isTeacher && !isEnrolled && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: '无权访问此课程'
      })
    }

    res.json({
      success: true,
      data: course
    })
  } catch (error) {
    console.error('获取课程详情错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 学生选课
router.post('/:id/enroll', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params

    if (req.user.role !== 'STUDENT') {
      return res.status(403).json({
        success: false,
        message: '只有学生可以选课'
      })
    }

    // 检查课程是否存在
    const course = await prisma.course.findUnique({
      where: { id }
    })

    if (!course) {
      return res.status(404).json({
        success: false,
        message: '课程不存在'
      })
    }

    // 检查是否已选课
    const existingEnrollment = await prisma.enrollment.findUnique({
      where: {
        studentId_courseId: {
          studentId: req.user.id,
          courseId: id
        }
      }
    })

    if (existingEnrollment) {
      return res.status(400).json({
        success: false,
        message: '您已选择此课程'
      })
    }

    // 创建选课记录
    const enrollment = await prisma.enrollment.create({
      data: {
        studentId: req.user.id,
        courseId: id
      }
    })

    res.status(201).json({
      success: true,
      message: '选课成功',
      data: enrollment
    })
  } catch (error) {
    console.error('选课错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

export default router
