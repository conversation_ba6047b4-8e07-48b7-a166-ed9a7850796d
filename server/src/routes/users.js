import express from 'express'
import { body, validationResult } from 'express-validator'
import { PrismaClient } from '@prisma/client'
import { authenticateToken, requireTeacher<PERSON>rAdmin } from '../middleware/auth.js'

const router = express.Router()
const prisma = new PrismaClient()

// 获取用户列表（仅教师和管理员）
router.get('/', [authenticateToken, requireTeacherOrAdmin], async (req, res) => {
  try {
    const { page = 1, limit = 10, role, search } = req.query
    const skip = (page - 1) * limit

    const where = {}

    // 角色过滤
    if (role && ['TEACHER', 'STUDENT', 'ADMIN'].includes(role.toUpperCase())) {
      where.role = role.toUpperCase()
    }

    // 搜索功能
    if (search) {
      where.OR = [
        { username: { contains: search, mode: 'insensitive' } },
        { nickname: { contains: search, mode: 'insensitive' } },
        { phone: { contains: search, mode: 'insensitive' } }
      ]
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        select: {
          id: true,
          username: true,
          phone: true,
          nickname: true,
          role: true,
          avatar: true,
          createdAt: true,
          _count: {
            select: {
              teacherCourses: true,
              enrollments: true,
              studentEvaluations: true,
              teacherEvaluations: true,
              replies: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.user.count({ where })
    ])

    res.json({
      success: true,
      data: users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('获取用户列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取学生列表（用于教师选择学生）
router.get('/students', authenticateToken, async (req, res) => {
  try {
    const { courseId, search } = req.query

    let where = {
      role: 'STUDENT'
    }

    // 如果指定了课程ID，只返回选了该课程的学生
    if (courseId) {
      where.enrollments = {
        some: {
          courseId
        }
      }
    }

    // 搜索功能
    if (search) {
      where.OR = [
        { username: { contains: search, mode: 'insensitive' } },
        { nickname: { contains: search, mode: 'insensitive' } }
      ]
    }

    const students = await prisma.user.findMany({
      where,
      select: {
        id: true,
        username: true,
        nickname: true,
        phone: true
      },
      orderBy: {
        nickname: 'asc'
      }
    })

    res.json({
      success: true,
      data: students
    })
  } catch (error) {
    console.error('获取学生列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取单个用户详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params

    // 检查权限：只能查看自己的信息，或者教师/管理员可以查看其他用户
    if (req.user.id !== id && !['TEACHER', 'ADMIN'].includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '无权查看此用户信息'
      })
    }

    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        phone: true,
        nickname: true,
        role: true,
        avatar: true,
        createdAt: true,
        teacherCourses: {
          select: {
            id: true,
            name: true,
            type: true,
            _count: {
              select: {
                enrollments: true
              }
            }
          }
        },
        enrollments: {
          include: {
            course: {
              select: {
                id: true,
                name: true,
                type: true,
                teacher: {
                  select: {
                    nickname: true
                  }
                }
              }
            }
          }
        },
        _count: {
          select: {
            teacherCourses: true,
            enrollments: true,
            studentEvaluations: true,
            teacherEvaluations: true,
            replies: true
          }
        }
      }
    })

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      })
    }

    res.json({
      success: true,
      data: user
    })
  } catch (error) {
    console.error('获取用户详情错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 更新用户信息
router.put('/:id', [
  authenticateToken,
  body('nickname').optional().isLength({ min: 2 }).withMessage('昵称至少2个字符'),
  body('phone').optional().isMobilePhone('zh-CN').withMessage('请输入有效的手机号')
], async (req, res) => {
  try {
    const { id } = req.params
    const { nickname, phone } = req.body

    // 检查权限：只能更新自己的信息，或者管理员可以更新其他用户
    if (req.user.id !== id && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: '无权修改此用户信息'
      })
    }

    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      })
    }

    // 如果更新手机号，检查是否重复
    if (phone && phone !== existingUser.phone) {
      const phoneExists = await prisma.user.findUnique({
        where: { phone }
      })

      if (phoneExists) {
        return res.status(400).json({
          success: false,
          message: '手机号已被使用'
        })
      }
    }

    const updateData = {}
    if (nickname) updateData.nickname = nickname
    if (phone) updateData.phone = phone

    const updatedUser = await prisma.user.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        username: true,
        phone: true,
        nickname: true,
        role: true,
        avatar: true,
        updatedAt: true
      }
    })

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: updatedUser
    })
  } catch (error) {
    console.error('更新用户信息错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

export default router
