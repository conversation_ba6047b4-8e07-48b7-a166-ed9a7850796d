import express from 'express'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { body, validationResult } from 'express-validator'
import { PrismaClient } from '@prisma/client'

const router = express.Router()
const prisma = new PrismaClient()

// 生成JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  })
}

// 注册
router.post('/register', [
  body('username')
    .isLength({ min: 3 })
    .withMessage('用户名至少3个字符')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('phone')
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号'),
  body('nickname')
    .optional()
    .isLength({ min: 1, max: 20 })
    .withMessage('昵称长度应在1-20个字符之间'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码至少6个字符'),
  body('registrationCode')
    .notEmpty()
    .withMessage('注册验证码不能为空')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    const { username, phone, nickname, password, registrationCode } = req.body

    // 验证注册验证码
    const validCode = await prisma.registrationCode.findFirst({
      where: {
        code: registrationCode,
        isUsed: false
      }
    })

    if (!validCode) {
      return res.status(400).json({
        success: false,
        message: '注册验证码无效或已被使用'
      })
    }

    // 检查用户是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { phone }
        ]
      }
    })

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名或手机号已存在'
      })
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, 12)

    // 创建用户（默认为学生角色）
    const user = await prisma.user.create({
      data: {
        username,
        phone,
        nickname,
        password: hashedPassword,
        role: 'STUDENT' // 只允许注册学生账号
      },
      select: {
        id: true,
        username: true,
        phone: true,
        nickname: true,
        role: true,
        createdAt: true
      }
    })

    // 标记验证码为已使用
    await prisma.registrationCode.update({
      where: { id: validCode.id },
      data: {
        isUsed: true,
        usedBy: username
      }
    })

    // 生成token
    const token = generateToken(user.id)

    res.status(201).json({
      success: true,
      message: '注册成功',
      user,
      token
    })
  } catch (error) {
    console.error('注册错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 登录
router.post('/login', [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    const { username, password } = req.body

    // 查找用户（自动识别角色）
    const user = await prisma.user.findFirst({
      where: {
        username
      }
    })

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      })
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      })
    }

    // 生成token
    const token = generateToken(user.id)

    // 返回用户信息（不包含密码）
    const { password: _, ...userWithoutPassword } = user

    res.json({
      success: true,
      message: '登录成功',
      user: userWithoutPassword,
      token
    })
  } catch (error) {
    console.error('登录错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取当前用户信息
router.get('/me', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '')
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证token'
      })
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET)
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        phone: true,
        nickname: true,
        role: true,
        avatar: true,
        createdAt: true
      }
    })

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      })
    }

    res.json({
      success: true,
      user
    })
  } catch (error) {
    console.error('获取用户信息错误:', error)
    res.status(401).json({
      success: false,
      message: 'token无效'
    })
  }
})

export default router
