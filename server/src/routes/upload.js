import express from 'express'
import multer from 'multer'
import path from 'path'
import { fileURLToPath } from 'url'
import fs from 'fs'
import { PrismaClient } from '@prisma/client'
import { authenticateToken } from '../middleware/auth.js'

const router = express.Router()
const prisma = new PrismaClient()

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 确保上传目录存在
const uploadDir = path.join(__dirname, '../../uploads')
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true })
}

// 配置multer
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir)
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    const ext = path.extname(file.originalname)
    cb(null, file.fieldname + '-' + uniqueSuffix + ext)
  }
})

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'application/zip',
    'application/x-rar-compressed'
  ]

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true)
  } else {
    cb(new Error('不支持的文件类型'), false)
  }
}

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB
  }
})

// 上传课程材料
router.post('/material', [
  authenticateToken,
  upload.single('file')
], async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的文件'
      })
    }

    const { courseId, title, description } = req.body

    if (!courseId) {
      return res.status(400).json({
        success: false,
        message: '课程ID不能为空'
      })
    }

    // 验证课程是否存在且用户有权限
    const course = await prisma.course.findFirst({
      where: {
        id: courseId,
        teacherId: req.user.id
      }
    })

    if (!course) {
      // 删除已上传的文件
      fs.unlinkSync(req.file.path)
      return res.status(403).json({
        success: false,
        message: '无权为此课程上传材料'
      })
    }

    // 保存文件信息到数据库
    const material = await prisma.material.create({
      data: {
        courseId,
        title: title || req.file.originalname,
        description: description || '',
        fileName: req.file.originalname,
        filePath: req.file.path,
        fileSize: req.file.size,
        mimeType: req.file.mimetype,
        uploadedBy: req.user.id
      }
    })

    res.status(201).json({
      success: true,
      message: '文件上传成功',
      data: {
        id: material.id,
        title: material.title,
        fileName: material.fileName,
        fileSize: material.fileSize,
        mimeType: material.mimeType,
        createdAt: material.createdAt
      }
    })
  } catch (error) {
    // 如果出错，删除已上传的文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path)
    }

    console.error('文件上传错误:', error)
    res.status(500).json({
      success: false,
      message: error.message || '文件上传失败'
    })
  }
})

// 上传头像
router.post('/avatar', [
  authenticateToken,
  upload.single('avatar')
], async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的头像'
      })
    }

    // 检查是否为图片
    if (!req.file.mimetype.startsWith('image/')) {
      fs.unlinkSync(req.file.path)
      return res.status(400).json({
        success: false,
        message: '头像必须是图片文件'
      })
    }

    // 更新用户头像路径
    const avatarUrl = `/uploads/${req.file.filename}`
    
    await prisma.user.update({
      where: { id: req.user.id },
      data: { avatar: avatarUrl }
    })

    res.json({
      success: true,
      message: '头像上传成功',
      data: {
        avatarUrl
      }
    })
  } catch (error) {
    // 如果出错，删除已上传的文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path)
    }

    console.error('头像上传错误:', error)
    res.status(500).json({
      success: false,
      message: '头像上传失败'
    })
  }
})

// 下载文件
router.get('/download/:materialId', authenticateToken, async (req, res) => {
  try {
    const { materialId } = req.params

    const material = await prisma.material.findUnique({
      where: { id: materialId },
      include: {
        course: {
          include: {
            enrollments: true
          }
        }
      }
    })

    if (!material) {
      return res.status(404).json({
        success: false,
        message: '文件不存在'
      })
    }

    // 检查权限
    const isTeacher = material.course.teacherId === req.user.id
    const isEnrolled = material.course.enrollments.some(e => e.studentId === req.user.id)

    if (!isTeacher && !isEnrolled && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: '无权下载此文件'
      })
    }

    // 检查文件是否存在
    if (!fs.existsSync(material.filePath)) {
      return res.status(404).json({
        success: false,
        message: '文件已被删除'
      })
    }

    // 设置响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(material.fileName)}"`)
    res.setHeader('Content-Type', material.mimeType)

    // 发送文件
    res.sendFile(path.resolve(material.filePath))
  } catch (error) {
    console.error('文件下载错误:', error)
    res.status(500).json({
      success: false,
      message: '文件下载失败'
    })
  }
})

export default router
