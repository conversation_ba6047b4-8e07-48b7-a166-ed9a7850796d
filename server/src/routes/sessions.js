import express from 'express'
import { body, validationResult } from 'express-validator'
import { PrismaClient } from '@prisma/client'
import { authenticateToken, requireTeacher } from '../middleware/auth.js'

const router = express.Router()
const prisma = new PrismaClient()

// 获取课程安排列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, courseId, studentId, status } = req.query
    const skip = (page - 1) * limit

    const where = {}
    
    // 如果是教师，只显示自己的课程安排
    if (req.user.role === 'TEACHER') {
      where.course = {
        teacherId: req.user.id
      }
    }
    
    // 如果是学生，只显示自己的课程安排
    if (req.user.role === 'STUDENT') {
      where.studentId = req.user.id
    }

    // 按课程过滤
    if (courseId) {
      where.courseId = courseId
    }

    // 按学生过滤
    if (studentId) {
      where.studentId = studentId
    }

    // 按状态过滤
    if (status) {
      where.status = status
    }

    const [sessions, total] = await Promise.all([
      prisma.session.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          course: {
            select: {
              id: true,
              name: true,
              type: true,
              price: true,
              duration: true,
              teacher: {
                select: {
                  id: true,
                  username: true,
                  nickname: true
                }
              }
            }
          },
          student: {
            select: {
              id: true,
              username: true,
              nickname: true
            }
          }
        },
        orderBy: {
          sessionDate: 'desc'
        }
      }),
      prisma.session.count({ where })
    ])

    res.json({
      success: true,
      data: sessions,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('获取课程安排列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 创建课程安排（仅教师）
router.post('/', [
  authenticateToken,
  requireTeacher,
  body('courseId').notEmpty().withMessage('课程ID不能为空'),
  body('studentId').notEmpty().withMessage('学生ID不能为空'),
  body('title').notEmpty().withMessage('课程标题不能为空'),
  body('sessionDate').isISO8601().withMessage('请提供有效的上课时间'),
  body('duration').isInt({ min: 15, max: 180 }).withMessage('课程时长必须在15-180分钟之间'),
  body('status').optional().isIn(['SCHEDULED', 'COMPLETED', 'CANCELLED', 'RESCHEDULED']).withMessage('课程状态无效')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    const { courseId, studentId, title, description, sessionDate, duration, location, status } = req.body

    // 验证课程是否属于当前教师
    const course = await prisma.course.findFirst({
      where: {
        id: courseId,
        teacherId: req.user.id
      }
    })

    if (!course) {
      return res.status(403).json({
        success: false,
        message: '无权为此课程创建安排'
      })
    }

    // 验证学生是否选了这门课
    const enrollment = await prisma.enrollment.findUnique({
      where: {
        studentId_courseId: {
          studentId,
          courseId
        }
      }
    })

    if (!enrollment) {
      return res.status(400).json({
        success: false,
        message: '该学生未选择此课程'
      })
    }

    const session = await prisma.session.create({
      data: {
        courseId,
        studentId,
        title,
        description: description || '',
        sessionDate: new Date(sessionDate),
        duration: parseInt(duration),
        location: location || '',
        status: status || 'SCHEDULED'
      },
      include: {
        course: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        student: {
          select: {
            id: true,
            username: true,
            nickname: true
          }
        }
      }
    })

    res.status(201).json({
      success: true,
      message: '课程安排创建成功',
      data: session
    })
  } catch (error) {
    console.error('创建课程安排错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取单个课程安排详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params

    const session = await prisma.session.findUnique({
      where: { id },
      include: {
        course: {
          select: {
            id: true,
            name: true,
            type: true,
            teacherId: true
          }
        },
        student: {
          select: {
            id: true,
            username: true,
            nickname: true
          }
        }
      }
    })

    if (!session) {
      return res.status(404).json({
        success: false,
        message: '课程安排不存在'
      })
    }

    // 检查权限
    const canView = 
      session.course.teacherId === req.user.id || 
      session.studentId === req.user.id ||
      req.user.role === 'ADMIN'

    if (!canView) {
      return res.status(403).json({
        success: false,
        message: '无权查看此课程安排'
      })
    }

    res.json({
      success: true,
      data: session
    })
  } catch (error) {
    console.error('获取课程安排详情错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 更新课程安排（仅教师）
router.put('/:id', [
  authenticateToken,
  requireTeacher,
  body('title').optional().notEmpty().withMessage('课程标题不能为空'),
  body('sessionDate').optional().isISO8601().withMessage('请提供有效的上课时间'),
  body('duration').optional().isInt({ min: 15, max: 180 }).withMessage('课程时长必须在15-180分钟之间'),
  body('status').optional().isIn(['SCHEDULED', 'COMPLETED', 'CANCELLED', 'RESCHEDULED']).withMessage('课程状态无效')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    const { id } = req.params
    const updateData = req.body

    // 检查课程安排是否存在且属于当前教师
    const existingSession = await prisma.session.findFirst({
      where: {
        id,
        course: {
          teacherId: req.user.id
        }
      }
    })

    if (!existingSession) {
      return res.status(404).json({
        success: false,
        message: '课程安排不存在或无权修改'
      })
    }

    // 处理日期格式
    if (updateData.sessionDate) {
      updateData.sessionDate = new Date(updateData.sessionDate)
    }

    // 处理数字类型
    if (updateData.duration) {
      updateData.duration = parseInt(updateData.duration)
    }

    const session = await prisma.session.update({
      where: { id },
      data: updateData,
      include: {
        course: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        student: {
          select: {
            id: true,
            username: true,
            nickname: true
          }
        }
      }
    })

    res.json({
      success: true,
      message: '课程安排更新成功',
      data: session
    })
  } catch (error) {
    console.error('更新课程安排错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 删除课程安排（仅教师）
router.delete('/:id', [authenticateToken, requireTeacher], async (req, res) => {
  try {
    const { id } = req.params

    // 检查课程安排是否存在且属于当前教师
    const existingSession = await prisma.session.findFirst({
      where: {
        id,
        course: {
          teacherId: req.user.id
        }
      }
    })

    if (!existingSession) {
      return res.status(404).json({
        success: false,
        message: '课程安排不存在或无权删除'
      })
    }

    await prisma.session.delete({
      where: { id }
    })

    res.json({
      success: true,
      message: '课程安排删除成功'
    })
  } catch (error) {
    console.error('删除课程安排错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

export default router
