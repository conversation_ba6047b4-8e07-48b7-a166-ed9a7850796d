import express from 'express'
import { body, validationResult } from 'express-validator'
import { PrismaClient } from '@prisma/client'
import { authenticateToken, requireTeacher } from '../middleware/auth.js'

const router = express.Router()
const prisma = new PrismaClient()

// 获取评价列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, courseId, studentId } = req.query
    const skip = (page - 1) * limit

    const where = {}

    // 根据用户角色过滤数据
    if (req.user.role === 'TEACHER') {
      where.teacherId = req.user.id
    } else if (req.user.role === 'STUDENT') {
      where.studentId = req.user.id
    }

    // 按课程过滤
    if (courseId) {
      where.courseId = courseId
    }

    // 按学生过滤（仅教师可用）
    if (studentId && req.user.role === 'TEACHER') {
      where.studentId = studentId
    }

    const [evaluations, total] = await Promise.all([
      prisma.evaluation.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          course: {
            select: {
              id: true,
              name: true,
              type: true
            }
          },
          session: {
            select: {
              id: true,
              title: true,
              sessionDate: true
            }
          },
          student: {
            select: {
              id: true,
              nickname: true,
              username: true
            }
          },
          teacher: {
            select: {
              id: true,
              nickname: true,
              username: true
            }
          },
          replies: {
            include: {
              user: {
                select: {
                  id: true,
                  nickname: true,
                  role: true
                }
              }
            },
            orderBy: {
              createdAt: 'asc'
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.evaluation.count({ where })
    ])

    res.json({
      success: true,
      data: evaluations,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('获取评价列表错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 创建评价（仅教师）
router.post('/', [
  authenticateToken,
  requireTeacher,
  body('courseId').notEmpty().withMessage('课程ID不能为空'),
  body('studentId').notEmpty().withMessage('学生ID不能为空'),
  body('title').notEmpty().withMessage('评价标题不能为空'),
  body('content').notEmpty().withMessage('评价内容不能为空'),
  body('rating').optional().isInt({ min: 1, max: 5 }).withMessage('评分必须是1-5之间的整数')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    const { courseId, sessionId, studentId, title, content, rating, isPublic } = req.body

    // 验证课程是否属于当前教师
    const course = await prisma.course.findFirst({
      where: {
        id: courseId,
        teacherId: req.user.id
      }
    })

    if (!course) {
      return res.status(403).json({
        success: false,
        message: '无权为此课程创建评价'
      })
    }

    // 验证学生是否选了这门课
    const enrollment = await prisma.enrollment.findUnique({
      where: {
        studentId_courseId: {
          studentId,
          courseId
        }
      }
    })

    if (!enrollment) {
      return res.status(400).json({
        success: false,
        message: '该学生未选择此课程'
      })
    }

    const evaluation = await prisma.evaluation.create({
      data: {
        courseId,
        sessionId: sessionId || null,
        teacherId: req.user.id,
        studentId,
        title,
        content,
        rating: rating ? parseInt(rating) : 5,
        isPublic: isPublic || false
      },
      include: {
        course: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        session: {
          select: {
            id: true,
            title: true,
            sessionDate: true
          }
        },
        student: {
          select: {
            id: true,
            nickname: true,
            username: true
          }
        }
      }
    })

    res.status(201).json({
      success: true,
      message: '评价创建成功',
      data: evaluation
    })
  } catch (error) {
    console.error('创建评价错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 获取单个评价详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params

    const evaluation = await prisma.evaluation.findUnique({
      where: { id },
      include: {
        course: {
          select: {
            id: true,
            name: true,
            type: true
          }
        },
        session: {
          select: {
            id: true,
            title: true,
            sessionDate: true
          }
        },
        student: {
          select: {
            id: true,
            nickname: true,
            username: true
          }
        },
        teacher: {
          select: {
            id: true,
            nickname: true,
            username: true
          }
        },
        replies: {
          include: {
            user: {
              select: {
                id: true,
                nickname: true,
                role: true
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    })

    if (!evaluation) {
      return res.status(404).json({
        success: false,
        message: '评价不存在'
      })
    }

    // 检查权限
    const canView = 
      evaluation.teacherId === req.user.id || 
      evaluation.studentId === req.user.id ||
      req.user.role === 'ADMIN'

    if (!canView) {
      return res.status(403).json({
        success: false,
        message: '无权查看此评价'
      })
    }

    res.json({
      success: true,
      data: evaluation
    })
  } catch (error) {
    console.error('获取评价详情错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

// 回复评价
router.post('/:id/reply', [
  authenticateToken,
  body('content').notEmpty().withMessage('回复内容不能为空')
], async (req, res) => {
  try {
    const errors = validationResult(req)
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      })
    }

    const { id } = req.params
    const { content } = req.body

    // 检查评价是否存在
    const evaluation = await prisma.evaluation.findUnique({
      where: { id }
    })

    if (!evaluation) {
      return res.status(404).json({
        success: false,
        message: '评价不存在'
      })
    }

    // 检查权限（只有评价的教师和学生可以回复）
    const canReply = 
      evaluation.teacherId === req.user.id || 
      evaluation.studentId === req.user.id

    if (!canReply) {
      return res.status(403).json({
        success: false,
        message: '无权回复此评价'
      })
    }

    const reply = await prisma.reply.create({
      data: {
        evaluationId: id,
        userId: req.user.id,
        content
      },
      include: {
        user: {
          select: {
            id: true,
            nickname: true,
            role: true
          }
        }
      }
    })

    res.status(201).json({
      success: true,
      message: '回复成功',
      data: reply
    })
  } catch (error) {
    console.error('回复评价错误:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    })
  }
})

export default router
