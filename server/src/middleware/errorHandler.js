// 全局错误处理中间件
export const errorHandler = (err, req, res, next) => {
  let error = { ...err }
  error.message = err.message

  console.error('错误详情:', err)

  // Prisma错误处理
  if (err.code === 'P2002') {
    const message = '数据重复，该记录已存在'
    error = { message, statusCode: 400 }
  }

  if (err.code === 'P2025') {
    const message = '记录未找到'
    error = { message, statusCode: 404 }
  }

  // JWT错误处理
  if (err.name === 'JsonWebTokenError') {
    const message = 'Token无效'
    error = { message, statusCode: 401 }
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token已过期'
    error = { message, statusCode: 401 }
  }

  // 验证错误
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ')
    error = { message, statusCode: 400 }
  }

  // 文件上传错误
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = '文件大小超出限制'
    error = { message, statusCode: 400 }
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = '意外的文件字段'
    error = { message, statusCode: 400 }
  }

  res.status(error.statusCode || 500).json({
    success: false,
    message: error.message || '服务器内部错误',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  })
}
