import jwt from 'jsonwebtoken'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 验证JWT token
export const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问被拒绝，未提供token'
      })
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET)
    
    // 从数据库获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        role: true,
        avatar: true
      }
    })

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      })
    }

    req.user = user
    next()
  } catch (error) {
    console.error('Token验证错误:', error)
    return res.status(403).json({
      success: false,
      message: 'Token无效'
    })
  }
}

// 验证用户角色
export const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证的用户'
      })
    }

    const userRoles = Array.isArray(roles) ? roles : [roles]
    
    if (!userRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      })
    }

    next()
  }
}

// 验证是否为教师
export const requireTeacher = requireRole('TEACHER')

// 验证是否为学生
export const requireStudent = requireRole('STUDENT')

// 验证是否为管理员
export const requireAdmin = requireRole('ADMIN')

// 验证是否为教师或管理员
export const requireTeacherOrAdmin = requireRole(['TEACHER', 'ADMIN'])
