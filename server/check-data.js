import { PrismaClient } from '@prisma/client'
import dotenv from 'dotenv'

dotenv.config()

const prisma = new PrismaClient()

async function checkData() {
  try {
    const courses = await prisma.course.findMany()
    const users = await prisma.user.findMany()
    const enrollments = await prisma.enrollment.findMany()
    
    console.log('📊 数据库状态检查：')
    console.log(`- 课程数量: ${courses.length}`)
    console.log(`- 用户数量: ${users.length}`)
    console.log(`- 选课记录: ${enrollments.length}`)
    
    if (courses.length > 0) {
      console.log('\n📚 课程列表：')
      courses.forEach((course, index) => {
        console.log(`${index + 1}. ${course.name} (${course.type}) - ${course.price/100}元`)
      })
    }
    
    if (users.length > 0) {
      console.log('\n👥 用户列表：')
      users.forEach((user, index) => {
        console.log(`${index + 1}. ${user.username} (${user.role}) - ${user.nickname || '无昵称'}`)
      })
    }
    
  } catch (error) {
    console.error('检查数据时出错:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkData()
