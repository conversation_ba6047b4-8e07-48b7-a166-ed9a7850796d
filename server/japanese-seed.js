import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import dotenv from 'dotenv'

dotenv.config()

const prisma = new PrismaClient()

async function main() {
  console.log('🎌 开始填充日语教学管理系统数据...')

  try {
    // 创建日语教师用户（系统唯一教师）
    const teacherPassword = await bcrypt.hash('teacher123', 12)
    const teacher = await prisma.user.upsert({
      where: { username: 'teacher' },
      update: {},
      create: {
        username: 'teacher',
        phone: '13800138000',
        password: teacherPassword,
        role: 'TEACHER',
        nickname: '田中老师'
      }
    })
    console.log('✅ 日语教师用户创建成功')

    // 创建学生用户
    const studentPassword = await bcrypt.hash('123456', 12)
    const students = []
    
    const studentData = [
      { username: 'student1', phone: '13800138001', nickname: '小明' },
      { username: 'student2', phone: '13800138002', nickname: '小红' },
      { username: 'student3', phone: '13800138003', nickname: '小李' },
      { username: 'student4', phone: '13800138004', nickname: '小王' },
      { username: 'student5', phone: '13800138005', nickname: '小张' }
    ]

    for (const data of studentData) {
      const student = await prisma.user.upsert({
        where: { username: data.username },
        update: {},
        create: {
          username: data.username,
          phone: data.phone,
          password: studentPassword,
          role: 'STUDENT',
          nickname: data.nickname
        }
      })
      students.push(student)
    }
    console.log('✅ 学生用户创建成功')

    // 创建日语课程类型
    const courseTypes = [
      {
        name: '自由会话',
        type: 'FREE_CONVERSATION',
        description: '轻松愉快的日语自由对话，适合初学者体验日语交流的乐趣',
        price: 0, // 免费体验
        duration: 30
      },
      {
        name: '口语课程',
        type: 'SPEAKING',
        description: '专注于日语口语表达能力的提升，通过实际对话练习提高流利度',
        price: 18000, // 180元
        duration: 60
      },
      {
        name: '商务日语',
        type: 'BUSINESS',
        description: '商务场景下的日语应用，包括商务礼仪、邮件写作、会议交流等',
        price: 20000, // 200元
        duration: 60
      },
      {
        name: '写作课程',
        type: 'WRITING',
        description: '日语写作技巧训练，从基础作文到高级写作表达',
        price: 20000, // 200元
        duration: 60
      },
      {
        name: '语法课程',
        type: 'GRAMMAR',
        description: '系统学习日语语法结构，从基础到高级语法的全面掌握',
        price: 20000, // 200元
        duration: 60
      },
      {
        name: 'JLPT N5-1 对策',
        type: 'JLPT',
        description: '日语能力考试专项辅导，针对性提高考试技巧和应试能力',
        price: 22000, // 220元
        duration: 60
      },
      {
        name: '研究生面试对策',
        type: 'GRADUATE_INTERVIEW',
        description: '日本研究生入学面试专项训练，提高面试表现和成功率',
        price: 30000, // 300元
        duration: 60
      },
      {
        name: '求职就职面试',
        type: 'JOB_INTERVIEW',
        description: '日本就职面试指导，包括简历制作、面试技巧、职场日语等',
        price: 30000, // 300元
        duration: 60
      },
      {
        name: '演讲指导',
        type: 'SPEECH_GUIDANCE',
        description: '日语演讲技巧培训，提升公众演讲和表达能力',
        price: 50000, // 500元
        duration: 60
      },
      {
        name: '高考日语',
        type: 'GAOKAO_JAPANESE',
        description: '高考日语专项辅导，针对高考日语考试的全面准备',
        price: 30000, // 300元
        duration: 60
      },
      {
        name: '其他课程',
        type: 'OTHER',
        description: '根据学生特殊需求定制的个性化课程',
        price: 0, // 价格协商
        duration: 60
      }
    ]

    const courses = []
    for (const courseData of courseTypes) {
      const course = await prisma.course.create({
        data: {
          ...courseData,
          teacherId: teacher.id
        }
      })
      courses.push(course)
    }
    console.log('✅ 日语课程类型创建成功')

    // 创建选课记录（学生选择不同的课程）
    const enrollmentData = [
      { studentIndex: 0, courseIndex: 1 }, // 小明选择口语课程
      { studentIndex: 0, courseIndex: 4 }, // 小明选择语法课程
      { studentIndex: 1, courseIndex: 2 }, // 小红选择商务日语
      { studentIndex: 1, courseIndex: 5 }, // 小红选择JLPT对策
      { studentIndex: 2, courseIndex: 3 }, // 小李选择写作课程
      { studentIndex: 3, courseIndex: 6 }, // 小王选择研究生面试对策
      { studentIndex: 4, courseIndex: 9 }  // 小张选择高考日语
    ]

    for (const enrollment of enrollmentData) {
      await prisma.enrollment.create({
        data: {
          studentId: students[enrollment.studentIndex].id,
          courseId: courses[enrollment.courseIndex].id
        }
      })
    }
    console.log('✅ 选课记录创建成功')

    // 创建注册验证码
    const registrationCodes = [
      'JAPANESE2024',
      'SENSEI123',
      'NIHONGO456',
      'STUDY789'
    ]

    for (const code of registrationCodes) {
      await prisma.registrationCode.upsert({
        where: { code: code },
        update: {},
        create: {
          code: code
        }
      })
    }
    console.log('✅ 注册验证码创建成功')

    // 创建示例课程安排
    const now = new Date()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)
    const dayAfterTomorrow = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000)

    await prisma.session.create({
      data: {
        courseId: courses[1].id, // 口语课程
        studentId: students[0].id, // 小明
        title: '日语口语练习 - 自我介绍',
        description: '学习基本的自我介绍表达方式',
        sessionDate: tomorrow,
        duration: 60,
        location: '线上教室',
        status: 'SCHEDULED'
      }
    })

    await prisma.session.create({
      data: {
        courseId: courses[2].id, // 商务日语
        studentId: students[1].id, // 小红
        title: '商务邮件写作',
        description: '学习商务邮件的基本格式和常用表达',
        sessionDate: dayAfterTomorrow,
        duration: 60,
        location: '线上教室',
        status: 'SCHEDULED'
      }
    })
    console.log('✅ 课程安排创建成功')

    // 创建示例评价
    await prisma.evaluation.create({
      data: {
        courseId: courses[1].id,
        teacherId: teacher.id,
        studentId: students[0].id,
        title: '口语课程第一次评价',
        content: '小明同学在今天的口语课程中表现很好！发音准确，学习态度认真。建议多练习日常对话，继续保持学习热情。下次课我们将学习更多实用的表达方式。',
        rating: 5,
        isPublic: false,
        isGroupEvaluation: false
      }
    })
    console.log('✅ 示例评价创建成功')

    console.log('🎉 日语教学管理系统数据填充完成！')
    console.log('')
    console.log('📚 系统信息：')
    console.log('- 教师账号: teacher / teacher123')
    console.log('- 学生账号: student1-student5 / 123456')
    console.log('- 注册验证码: JAPANESE2024, SENSEI123, NIHONGO456, STUDY789')
    console.log('')
    console.log('🎌 课程类型：')
    courseTypes.forEach((course, index) => {
      const priceText = course.price === 0 ? '免费' : `${course.price / 100}元`
      console.log(`${index + 1}. ${course.name} - ${priceText}/${course.duration}分钟`)
    })

  } catch (error) {
    console.error('❌ 填充数据时出错:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('填充数据时出错:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
