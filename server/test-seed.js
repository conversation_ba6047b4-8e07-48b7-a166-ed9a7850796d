import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'
import dotenv from 'dotenv'

dotenv.config()

const prisma = new PrismaClient()

async function main() {
  console.log('开始填充数据库种子数据...')

  try {
    // 创建管理员用户
    const adminPassword = await bcrypt.hash('admin123', 12)
    const admin = await prisma.user.upsert({
      where: { username: 'admin' },
      update: {},
      create: {
        username: 'admin',
        email: '<EMAIL>',
        name: '系统管理员',
        password: adminPassword,
        role: 'ADMIN'
      }
    })
    console.log('✅ 管理员用户创建成功')

    // 创建教师用户
    const teacherPassword = await bcrypt.hash('123456', 12)
    const teacher1 = await prisma.user.upsert({
      where: { username: 'teacher' },
      update: {},
      create: {
        username: 'teacher',
        email: '<EMAIL>',
        name: '张教授',
        password: teacherPassword,
        role: 'TEACHER'
      }
    })
    console.log('✅ 教师用户创建成功')

    // 创建学生用户
    const studentPassword = await bcrypt.hash('123456', 12)
    const student = await prisma.user.upsert({
      where: { username: 'student' },
      update: {},
      create: {
        username: 'student',
        email: '<EMAIL>',
        name: '学生1号',
        password: studentPassword,
        role: 'STUDENT'
      }
    })
    console.log('✅ 学生用户创建成功')

    // 创建课程
    const course1 = await prisma.course.upsert({
      where: { code: 'MATH001' },
      update: {},
      create: {
        name: '高等数学',
        description: '高等数学是理工科学生的基础课程，主要学习微积分、线性代数等内容。',
        code: 'MATH001',
        credits: 4,
        semester: '春季学期',
        year: 2024,
        teacherId: teacher1.id
      }
    })
    console.log('✅ 课程创建成功')

    // 创建选课记录
    await prisma.enrollment.upsert({
      where: {
        studentId_courseId: {
          studentId: student.id,
          courseId: course1.id
        }
      },
      update: {},
      create: {
        studentId: student.id,
        courseId: course1.id
      }
    })
    console.log('✅ 选课记录创建成功')

    // 创建评价
    await prisma.evaluation.create({
      data: {
        courseId: course1.id,
        teacherId: teacher1.id,
        studentId: student.id,
        title: '第一次课程评价',
        content: '学生在本次课程中表现良好，积极参与课堂讨论，作业完成质量较高。建议继续保持学习热情，加强基础知识的巩固。',
        rating: 5,
        isPublic: false
      }
    })
    console.log('✅ 评价创建成功')

    console.log('🎉 数据库种子数据填充完成！')
    console.log('创建的用户：')
    console.log('- 管理员: admin / admin123')
    console.log('- 教师: teacher / 123456')
    console.log('- 学生: student / 123456')

  } catch (error) {
    console.error('❌ 填充种子数据时出错:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('填充种子数据时出错:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
