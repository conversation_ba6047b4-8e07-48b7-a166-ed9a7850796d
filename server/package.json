{"name": "course-evaluation-server", "version": "1.0.0", "description": "课程评价系统后端服务", "main": "src/index.js", "type": "module", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "build": "echo 'No build step required'", "db:migrate": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio", "db:generate": "npx prisma generate"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "express-rate-limit": "^6.10.0", "@prisma/client": "^5.2.0"}, "devDependencies": {"nodemon": "^3.0.1", "prisma": "^5.2.0"}, "prisma": {"seed": "node prisma/seed.js"}, "keywords": ["nodejs", "express", "postgresql", "prisma", "education", "course-evaluation"], "author": "Course Evaluation System", "license": "MIT"}