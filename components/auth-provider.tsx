"use client"

import type React from "react"
import { createContext, useContext, useEffect, useState } from "react"
import type { User } from "@/lib/auth"

interface AuthContextType {
  user: User | null
  loading: boolean
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  signOut: async () => {},
})

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 检查localStorage中的用户数据
    const userData = localStorage.getItem("currentUser")
    if (userData) {
      setUser(JSON.parse(userData))
    }
    setLoading(false)

    // 监听登录事件
    const handleLogin = (event: CustomEvent) => {
      setUser(event.detail)
    }

    // 监听登出事件
    const handleLogout = () => {
      setUser(null)
    }

    window.addEventListener("userLogin", handleLogin as EventListener)
    window.addEventListener("userLogout", handleLogout)

    return () => {
      window.removeEventListener("userLogin", handleLogin as EventListener)
      window.removeEventListener("userLogout", handleLogout)
    }
  }, [])

  async function signOut() {
    localStorage.removeItem("currentUser")
    setUser(null)

    // Dispatch logout event
    window.dispatchEvent(new CustomEvent("userLogout"))

    // Show logout message and redirect after delay
    const logoutMessage = document.createElement("div")
    logoutMessage.innerHTML = `
      <div style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 9999;
        text-align: center;
        border: 1px solid #e5e7eb;
      ">
        <div style="margin-bottom: 1rem; font-size: 1.125rem; font-weight: 600; color: #374151;">
          退出成功
        </div>
        <div style="color: #6b7280; font-size: 0.875rem;">
          正在跳转到首页...
        </div>
      </div>
    `

    // Add backdrop
    const backdrop = document.createElement("div")
    backdrop.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9998;
    `

    document.body.appendChild(backdrop)
    document.body.appendChild(logoutMessage)

    // Redirect after 2 seconds
    setTimeout(() => {
      document.body.removeChild(backdrop)
      document.body.removeChild(logoutMessage)
      window.location.href = "/"
    }, 2000)
  }

  return <AuthContext.Provider value={{ user, loading, signOut }}>{children}</AuthContext.Provider>
}

export const useAuth = () => useContext(AuthContext)
