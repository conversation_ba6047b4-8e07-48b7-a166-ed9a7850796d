"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Bell, Check } from "lucide-react"
import { useAuth } from "@/components/auth-provider"
import { NotificationService, type Notification } from "@/lib/notifications"

export function NotificationDropdown() {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [isOpen, setIsOpen] = useState(false)

  useEffect(() => {
    if (user) {
      fetchNotifications()
    }
  }, [user])

  async function fetchNotifications() {
    if (!user) return

    const notifications = await NotificationService.getNotifications(user.id)
    const unreadCount = await NotificationService.getUnreadCount(user.id)

    setNotifications(notifications)
    setUnreadCount(unreadCount)
  }

  async function markAsRead(notificationId: string) {
    await NotificationService.markAsRead(notificationId)
    await fetchNotifications()
  }

  function getNotificationIcon(type: string) {
    switch (type) {
      case "enrollment_request":
        return "📝"
      case "enrollment_approved":
        return "✅"
      case "enrollment_rejected":
        return "❌"
      default:
        return "📢"
    }
  }

  if (!user) return null

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-4 w-4" />
          {unreadCount > 0 && (
            <Badge variant="destructive" className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs">
              {unreadCount > 9 ? "9+" : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <div className="p-2">
          <h3 className="font-semibold text-sm mb-2">通知</h3>
          {notifications.length === 0 ? (
            <p className="text-sm text-gray-500 text-center py-4">暂无通知</p>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {notifications.slice(0, 5).map((notification) => (
                <div
                  key={notification.id}
                  className={`p-2 rounded-lg border ${notification.read ? "bg-gray-50" : "bg-blue-50 border-blue-200"}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span>{getNotificationIcon(notification.type)}</span>
                        <p className="text-sm font-medium">{notification.title}</p>
                      </div>
                      <p className="text-xs text-gray-600 mt-1">{notification.message}</p>
                      <p className="text-xs text-gray-400 mt-1">
                        {new Date(notification.created_at).toLocaleString("zh-CN")}
                      </p>
                    </div>
                    {!notification.read && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                        className="h-6 w-6 p-0"
                      >
                        <Check className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export function NotificationList() {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<Notification[]>([])

  useEffect(() => {
    if (user) {
      fetchNotifications()
    }
  }, [user])

  async function fetchNotifications() {
    if (!user) return
    const notifications = await NotificationService.getNotifications(user.id)
    setNotifications(notifications)
  }

  async function markAsRead(notificationId: string) {
    await NotificationService.markAsRead(notificationId)
    await fetchNotifications()
  }

  if (!user) return null

  return (
    <Card>
      <CardHeader>
        <CardTitle>通知中心</CardTitle>
        <CardDescription>查看您的所有通知</CardDescription>
      </CardHeader>
      <CardContent>
        {notifications.length === 0 ? (
          <p className="text-center text-gray-500 py-8">暂无通知</p>
        ) : (
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 rounded-lg border ${notification.read ? "bg-gray-50" : "bg-blue-50 border-blue-200"}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getNotificationIcon(notification.type)}</span>
                      <h4 className="font-medium">{notification.title}</h4>
                      {!notification.read && (
                        <Badge variant="secondary" className="text-xs">
                          新
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-2">{notification.message}</p>
                    <p className="text-xs text-gray-400 mt-2">
                      {new Date(notification.created_at).toLocaleString("zh-CN")}
                    </p>
                  </div>
                  {!notification.read && (
                    <Button variant="outline" size="sm" onClick={() => markAsRead(notification.id)}>
                      标记已读
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )

  function getNotificationIcon(type: string) {
    switch (type) {
      case "enrollment_request":
        return "📝"
      case "enrollment_approved":
        return "✅"
      case "enrollment_rejected":
        return "❌"
      default:
        return "📢"
    }
  }
}
