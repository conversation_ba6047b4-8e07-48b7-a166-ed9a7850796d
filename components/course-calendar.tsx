"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ChevronLeft, ChevronRight, Calendar, Clock } from "lucide-react"

interface Session {
  id: string
  name: string
  date: string
  time: string
  description?: string
}

interface CourseCalendarProps {
  sessions: Session[]
  courseId: string
  userRole: "student" | "teacher" | "admin"
}

export function CourseCalendar({ sessions, courseId, userRole }: CourseCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay()
  }

  const getSessionsForDate = (date: Date) => {
    const dateStr = date.toISOString().split("T")[0]
    return sessions.filter((session) => session.date === dateStr)
  }

  const navigateMonth = (direction: "prev" | "next") => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev)
      if (direction === "prev") {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const daysInMonth = getDaysInMonth(currentDate)
  const firstDayOfMonth = getFirstDayOfMonth(currentDate)
  const monthNames = [
    "一月",
    "二月",
    "三月",
    "四月",
    "五月",
    "六月",
    "七月",
    "八月",
    "九月",
    "十月",
    "十一月",
    "十二月",
  ]
  const dayNames = ["日", "一", "二", "三", "四", "五", "六"]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              课程日历
            </CardTitle>
            <CardDescription>查看课程安排和时间表</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => navigateMonth("prev")}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="font-medium min-w-[100px] text-center">
              {currentDate.getFullYear()}年 {monthNames[currentDate.getMonth()]}
            </span>
            <Button variant="outline" size="sm" onClick={() => navigateMonth("next")}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-7 gap-1 mb-4">
          {dayNames.map((day) => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {/* Empty cells for days before the first day of the month */}
          {Array.from({ length: firstDayOfMonth }, (_, index) => (
            <div key={`empty-${index}`} className="p-2 h-20"></div>
          ))}

          {/* Days of the month */}
          {Array.from({ length: daysInMonth }, (_, index) => {
            const day = index + 1
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day)
            const sessionsForDay = getSessionsForDate(date)
            const isToday = date.toDateString() === new Date().toDateString()

            return (
              <div
                key={day}
                className={`p-1 h-20 border rounded-lg ${
                  isToday ? "bg-blue-50 border-blue-200" : "border-gray-200"
                } ${sessionsForDay.length > 0 ? "bg-green-50" : ""}`}
              >
                <div className={`text-sm font-medium mb-1 ${isToday ? "text-blue-600" : ""}`}>{day}</div>

                {sessionsForDay.map((session) => (
                  <div
                    key={session.id}
                    className="text-xs bg-blue-100 text-blue-800 rounded px-1 py-0.5 mb-1 truncate cursor-pointer hover:bg-blue-200"
                    title={`${session.name} - ${session.time}`}
                  >
                    <div className="flex items-center">
                      <Clock className="h-2 w-2 mr-1" />
                      {session.time.split("-")[0]}
                    </div>
                    <div className="truncate">{session.name}</div>
                  </div>
                ))}
              </div>
            )
          })}
        </div>

        {/* Legend */}
        <div className="mt-4 flex items-center justify-center space-x-4 text-xs text-gray-500">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-50 border border-blue-200 rounded mr-1"></div>
            今天
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-50 border border-gray-200 rounded mr-1"></div>
            有课程
          </div>
        </div>

        {/* Upcoming Sessions List */}
        <div className="mt-6">
          <h4 className="font-medium mb-3">即将到来的课程</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {sessions
              .filter((session) => new Date(session.date) >= new Date())
              .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
              .slice(0, 5)
              .map((session) => (
                <div key={session.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div>
                    <p className="font-medium text-sm">{session.name}</p>
                    <p className="text-xs text-gray-600">
                      {new Date(session.date).toLocaleDateString("zh-CN")} {session.time}
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {Math.ceil((new Date(session.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))} 天后
                  </Badge>
                </div>
              ))}

            {sessions.filter((session) => new Date(session.date) >= new Date()).length === 0 && (
              <p className="text-gray-500 text-center py-4">暂无即将到来的课程</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
