"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useAuth } from "@/components/auth-provider"
import { BookOpen, LogOut, User } from "lucide-react"
import Link from "next/link"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { NotificationDropdown } from "@/components/notifications"

export function Navbar() {
  const { user, signOut } = useAuth()

  return (
    <nav className="border-b bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2">
              <BookOpen className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">课程评价系统</span>
            </Link>
          </div>

          {user && (
            <div className="flex items-center space-x-4">
              <nav className="flex space-x-4">
                {user.role === "teacher" && (
                  <>
                    <Link href="/teacher/dashboard" className="text-gray-700 hover:text-blue-600">
                      教师面板
                    </Link>
                    <Link href="/teacher/courses" className="text-gray-700 hover:text-blue-600">
                      我的课程
                    </Link>
                    <Link href="/teacher/evaluations" className="text-gray-700 hover:text-blue-600">
                      评价管理
                    </Link>
                    <Link href="/teacher/students" className="text-gray-700 hover:text-blue-600">
                      学生管理
                    </Link>
                    <Link href="/teacher/requests" className="text-gray-700 hover:text-blue-600">
                      申请审核
                    </Link>
                  </>
                )}
                {user.role === "student" && (
                  <>
                    <Link href="/student/dashboard" className="text-gray-700 hover:text-blue-600">
                      学生面板
                    </Link>
                    <Link href="/student/evaluations" className="text-gray-700 hover:text-blue-600">
                      我的评价
                    </Link>
                    <Link href="/student/courses" className="text-gray-700 hover:text-blue-600">
                      课程选择
                    </Link>
                    <Link href="/student/requests" className="text-gray-700 hover:text-blue-600">
                      申请状态
                    </Link>
                  </>
                )}
                {user.role === "admin" && (
                  <>
                    <Link href="/admin/dashboard" className="text-gray-700 hover:text-blue-600">
                      管理面板
                    </Link>
                    <Link href="/admin/users" className="text-gray-700 hover:text-blue-600">
                      用户管理
                    </Link>
                    <Link href="/admin/courses" className="text-gray-700 hover:text-blue-600">
                      课程管理
                    </Link>
                  </>
                )}
              </nav>

              <NotificationDropdown />

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>{user.name}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={signOut}>
                    <LogOut className="h-4 w-4 mr-2" />
                    退出登录
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}
