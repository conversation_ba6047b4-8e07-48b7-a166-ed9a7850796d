# 教育评价平台 Docker 部署指南

## 🚀 快速开始

### 1. 环境准备

确保服务器已安装：
- Docker (20.10+)
- Docker Compose (2.0+)

```bash
# 检查 Docker 版本
docker --version
docker-compose --version
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.production.example .env.production

# 编辑生产环境配置
nano .env.production
```

填入您的 PostgreSQL 配置：
```env
```

### 3. 一键部署

```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 部署生产环境
./deploy.sh prod
```

## 🔧 调试指南

### 开发环境调试

```bash
# 启动开发环境（支持热重载）
./deploy.sh dev

# 应用将运行在 http://localhost:3001
# 代码修改会自动重新加载
```

### 查看日志

```bash
# 查看实时日志
./deploy.sh logs

# 或直接使用 docker-compose
docker-compose logs -f app

# 查看特定时间的日志
docker-compose logs --since="2h" app
```

### 容器调试

```bash
# 查看容器状态
docker-compose ps

# 进入容器内部
docker-compose exec app sh

# 查看容器资源使用
docker stats

# 重启特定服务
docker-compose restart app
```

### 数据库连接调试

```bash
# 测试 PostgreSQL 连接
docker-compose exec app node -e "
const { createClient } = require('@supabase/supabase-js');
client.from('users').select('count').then(console.log).catch(console.error);
"
```

## 🌐 Cloudflare 配置

### DNS 设置
1. 在 Cloudflare 中添加 A 记录指向您的服务器 IP
2. 启用 Cloudflare 代理（橙色云朵）

### SSL/TLS 设置
- 加密模式：完全（严格）
- 边缘证书：自动 HTTPS 重写开启

### 页面规则（可选）
```
your-domain.com/*
- 缓存级别：标准
- 浏览器缓存 TTL：4 小时
```

## 📊 监控和维护

### 健康检查

```bash
# 执行健康检查
./deploy.sh health

# 手动检查
curl -f http://localhost:3000
```

### 备份策略

```bash
# 备份环境配置
cp .env.production .env.production.backup

# 导出 Docker 镜像
docker save educational-evaluation-platform > backup.tar
```

### 更新部署

```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
./deploy.sh prod
```

## 🐛 常见问题

### 1. 容器启动失败
```bash
# 检查日志
docker-compose logs app

# 常见原因：
# - 环境变量配置错误
# - 端口被占用
# - 内存不足
```

### 2. PostgreSQL 连接失败
```bash
# 检查环境变量
docker-compose exec app env | grep SUPABASE

# 测试网络连接
docker-compose exec app ping your-project.supabase.co
```

### 3. 应用无法访问
```bash
# 检查端口映射
docker-compose ps

# 检查防火墙
sudo ufw status
sudo ufw allow 3000
```

## 📈 性能优化

### 1. 启用 Nginx 反向代理
```bash
# 启动 Nginx 服务
docker-compose --profile nginx up -d nginx
```

### 2. 资源限制
在 `docker-compose.yml` 中添加：
```yaml
services:
  app:
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
```

## 🔒 安全建议

1. **环境变量安全**
   - 不要将 `.env.production` 提交到版本控制
   - 定期轮换 API 密钥

2. **网络安全**
   - 使用 Cloudflare 的 DDoS 保护
   - 启用 WAF 规则

3. **容器安全**
   - 定期更新基础镜像
   - 使用非 root 用户运行应用

## 📞 技术支持

如遇到问题，请检查：
1. Docker 和 Docker Compose 版本
2. 服务器资源使用情况
3. PostgreSQL 服务状态
4. 网络连接状况
