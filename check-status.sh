#!/bin/bash

echo "🎌 日语教学管理平台 - 系统状态检查"
echo "=================================="

# 检查前端服务器
echo "📱 检查前端服务器 (http://localhost:5173)..."
if curl -s http://localhost:5173 > /dev/null; then
    echo "✅ 前端服务器运行正常"
else
    echo "❌ 前端服务器未运行"
fi

# 检查后端服务器
echo "🔧 检查后端服务器 (http://localhost:3000)..."
if curl -s http://localhost:3000/api/health > /dev/null; then
    echo "✅ 后端服务器运行正常"
    
    # 检查API健康状态
    health_status=$(curl -s http://localhost:3000/api/health | grep -o '"status":"OK"')
    if [ "$health_status" = '"status":"OK"' ]; then
        echo "✅ API健康检查通过"
    else
        echo "⚠️  API健康检查异常"
    fi
else
    echo "❌ 后端服务器未运行"
fi

# 检查数据库连接
echo "🗄️  检查数据库连接..."
cd server 2>/dev/null
if [ -f ".env" ]; then
    if grep -q "DATABASE_URL" .env; then
        echo "✅ 数据库配置文件存在"
        
        # 尝试连接数据库
        if npx prisma db push --accept-data-loss 2>/dev/null; then
            echo "✅ 数据库连接正常"
        else
            echo "⚠️  数据库连接可能有问题"
        fi
    else
        echo "❌ 数据库配置缺失"
    fi
else
    echo "❌ 环境配置文件不存在"
fi
cd ..

# 检查端口占用
echo "🔌 检查端口占用情况..."
if lsof -i :3000 > /dev/null 2>&1; then
    echo "✅ 端口 3000 (后端) 已被使用"
else
    echo "❌ 端口 3000 (后端) 未被使用"
fi

if lsof -i :5173 > /dev/null 2>&1; then
    echo "✅ 端口 5173 (前端) 已被使用"
else
    echo "❌ 端口 5173 (前端) 未被使用"
fi

# 测试登录功能
echo "🔐 测试登录功能..."
login_response=$(curl -s -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"teacher","password":"teacher123"}' 2>/dev/null)

if echo "$login_response" | grep -q '"success":true'; then
    echo "✅ 教师登录功能正常"
else
    echo "❌ 教师登录功能异常"
fi

# 显示访问信息
echo ""
echo "🌐 访问信息"
echo "=================================="
echo "前端应用: http://localhost:5173"
echo "后端API:  http://localhost:3000/api"
echo "API文档:  http://localhost:3000/api"
echo "健康检查: http://localhost:3000/api/health"
echo ""
echo "🔑 测试账号"
echo "=================================="
echo "教师账号: teacher / teacher123"
echo "学生账号: student1-5 / 123456"
echo ""
echo "📊 系统检查完成！"
