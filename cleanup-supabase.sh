#!/bin/bash

# Supabase 清理脚本
# 移除项目中所有 Supabase 相关的代码和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理文档中的 Supabase 引用
cleanup_docs() {
    log_info "清理文档中的 Supabase 引用..."
    
    # 更新 SETUP_GUIDE.md
    if [ -f "SETUP_GUIDE.md" ]; then
        sed -i 's/Supabase/PostgreSQL/g' SETUP_GUIDE.md
        sed -i '/Supabase SQL Editor/d' SETUP_GUIDE.md
        sed -i '/supabase\.com/d' SETUP_GUIDE.md
        log_success "已更新 SETUP_GUIDE.md"
    fi
    
    # 更新 QUICK_START.md
    if [ -f "QUICK_START.md" ]; then
        sed -i 's/Supabase/PostgreSQL/g' QUICK_START.md
        sed -i '/填入您的 Supabase 配置/c\# 填入您的 PostgreSQL 配置' QUICK_START.md
        log_success "已更新 QUICK_START.md"
    fi
    
    # 更新 DEPLOYMENT.md
    if [ -f "DEPLOYMENT.md" ]; then
        sed -i 's/Supabase/PostgreSQL/g' DEPLOYMENT.md
        sed -i '/NEXT_PUBLIC_SUPABASE/d' DEPLOYMENT.md
        sed -i '/SUPABASE_SERVICE_ROLE_KEY/d' DEPLOYMENT.md
        log_success "已更新 DEPLOYMENT.md"
    fi
}

# 清理脚本中的 Supabase 引用
cleanup_scripts() {
    log_info "清理脚本中的 Supabase 引用..."
    
    # 更新 health-check.sh
    if [ -f "health-check.sh" ]; then
        # 移除 check_supabase 函数调用
        sed -i '/check_supabase/d' health-check.sh
        # 移除整个 check_supabase 函数
        sed -i '/# 检查 Supabase 连接/,/^}/d' health-check.sh
        log_success "已更新 health-check.sh"
    fi
    
    # 更新 debug.sh
    if [ -f "debug.sh" ]; then
        # 替换数据库测试逻辑
        sed -i 's/@supabase\/supabase-js/\.\/lib\/database/g' debug.sh
        sed -i 's/createClient/healthCheck/g' debug.sh
        log_success "已更新 debug.sh"
    fi
}

# 清理环境变量示例文件
cleanup_env_files() {
    log_info "清理环境变量示例文件..."
    
    # 更新 .env.local.example
    if [ -f ".env.local.example" ]; then
        cat > .env.local.example << 'EOF'
# 开发环境配置文件
# 复制此文件为 .env.local 并填入您的 PostgreSQL 配置

# PostgreSQL 数据库配置
DATABASE_URL=****************************************/database
POSTGRES_HOST=localhost
POSTGRES_DB=education_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
POSTGRES_PORT=5432

# Redis 缓存配置（可选）
REDIS_URL=redis://localhost:6379

# 开发环境特定配置
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development

# 调试选项
DEBUG=true
NEXT_PUBLIC_DEBUG=true

# 应用配置
NEXT_PUBLIC_DATABASE_TYPE=postgresql
EOF
        log_success "已更新 .env.local.example"
    fi
    
    # 更新 .env.production.example
    if [ -f ".env.production.example" ]; then
        cat > .env.production.example << 'EOF'
# 生产环境配置文件
# 复制此文件为 .env.production 并填入您的 PostgreSQL 配置

# PostgreSQL 数据库配置
DATABASE_URL=****************************************/database
POSTGRES_HOST=localhost
POSTGRES_DB=education_platform
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
POSTGRES_PORT=5432

# Redis 缓存配置（可选）
REDIS_URL=redis://localhost:6379

# 生产环境特定配置
NODE_ENV=production
NEXT_PUBLIC_APP_ENV=production

# 安全配置
DEBUG=false
NEXT_PUBLIC_DEBUG=false

# 应用配置
NEXT_PUBLIC_DATABASE_TYPE=postgresql
EOF
        log_success "已更新 .env.production.example"
    fi
}

# 删除冗余文件
cleanup_redundant_files() {
    log_info "删除冗余文件..."
    
    # 删除 Supabase 相关的环境变量文件
    rm -f .env.local.supabase
    
    # 删除旧的数据库切换脚本（我们会创建新的）
    if [ -f "switch-database.sh" ]; then
        log_warning "保留 switch-database.sh，但需要手动更新"
    fi
    
    log_success "冗余文件清理完成"
}

# 主函数
main() {
    echo "🧹 开始清理 Supabase 相关内容..."
    echo ""
    
    cleanup_docs
    cleanup_scripts
    cleanup_env_files
    cleanup_redundant_files
    
    echo ""
    log_success "Supabase 清理完成！"
    echo ""
    echo "📋 清理总结："
    echo "  ✅ 已删除 lib/supabase.ts"
    echo "  ✅ 已移除 package.json 中的 @supabase/supabase-js 依赖"
    echo "  ✅ 已创建新的 lib/data-access.ts"
    echo "  ✅ 已更新教师仪表板页面"
    echo "  ✅ 已清理文档中的 Supabase 引用"
    echo "  ✅ 已更新脚本文件"
    echo "  ✅ 已更新环境变量示例文件"
    echo ""
    echo "⚠️  还需要手动更新的文件："
    echo "  - app/student/evaluations/page.tsx"
    echo "  - app/student/evaluations/[id]/page.tsx"
    echo "  - app/teacher/evaluations/create/page.tsx"
    echo "  - 其他使用 supabase 的组件文件"
    echo ""
    echo "🚀 下一步："
    echo "  1. 运行 'npm install' 更新依赖"
    echo "  2. 手动更新剩余的组件文件"
    echo "  3. 测试应用功能"
}

main "$@"
