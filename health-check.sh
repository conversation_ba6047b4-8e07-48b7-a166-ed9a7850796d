#!/bin/bash

# 教育评价平台健康检查脚本
# 用于检查开发和生产环境的运行状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 服务
check_docker() {
    log_info "检查 Docker 服务状态..."
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 服务未运行"
        return 1
    fi
    
    log_success "Docker 服务正常"
}

# 检查容器状态
check_containers() {
    log_info "检查容器状态..."
    
    # 检查生产环境容器
    if docker-compose ps app | grep -q "Up"; then
        log_success "生产环境容器运行正常"
        PROD_RUNNING=true
    else
        log_warning "生产环境容器未运行"
        PROD_RUNNING=false
    fi
    
    # 检查开发环境容器
    if docker-compose --profile dev ps app-dev | grep -q "Up"; then
        log_success "开发环境容器运行正常"
        DEV_RUNNING=true
    else
        log_warning "开发环境容器未运行"
        DEV_RUNNING=false
    fi
}

# 检查应用健康状态
check_app_health() {
    log_info "检查应用健康状态..."
    
    # 检查生产环境
    if [ "$PROD_RUNNING" = true ]; then
        if curl -f -s http://localhost:3000 > /dev/null; then
            log_success "生产环境应用响应正常 (http://localhost:3000)"
        else
            log_error "生产环境应用无响应"
        fi
    fi
    
    # 检查开发环境
    if [ "$DEV_RUNNING" = true ]; then
        if curl -f -s http://localhost:3001 > /dev/null; then
            log_success "开发环境应用响应正常 (http://localhost:3001)"
        else
            log_error "开发环境应用无响应"
        fi
    fi
}


# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."

    # 检查开发环境数据库连接
    if [ "$DEV_RUNNING" = true ]; then
        if curl -f -s http://localhost:3001/api/health > /dev/null 2>&1; then
            log_success "开发环境数据库连接正常"
        else
            log_error "开发环境数据库连接失败"
        fi
    fi

    # 检查生产环境数据库连接
    if [ "$PROD_RUNNING" = true ]; then
        if curl -f -s http://localhost:3000/api/health > /dev/null 2>&1; then
            log_success "生产环境数据库连接正常"
        else
            log_error "生产环境数据库连接失败"
        fi
    fi
}

# 检查系统资源
check_resources() {
    log_info "检查系统资源使用情况..."
    
    # 检查内存使用
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$MEMORY_USAGE > 80" | bc -l) )); then
        log_warning "内存使用率较高: ${MEMORY_USAGE}%"
    else
        log_success "内存使用率正常: ${MEMORY_USAGE}%"
    fi
    
    # 检查磁盘使用
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$DISK_USAGE" -gt 80 ]; then
        log_warning "磁盘使用率较高: ${DISK_USAGE}%"
    else
        log_success "磁盘使用率正常: ${DISK_USAGE}%"
    fi
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用情况..."
    
    # 检查 3000 端口
    if netstat -tulpn 2>/dev/null | grep -q ":3000 "; then
        log_success "端口 3000 已被使用（生产环境）"
    else
        log_warning "端口 3000 未被使用"
    fi
    
    # 检查 3001 端口
    if netstat -tulpn 2>/dev/null | grep -q ":3001 "; then
        log_success "端口 3001 已被使用（开发环境）"
    else
        log_warning "端口 3001 未被使用"
    fi
}

# 生成健康报告
generate_report() {
    log_info "生成健康检查报告..."
    
    echo ""
    echo "=================================="
    echo "     健康检查报告"
    echo "=================================="
    echo "检查时间: $(date)"
    echo ""
    echo "环境状态:"
    echo "  - 生产环境: $([ "$PROD_RUNNING" = true ] && echo "运行中" || echo "未运行")"
    echo "  - 开发环境: $([ "$DEV_RUNNING" = true ] && echo "运行中" || echo "未运行")"
    echo ""
    echo "系统资源:"
    echo "  - 内存使用率: ${MEMORY_USAGE}%"
    echo "  - 磁盘使用率: ${DISK_USAGE}%"
    echo ""
    echo "访问地址:"
    if [ "$PROD_RUNNING" = true ]; then
        echo "  - 生产环境: http://localhost:3000"
    fi
    if [ "$DEV_RUNNING" = true ]; then
        echo "  - 开发环境: http://localhost:3001"
    fi
    echo "=================================="
}

# 主函数
main() {
    echo "🏥 教育评价平台健康检查"
    echo ""
    
    check_docker || exit 1
    check_containers
    check_app_health
    check_database
    check_resources
    check_ports
    generate_report
    
    echo ""
    log_success "健康检查完成！"
}

main "$@"
