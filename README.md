# 课程评价系统

一个基于React和PostgreSQL的现代化教育课程评价平台，专为教师和学生设计的互动系统。

## 🚀 技术栈

### 前端
- **React 18** - 现代化用户界面库
- **Vite** - 快速构建工具
- **Ant Design** - 企业级UI组件库
- **React Router** - 单页面应用路由
- **Zustand** - 轻量级状态管理
- **Axios** - HTTP客户端

### 后端
- **Node.js** + **Express** - 服务端框架
- **PostgreSQL** - 关系型数据库
- **Prisma** - 现代化ORM工具
- **JWT** - 身份认证
- **Multer** - 文件上传处理
- **bcrypt** - 密码加密

## 📋 功能特性

### 👨‍🏫 教师功能
- ✅ 课程创建和管理
- ✅ 富文本评价编辑
- ✅ 课程材料上传（PPT、作业等）
- ✅ 批量或个人评价发布
- ✅ 课程公告管理
- ✅ 课程时间表安排
- ✅ 学生选课管理

### 👨‍🎓 学生功能
- ✅ 查看个人课程评价
- ✅ 评价回复和反馈
- ✅ 课程信息查看
- ✅ 课程材料下载
- ✅ 隐私数据保护

### 🔐 系统特性
- ✅ JWT安全认证
- ✅ 角色权限管理
- ✅ 完全中文界面
- ✅ 响应式设计
- ✅ 数据库事务支持

## 🛠️ 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 13+
- npm 或 yarn

### 1. 克隆项目
```bash
git clone <repository-url>
cd course-evaluation-system
```

### 2. 安装依赖
```bash
npm run install:all
```

### 3. 配置数据库
1. 创建PostgreSQL数据库
2. 复制环境配置文件：
   ```bash
   cp server/.env.example server/.env
   ```
3. 编辑 `server/.env` 文件，配置数据库连接

### 4. 数据库迁移
```bash
npm run db:migrate
```

### 5. 数据库种子数据
```bash
npm run db:seed
```

### 6. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:5173 查看前端应用
访问 http://localhost:3000 查看后端API

## 📁 项目结构

```
course-evaluation-system/
├── client/                 # React前端应用
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── store/         # 状态管理
│   │   ├── utils/         # 工具函数
│   │   └── styles/        # 样式文件
│   ├── public/            # 静态资源
│   └── package.json
├── server/                # Node.js后端应用
│   ├── src/
│   │   ├── routes/        # API路由
│   │   ├── controllers/   # 控制器
│   │   ├── middleware/    # 中间件
│   │   ├── utils/         # 工具函数
│   │   └── prisma/        # 数据库配置
│   └── package.json
└── README.md
```

## 🔧 开发命令

```bash
# 开发环境
npm run dev              # 同时启动前后端开发服务器
npm run client:dev       # 仅启动前端开发服务器
npm run server:dev       # 仅启动后端开发服务器

# 构建
npm run build           # 构建前端应用

# 数据库
npm run db:migrate      # 运行数据库迁移
npm run db:seed         # 填充种子数据
npm run db:studio       # 打开Prisma Studio数据库管理界面
```

## 📝 API文档

API文档将在开发服务器启动后可通过 http://localhost:3000/api-docs 访问

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情
