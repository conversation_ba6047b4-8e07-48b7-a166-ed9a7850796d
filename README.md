# 课程评价系统 (Course Evaluation System)

一个基于React和PostgreSQL的现代化教育课程评价平台，专为教师和学生设计的互动系统。

## 🚀 技术栈

### 前端技术
- **React 18** - 现代化用户界面库，组件化开发
- **Vite** - 快速构建工具，热重载开发体验
- **Ant Design** - 企业级UI组件库，中文友好
- **React Router** - 单页面应用路由管理
- **Zustand** - 轻量级状态管理，简单易用
- **Axios** - HTTP客户端，API请求处理

### 后端技术
- **Node.js** + **Express** - 高性能服务端框架
- **PostgreSQL** - 企业级关系型数据库
- **Prisma** - 现代化ORM工具，类型安全
- **JWT** - JSON Web Token身份认证
- **Multer** - 文件上传处理中间件
- **bcrypt** - 密码加密安全存储

## 📋 核心功能

### 👨‍🏫 教师端功能
- ✅ **课程管理** - 创建、编辑、删除课程信息
- ✅ **评价系统** - 创建富文本格式的详细评价
- ✅ **材料上传** - 支持PPT、PDF、Word等多种格式
- ✅ **灵活发布** - 批量或个人定向评价发布
- ✅ **公告管理** - 发布课程相关通知和公告
- ✅ **时间安排** - 管理课程时间表和课次安排
- ✅ **学生管理** - 查看选课学生信息和统计

### 👨‍🎓 学生端功能
- ✅ **个人评价** - 查看专属的课程评价记录
- ✅ **互动回复** - 对评价进行回复和反馈交流
- ✅ **课程信息** - 查看课程详情、公告和安排
- ✅ **材料下载** - 下载课程相关学习材料
- ✅ **隐私保护** - 只能访问自己的评价数据
- ✅ **学习统计** - 查看学习进度和评价统计

### 🔐 系统特性
- ✅ **安全认证** - JWT token身份验证机制
- ✅ **权限管理** - 基于角色的访问控制
- ✅ **中文界面** - 完全中文化的用户界面
- ✅ **响应式设计** - 支持桌面和移动设备
- ✅ **数据安全** - 数据库事务和错误处理

## 🛠️ 快速开始

### 环境要求
- **Node.js** 18.0+
- **PostgreSQL** 13.0+
- **npm** 或 **yarn**

### 方式一：一键启动（推荐）
```bash
# 克隆项目
git clone <repository-url>
cd course-evaluation-system

# 运行一键启动脚本
./start.sh
```

### 方式二：手动安装
```bash
# 1. 安装所有依赖
npm run install:all

# 2. 配置数据库
cp server/.env.example server/.env
# 编辑 server/.env 文件，配置数据库连接

# 3. 数据库设置
npm run db:migrate    # 运行数据库迁移
npm run db:seed       # 填充演示数据

# 4. 启动开发服务器
npm run dev
```

### 访问地址
- **前端应用**: http://localhost:5173
- **后端API**: http://localhost:3000
- **API文档**: http://localhost:3000/api

### 演示账号
- **教师账号**: `teacher` / `123456`
- **学生账号**: `student` / `123456`
- **管理员**: `admin` / `admin123`

## 📁 项目结构

```
course-evaluation-system/
├── client/                    # React前端应用
│   ├── src/
│   │   ├── components/        # 可复用组件
│   │   ├── pages/            # 页面组件
│   │   │   ├── teacher/      # 教师页面
│   │   │   └── student/      # 学生页面
│   │   ├── layouts/          # 布局组件
│   │   ├── store/            # Zustand状态管理
│   │   ├── utils/            # 工具函数
│   │   └── styles/           # 样式文件
│   ├── public/               # 静态资源
│   ├── index.html            # HTML模板
│   ├── vite.config.js        # Vite配置
│   └── package.json          # 前端依赖
├── server/                   # Node.js后端应用
│   ├── src/
│   │   ├── routes/           # API路由
│   │   │   ├── auth.js       # 认证路由
│   │   │   ├── courses.js    # 课程路由
│   │   │   ├── evaluations.js # 评价路由
│   │   │   ├── users.js      # 用户路由
│   │   │   └── upload.js     # 文件上传路由
│   │   ├── middleware/       # 中间件
│   │   │   ├── auth.js       # 认证中间件
│   │   │   ├── errorHandler.js # 错误处理
│   │   │   └── notFound.js   # 404处理
│   │   └── index.js          # 服务器入口
│   ├── prisma/               # 数据库配置
│   │   ├── schema.prisma     # 数据库模型
│   │   └── seed.js           # 种子数据
│   ├── uploads/              # 文件上传目录
│   ├── .env                  # 环境变量
│   └── package.json          # 后端依赖
├── start.sh                  # 一键启动脚本
├── package.json              # 根目录配置
└── README.md                 # 项目文档
```

## 🔧 开发命令

```bash
# 开发环境
npm run dev              # 同时启动前后端开发服务器
npm run client:dev       # 仅启动前端开发服务器
npm run server:dev       # 仅启动后端开发服务器

# 构建部署
npm run build           # 构建前端应用
npm run client:build    # 仅构建前端
npm run server:start    # 启动生产环境后端

# 数据库管理
npm run db:migrate      # 运行数据库迁移
npm run db:seed         # 填充种子数据
npm run db:studio       # 打开Prisma Studio数据库管理界面

# 依赖管理
npm run install:all     # 安装所有依赖（前端+后端）
```

## 📊 数据库设计

### 核心数据表
- **users** - 用户表（教师、学生、管理员）
- **courses** - 课程表
- **enrollments** - 选课关系表
- **evaluations** - 评价表
- **replies** - 回复表
- **sessions** - 课次表
- **materials** - 课程材料表
- **announcements** - 公告表

### 关系设计
- 用户与课程：一对多（教师）、多对多（学生选课）
- 课程与评价：一对多
- 评价与回复：一对多
- 课程与材料：一对多

## 🔒 安全特性

- **密码加密** - 使用bcrypt进行密码哈希
- **JWT认证** - 无状态身份验证
- **权限控制** - 基于角色的访问控制
- **输入验证** - 服务端数据验证
- **文件安全** - 文件类型和大小限制
- **CORS配置** - 跨域请求安全控制

## 📱 界面预览

### 教师端界面
- **仪表盘** - 课程统计、最新评价、今日安排
- **课程管理** - 课程CRUD操作、学生管理
- **评价管理** - 创建评价、查看回复、互动交流
- **学生管理** - 学生信息、选课统计

### 学生端界面
- **学习概览** - 学习进度、最新评价、课程安排
- **我的评价** - 查看评价、回复互动
- **课程信息** - 课程详情、材料下载、公告查看

## 🚀 部署指南

### 开发环境
```bash
# 使用一键启动脚本
./start.sh
```

### 生产环境
```bash
# 1. 构建前端
npm run build

# 2. 配置生产环境变量
cp server/.env.example server/.env.production
# 编辑生产环境配置

# 3. 启动生产服务器
NODE_ENV=production npm run server:start
```

## 🔧 配置说明

### 环境变量配置
```env
# 数据库配置
DATABASE_URL="postgresql://user:password@localhost:5432/course_evaluation"

# JWT配置
JWT_SECRET="your-secret-key"
JWT_EXPIRES_IN="7d"

# 服务器配置
PORT=3000
NODE_ENV="development"

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH="./uploads"

# 前端URL
CLIENT_URL="http://localhost:5173"
```

## 📝 API接口文档

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息

### 课程接口
- `GET /api/courses` - 获取课程列表
- `POST /api/courses` - 创建课程
- `GET /api/courses/:id` - 获取课程详情
- `PUT /api/courses/:id` - 更新课程
- `DELETE /api/courses/:id` - 删除课程

### 评价接口
- `GET /api/evaluations` - 获取评价列表
- `POST /api/evaluations` - 创建评价
- `GET /api/evaluations/:id` - 获取评价详情
- `POST /api/evaluations/:id/reply` - 回复评价

### 用户接口
- `GET /api/users` - 获取用户列表
- `GET /api/users/students` - 获取学生列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息

### 文件接口
- `POST /api/upload/material` - 上传课程材料
- `POST /api/upload/avatar` - 上传头像
- `GET /api/upload/download/:id` - 下载文件

## 🤝 贡献指南

1. **Fork项目** - 点击右上角Fork按钮
2. **创建分支** - `git checkout -b feature/新功能`
3. **提交代码** - `git commit -m '添加新功能'`
4. **推送分支** - `git push origin feature/新功能`
5. **创建PR** - 在GitHub上创建Pull Request

### 开发规范
- 使用ESLint进行代码检查
- 遵循React Hooks最佳实践
- API接口需要添加适当的错误处理
- 数据库操作使用事务确保数据一致性

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. **查看文档** - 仔细阅读本README文档
2. **检查日志** - 查看控制台错误信息
3. **提交Issue** - 在GitHub上提交问题报告
4. **联系开发者** - 通过邮件联系技术支持

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**🎓 课程评价系统** - 让教育更有温度，让学习更有意义！

## 🔧 开发命令

```bash
# 开发环境
npm run dev              # 同时启动前后端开发服务器
npm run client:dev       # 仅启动前端开发服务器
npm run server:dev       # 仅启动后端开发服务器

# 构建部署
npm run build           # 构建前端应用
npm run client:build    # 仅构建前端
npm run server:start    # 启动生产环境后端

# 数据库管理
npm run db:migrate      # 运行数据库迁移
npm run db:seed         # 填充种子数据
npm run db:studio       # 打开Prisma Studio数据库管理界面

# 依赖管理
npm run install:all     # 安装所有依赖（前端+后端）
```

## 📊 数据库设计

### 核心数据表
- **users** - 用户表（教师、学生、管理员）
- **courses** - 课程表
- **enrollments** - 选课关系表
- **evaluations** - 评价表
- **replies** - 回复表
- **sessions** - 课次表
- **materials** - 课程材料表
- **announcements** - 公告表

### 关系设计
- 用户与课程：一对多（教师）、多对多（学生选课）
- 课程与评价：一对多
- 评价与回复：一对多
- 课程与材料：一对多

## 🔒 安全特性

- **密码加密** - 使用bcrypt进行密码哈希
- **JWT认证** - 无状态身份验证
- **权限控制** - 基于角色的访问控制
- **输入验证** - 服务端数据验证
- **文件安全** - 文件类型和大小限制
- **CORS配置** - 跨域请求安全控制

## 📱 界面预览

### 教师端界面
- **仪表盘** - 课程统计、最新评价、今日安排
- **课程管理** - 课程CRUD操作、学生管理
- **评价管理** - 创建评价、查看回复、互动交流
- **学生管理** - 学生信息、选课统计

### 学生端界面
- **学习概览** - 学习进度、最新评价、课程安排
- **我的评价** - 查看评价、回复互动
- **课程信息** - 课程详情、材料下载、公告查看

## 🚀 部署指南

### 开发环境
```bash
# 使用一键启动脚本
./start.sh
```

### 生产环境
```bash
# 1. 构建前端
npm run build

# 2. 配置生产环境变量
cp server/.env.example server/.env.production
# 编辑生产环境配置

# 3. 启动生产服务器
NODE_ENV=production npm run server:start
```

## 🔧 配置说明

### 环境变量配置
```env
# 数据库配置
DATABASE_URL="postgresql://user:password@localhost:5432/course_evaluation"

# JWT配置
JWT_SECRET="your-secret-key"
JWT_EXPIRES_IN="7d"

# 服务器配置
PORT=3000
NODE_ENV="development"

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH="./uploads"

# 前端URL
CLIENT_URL="http://localhost:5173"
```

## 📝 API接口文档

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息

### 课程接口
- `GET /api/courses` - 获取课程列表
- `POST /api/courses` - 创建课程
- `GET /api/courses/:id` - 获取课程详情
- `PUT /api/courses/:id` - 更新课程
- `DELETE /api/courses/:id` - 删除课程

### 评价接口
- `GET /api/evaluations` - 获取评价列表
- `POST /api/evaluations` - 创建评价
- `GET /api/evaluations/:id` - 获取评价详情
- `POST /api/evaluations/:id/reply` - 回复评价

### 用户接口
- `GET /api/users` - 获取用户列表
- `GET /api/users/students` - 获取学生列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息

### 文件接口
- `POST /api/upload/material` - 上传课程材料
- `POST /api/upload/avatar` - 上传头像
- `GET /api/upload/download/:id` - 下载文件

## 🤝 贡献指南

1. **Fork项目** - 点击右上角Fork按钮
2. **创建分支** - `git checkout -b feature/新功能`
3. **提交代码** - `git commit -m '添加新功能'`
4. **推送分支** - `git push origin feature/新功能`
5. **创建PR** - 在GitHub上创建Pull Request

### 开发规范
- 使用ESLint进行代码检查
- 遵循React Hooks最佳实践
- API接口需要添加适当的错误处理
- 数据库操作使用事务确保数据一致性

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. **查看文档** - 仔细阅读本README文档
2. **检查日志** - 查看控制台错误信息
3. **提交Issue** - 在GitHub上提交问题报告
4. **联系开发者** - 通过邮件联系技术支持

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**🎓 课程评价系统** - 让教育更有温度，让学习更有意义！
