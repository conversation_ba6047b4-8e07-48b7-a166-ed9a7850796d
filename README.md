# 🎌 日语教学管理平台 (Japanese Teaching Management Platform)

一个专为日语教师设计的现代化教学管理系统，基于React和PostgreSQL构建，提供完整的课程管理、学生管理和教学评价功能。

## 🚀 技术栈

### 前端技术
- **React 18** - 现代化用户界面库，组件化开发
- **Vite** - 快速构建工具，热重载开发体验
- **Ant Design** - 企业级UI组件库，中文友好
- **React Router** - 单页面应用路由管理
- **Zustand** - 轻量级状态管理，简单易用
- **Axios** - HTTP客户端，API请求处理

### 后端技术
- **Node.js** + **Express** - 高性能服务端框架
- **PostgreSQL** - 企业级关系型数据库
- **Prisma** - 现代化ORM工具，类型安全
- **JWT** - JSON Web Token身份认证
- **Multer** - 文件上传处理中间件
- **bcrypt** - 密码加密安全存储

## 🎯 系统定位

本系统专为**单一日语教师**设计，提供完整的日语教学管理解决方案：

- **单教师架构** - 专为个人日语教师量身定制
- **固定课程体系** - 预设11种日语课程类型
- **价格管理** - 每种课程类型都有固定价格体系
- **学生管理** - 完整的学生选课和学习跟踪
- **教学评价** - 支持富文本评价和师生互动

## 📚 日语课程体系

系统内置11种专业日语课程类型：

| 课程类型 | 价格 | 时长 | 说明 |
|---------|------|------|------|
| 🆓 自由会话 | 免费 | 30分钟 | 免费体验课程 |
| 🗣️ 口语课程 | ¥180 | 60分钟 | 日语口语训练 |
| 💼 商务日语 | ¥200 | 60分钟 | 商务场景应用 |
| ✍️ 写作课程 | ¥200 | 60分钟 | 日语写作技巧 |
| 📖 语法课程 | ¥200 | 60分钟 | 语法系统学习 |
| 🎯 JLPT N5-1 对策 | ¥220 | 60分钟 | 能力考试辅导 |
| 🎓 研究生面试对策 | ¥300 | 60分钟 | 升学面试指导 |
| 💼 求职就职面试 | ¥300 | 60分钟 | 就职面试训练 |
| 🎤 演讲指导 | ¥500 | 60分钟 | 演讲技巧培训 |
| 📝 高考日语 | ¥300 | 60分钟 | 高考日语辅导 |
| 🔧 其他课程 | 协商 | 60分钟 | 定制化课程 |

## 🚀 核心功能

### 👨‍🏫 教师功能
- ✅ **课程类型管理** - 管理11种固定日语课程类型
- ✅ **课程安排** - 创建和管理一对一课程时间表
- ✅ **学生管理** - 查看学生信息和选课情况
- ✅ **富文本评价** - 支持图片、格式化文本的详细评价
- ✅ **文件上传** - 上传课件、作业、音频等教学材料
- ✅ **批量评价** - 对课程所有学生发布统一评价
- ✅ **个性化评价** - 针对特定学生的个性化反馈
- ✅ **日历视图** - 直观的课程安排日历管理

### 👨‍🎓 学生功能
- ✅ **课程选择** - 从11种课程类型中选择学习内容
- ✅ **评价查看** - 查看教师的详细学习评价
- ✅ **双向交流** - 对教师评价进行回复和互动
- ✅ **课程信息** - 查看课程详情、时间安排
- ✅ **材料下载** - 下载教学课件和学习资料
- ✅ **学习进度** - 跟踪个人学习进度和统计
- ✅ **上课提醒** - 显示下次上课时间和安排

### 🔐 安全特性
- ✅ **验证码注册** - 只有持有验证码才能注册学生账号
- ✅ **自动登录识别** - 根据用户名自动识别教师/学生身份
- ✅ **手机号注册** - 使用手机号替代邮箱进行注册
- ✅ **昵称系统** - 支持昵称，无需真实姓名
- ✅ **权限控制** - 严格的数据访问权限管理

## 🛠️ 快速开始

### 环境要求
- **Node.js** 18.0+
- **PostgreSQL** 13.0+
- **npm** 或 **yarn**

### 方式一：一键启动（推荐）
```bash
# 克隆项目
git clone <repository-url>
cd course-evaluation-system

# 运行一键启动脚本
./start.sh
```

### 方式二：手动安装
```bash
# 1. 安装所有依赖
npm run install:all

# 2. 配置数据库
cp server/.env.example server/.env
# 编辑 server/.env 文件，配置数据库连接

# 3. 数据库设置
npm run db:migrate    # 运行数据库迁移
npm run db:seed       # 填充演示数据

# 4. 启动开发服务器
npm run dev
```

### 🎯 访问地址
- **前端应用**: http://localhost:5174
- **后端API**: http://localhost:3000
- **API文档**: http://localhost:3000/api

### 👤 演示账号
- **日语教师**: `teacher` / `teacher123`
- **学生账号**: `student1-5` / `123456`
- **注册验证码**: `JAPANESE2024`, `SENSEI123`, `NIHONGO456`, `STUDY789`

## 📁 项目结构

```
japanese-teaching-platform/
├── client/                    # React前端应用
│   ├── src/
│   │   ├── components/        # 可复用组件
│   │   ├── pages/            # 页面组件
│   │   │   ├── teacher/      # 教师页面
│   │   │   │   ├── Dashboard.jsx          # 教师仪表盘
│   │   │   │   ├── CourseManagement.jsx   # 课程管理
│   │   │   │   ├── SessionManagement.jsx  # 课程安排
│   │   │   │   ├── EvaluationManagement.jsx # 评价管理
│   │   │   │   └── StudentManagement.jsx  # 学生管理
│   │   │   └── student/      # 学生页面
│   │   │       ├── Dashboard.jsx          # 学生仪表盘
│   │   │       ├── MyEvaluations.jsx      # 我的评价
│   │   │       └── CourseInfo.jsx         # 课程信息
│   │   ├── layouts/          # 布局组件
│   │   ├── store/            # Zustand状态管理
│   │   ├── utils/            # 工具函数
│   │   └── styles/           # 样式文件
│   ├── public/               # 静态资源
│   ├── index.html            # HTML模板
│   ├── vite.config.js        # Vite配置
│   └── package.json          # 前端依赖
├── server/                   # Node.js后端应用
│   ├── src/
│   │   ├── routes/           # API路由
│   │   │   ├── auth.js       # 认证路由
│   │   │   ├── courses.js    # 课程路由
│   │   │   ├── sessions.js   # 课程安排路由
│   │   │   ├── evaluations.js # 评价路由
│   │   │   ├── users.js      # 用户路由
│   │   │   └── upload.js     # 文件上传路由
│   │   ├── middleware/       # 中间件
│   │   │   ├── auth.js       # 认证中间件
│   │   │   ├── errorHandler.js # 错误处理
│   │   │   └── notFound.js   # 404处理
│   │   └── index.js          # 服务器入口
│   ├── prisma/               # 数据库配置
│   │   ├── schema.prisma     # 数据库模型
│   │   ├── seed.js           # 原始种子数据
│   │   └── japanese-seed.js  # 日语教学种子数据
│   ├── uploads/              # 文件上传目录
│   ├── .env                  # 环境变量
│   └── package.json          # 后端依赖
├── start.sh                  # 一键启动脚本
├── check-system.sh           # 系统检查脚本
├── package.json              # 根目录配置
├── README.md                 # 项目文档
├── USAGE.md                  # 使用指南
└── PROJECT_SUMMARY.md        # 项目总结
```

## 🔧 开发命令

```bash
# 开发环境
npm run dev              # 同时启动前后端开发服务器
npm run client:dev       # 仅启动前端开发服务器
npm run server:dev       # 仅启动后端开发服务器

# 构建部署
npm run build           # 构建前端应用
npm run client:build    # 仅构建前端
npm run server:start    # 启动生产环境后端

# 数据库管理
npm run db:migrate      # 运行数据库迁移
npm run db:seed         # 填充种子数据
npm run db:studio       # 打开Prisma Studio数据库管理界面

# 依赖管理
npm run install:all     # 安装所有依赖（前端+后端）
```

## 📊 数据库设计

### 核心数据表
- **users** - 用户表（教师、学生、管理员）
- **courses** - 课程表
- **enrollments** - 选课关系表
- **evaluations** - 评价表
- **replies** - 回复表
- **sessions** - 课次表
- **materials** - 课程材料表
- **announcements** - 公告表

### 关系设计
- 用户与课程：一对多（教师）、多对多（学生选课）
- 课程与评价：一对多
- 评价与回复：一对多
- 课程与材料：一对多

## 🔒 安全特性

- **密码加密** - 使用bcrypt进行密码哈希
- **JWT认证** - 无状态身份验证
- **权限控制** - 基于角色的访问控制
- **输入验证** - 服务端数据验证
- **文件安全** - 文件类型和大小限制
- **CORS配置** - 跨域请求安全控制

## 📱 界面预览

### 教师端界面
- **仪表盘** - 课程统计、最新评价、今日安排
- **课程管理** - 课程CRUD操作、学生管理
- **评价管理** - 创建评价、查看回复、互动交流
- **学生管理** - 学生信息、选课统计

### 学生端界面
- **学习概览** - 学习进度、最新评价、课程安排
- **我的评价** - 查看评价、回复互动
- **课程信息** - 课程详情、材料下载、公告查看

## 🚀 部署指南

### 开发环境
```bash
# 使用一键启动脚本
./start.sh
```

### 生产环境
```bash
# 1. 构建前端
npm run build

# 2. 配置生产环境变量
cp server/.env.example server/.env.production
# 编辑生产环境配置

# 3. 启动生产服务器
NODE_ENV=production npm run server:start
```

## 🔧 配置说明

### 环境变量配置
```env
# 数据库配置
DATABASE_URL="postgresql://user:password@localhost:5432/course_evaluation"

# JWT配置
JWT_SECRET="your-secret-key"
JWT_EXPIRES_IN="7d"

# 服务器配置
PORT=3000
NODE_ENV="development"

# 文件上传配置
MAX_FILE_SIZE=10485760  # 10MB
UPLOAD_PATH="./uploads"

# 前端URL
CLIENT_URL="http://localhost:5173"
```

## 📝 API接口文档

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/auth/me` - 获取当前用户信息

### 日语课程接口
- `GET /api/courses` - 获取日语课程类型列表
- `POST /api/courses` - 创建新的课程类型
- `GET /api/courses/:id` - 获取课程类型详情
- `PUT /api/courses/:id` - 更新课程类型信息
- `DELETE /api/courses/:id` - 删除课程类型

### 课程安排接口
- `GET /api/sessions` - 获取课程安排列表
- `POST /api/sessions` - 创建新的课程安排
- `GET /api/sessions/:id` - 获取课程安排详情
- `PUT /api/sessions/:id` - 更新课程安排
- `DELETE /api/sessions/:id` - 删除课程安排

### 评价接口
- `GET /api/evaluations` - 获取评价列表
- `POST /api/evaluations` - 创建评价
- `GET /api/evaluations/:id` - 获取评价详情
- `POST /api/evaluations/:id/reply` - 回复评价

### 用户接口
- `GET /api/users` - 获取用户列表
- `GET /api/users/students` - 获取学生列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息

### 文件接口
- `POST /api/upload/material` - 上传课程材料
- `POST /api/upload/avatar` - 上传头像
- `GET /api/upload/download/:id` - 下载文件

## 🤝 贡献指南

1. **Fork项目** - 点击右上角Fork按钮
2. **创建分支** - `git checkout -b feature/新功能`
3. **提交代码** - `git commit -m '添加新功能'`
4. **推送分支** - `git push origin feature/新功能`
5. **创建PR** - 在GitHub上创建Pull Request

### 开发规范
- 使用ESLint进行代码检查
- 遵循React Hooks最佳实践
- API接口需要添加适当的错误处理
- 数据库操作使用事务确保数据一致性

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. **查看文档** - 仔细阅读本README文档
2. **检查日志** - 查看控制台错误信息
3. **提交Issue** - 在GitHub上提交问题报告
4. **联系开发者** - 通过邮件联系技术支持

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

---

**🎌 日语教学管理平台** - 让日语学习更专业，让教学管理更高效！
