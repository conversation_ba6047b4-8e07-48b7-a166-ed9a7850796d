#!/bin/bash

# 教育评价平台 Docker 部署脚本
# 使用方法: ./deploy.sh [dev|prod|stop|logs|restart]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 检查环境变量文件
check_env_file() {
    local env_file=$1
    if [ ! -f "$env_file" ]; then
        log_error "环境变量文件 $env_file 不存在"
        log_info "请复制 ${env_file}.example 并填入正确的配置"
        exit 1
    fi
}

# 部署生产环境
deploy_prod() {
    log_info "开始部署生产环境..."
    
    check_env_file ".env.production"
    
    # 停止现有容器
    docker-compose down
    
    # 构建并启动生产环境
    docker-compose up -d --build
    
    log_success "生产环境部署完成！"
    log_info "应用运行在: http://localhost:3000"
    log_info "查看日志: docker-compose logs -f"
}

# 启动开发环境
deploy_dev() {
    log_info "启动开发环境..."

    check_env_file ".env.local"

    # 停止现有容器
    docker-compose --profile dev down

    # 启动开发环境
    log_info "启动应用容器..."
    docker-compose --profile dev up -d --build app-dev

    log_success "开发环境启动完成！"
    log_info "应用运行在: http://localhost:3001"
    log_info "连接到您的外部 PostgreSQL 数据库"
    log_info "支持热重载，修改代码会自动更新"
}

# 停止所有服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose --profile dev --profile nginx down
    log_success "所有服务已停止"
}

# 查看日志
show_logs() {
    log_info "显示应用日志..."
    docker-compose logs -f app
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    docker-compose restart
    log_success "服务重启完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "应用运行正常"
    else
        log_error "应用无法访问"
        log_info "检查容器状态: docker-compose ps"
        log_info "查看日志: docker-compose logs app"
    fi
}

# 清理资源
cleanup() {
    log_info "清理 Docker 资源..."
    docker-compose down --volumes --remove-orphans
    docker system prune -f
    log_success "清理完成"
}

# 主函数
main() {
    check_docker
    
    case "${1:-prod}" in
        "prod")
            deploy_prod
            ;;
        "dev")
            deploy_dev
            ;;
        "stop")
            stop_services
            ;;
        "logs")
            show_logs
            ;;
        "restart")
            restart_services
            ;;
        "health")
            health_check
            ;;
        "cleanup")
            cleanup
            ;;
        *)
            echo "使用方法: $0 [prod|dev|stop|logs|restart|health|cleanup]"
            echo ""
            echo "命令说明:"
            echo "  prod     - 部署生产环境 (默认)"
            echo "  dev      - 启动开发环境"
            echo "  stop     - 停止所有服务"
            echo "  logs     - 查看应用日志"
            echo "  restart  - 重启服务"
            echo "  health   - 健康检查"
            echo "  cleanup  - 清理 Docker 资源"
            exit 1
            ;;
    esac
}

main "$@"
