import { UserModel, CourseModel, EvaluationModel, EnrollmentModel } from './models'
import { testConnection } from './database'

// 检查是否使用模拟数据
export const isUsingMockData = !process.env.DATABASE_URL || process.env.NODE_ENV === 'development'

// 带超时的数据库操作包装器
async function withTimeout<T>(operation: () => Promise<T>, timeoutMs: number = 5000): Promise<T> {
  return Promise.race([
    operation(),
    new Promise<T>((_, reject) =>
      setTimeout(() => reject(new Error(`操作超时 (${timeoutMs}ms)`)), timeoutMs)
    )
  ])
}

// 用户相关数据访问
export const userService = {
  async findById(id: string) {
    try {
      if (isUsingMockData) {
        return mockData.users.find(u => u.id === id) || null
      }
      return await withTimeout(() => UserModel.findById(id))
    } catch (error) {
      console.error('用户查询失败:', error)
      return mockData.users.find(u => u.id === id) || null
    }
  },

  async findByEmail(email: string) {
    try {
      if (isUsingMockData) {
        return mockData.users.find(u => u.email === email) || null
      }
      return await withTimeout(() => UserModel.findByEmail(email))
    } catch (error) {
      console.error('用户邮箱查询失败:', error)
      return mockData.users.find(u => u.email === email) || null
    }
  },

  async create(userData: {
    email: string
    name: string
    role: 'teacher' | 'student' | 'admin'
  }) {
    try {
      if (isUsingMockData) {
        const newUser = { ...userData, id: Date.now().toString(), created_at: new Date().toISOString() }
        mockData.users.push(newUser)
        return newUser
      }
      return await withTimeout(() => UserModel.create(userData))
    } catch (error) {
      console.error('用户创建失败:', error)
      throw error
    }
  },

  async update(id: string, userData: Partial<{
    name: string
    role: 'teacher' | 'student' | 'admin'
  }>) {
    try {
      if (isUsingMockData) {
        const userIndex = mockData.users.findIndex(u => u.id === id)
        if (userIndex >= 0) {
          mockData.users[userIndex] = { ...mockData.users[userIndex], ...userData }
          return mockData.users[userIndex]
        }
        return null
      }
      return await withTimeout(() => UserModel.update(id, userData))
    } catch (error) {
      console.error('用户更新失败:', error)
      throw error
    }
  }
}

// 课程相关数据访问
export const courseService = {
  async findAll(teacherId?: string) {
    try {
      if (isUsingMockData) {
        return teacherId
          ? mockData.courses.filter(c => c.teacher_id === teacherId)
          : mockData.courses
      }
      return await withTimeout(() => CourseModel.findAll(teacherId))
    } catch (error) {
      console.error('课程查询失败:', error)
      return teacherId
        ? mockData.courses.filter(c => c.teacher_id === teacherId)
        : mockData.courses
    }
  },

  async findById(id: string) {
    try {
      if (isUsingMockData) {
        return mockData.courses.find(c => c.id === id) || null
      }
      return await withTimeout(() => CourseModel.findById(id))
    } catch (error) {
      console.error('课程详情查询失败:', error)
      return mockData.courses.find(c => c.id === id) || null
    }
  },

  async create(courseData: {
    name: string
    description?: string
    teacher_id: string
  }) {
    try {
      if (isUsingMockData) {
        const newCourse = {
          ...courseData,
          id: Date.now().toString(),
          created_at: new Date().toISOString()
        }
        mockData.courses.push(newCourse)
        return newCourse
      }
      return await withTimeout(() => CourseModel.create(courseData))
    } catch (error) {
      console.error('课程创建失败:', error)
      throw error
    }
  },

  async findByStudentId(studentId: string) {
    try {
      if (isUsingMockData) {
        // 模拟学生选课数据
        return mockData.courses.slice(0, 2) // 返回前两个课程作为示例
      }
      return await withTimeout(() => CourseModel.findByStudentId(studentId))
    } catch (error) {
      console.error('学生课程查询失败:', error)
      return mockData.courses.slice(0, 2)
    }
  },

  // 获取课程统计信息
  async getStats(teacherId: string) {
    try {
      if (isUsingMockData) {
        const courses = mockData.courses.filter(c => c.teacher_id === teacherId)
        return {
          coursesCount: courses.length,
          totalStudents: courses.reduce((sum, c) => sum + (c.student_count || 0), 0),
        }
      }
      const courses = await withTimeout(() => CourseModel.findAll(teacherId))
      return {
        coursesCount: courses.length,
        totalStudents: 0, // 需要从 enrollments 表计算
      }
    } catch (error) {
      console.error('课程统计查询失败:', error)
      return {
        coursesCount: 2,
        totalStudents: 43,
      }
    }
  }
}

// 评价相关数据访问
export const evaluationService = {
  async findAll(filters?: {
    courseId?: string
    teacherId?: string
    studentId?: string
  }) {
    try {
      if (isUsingMockData) {
        let result = mockData.evaluations
        if (filters?.teacherId) {
          result = result.filter(e => e.teacher_id === filters.teacherId)
        }
        if (filters?.courseId) {
          result = result.filter(e => e.course_id === filters.courseId)
        }
        return result
      }
      return await withTimeout(() => EvaluationModel.findAll(filters))
    } catch (error) {
      console.error('评价查询失败:', error)
      return mockData.evaluations
    }
  },

  async create(evaluationData: {
    course_id: string
    teacher_id: string
    title: string
    content: string
    target_type: 'all' | 'individual' | 'group'
    session_id?: string
  }) {
    try {
      if (isUsingMockData) {
        const newEvaluation = {
          ...evaluationData,
          id: Date.now().toString(),
          created_at: new Date().toISOString(),
          courses: { name: '模拟课程' },
          users: { name: '模拟教师' },
          course_sessions: null,
          course_materials: [],
          evaluation_responses: [],
        }
        mockData.evaluations.push(newEvaluation)
        return newEvaluation
      }
      return await withTimeout(() => EvaluationModel.create(evaluationData))
    } catch (error) {
      console.error('评价创建失败:', error)
      throw error
    }
  },

  // 获取评价统计信息
  async getStats(teacherId: string) {
    try {
      if (isUsingMockData) {
        const evaluations = mockData.evaluations.filter(e => e.teacher_id === teacherId)
        return {
          evaluationsCount: evaluations.length,
        }
      }
      const evaluations = await withTimeout(() => EvaluationModel.findAll({ teacherId }))
      return {
        evaluationsCount: evaluations.length,
      }
    } catch (error) {
      console.error('评价统计查询失败:', error)
      return {
        evaluationsCount: 5,
      }
    }
  }
}

// 选课相关数据访问
export const enrollmentService = {
  async enroll(studentId: string, courseId: string) {
    try {
      if (isUsingMockData) {
        console.log(`模拟选课: 学生 ${studentId} 选择课程 ${courseId}`)
        return { success: true }
      }
      return await withTimeout(() => EnrollmentModel.enroll(studentId, courseId))
    } catch (error) {
      console.error('选课失败:', error)
      throw error
    }
  },

  async unenroll(studentId: string, courseId: string) {
    try {
      if (isUsingMockData) {
        console.log(`模拟退课: 学生 ${studentId} 退出课程 ${courseId}`)
        return { success: true }
      }
      return await withTimeout(() => EnrollmentModel.unenroll(studentId, courseId))
    } catch (error) {
      console.error('退课失败:', error)
      throw error
    }
  },

  async getStudentsByCourse(courseId: string) {
    try {
      if (isUsingMockData) {
        // 返回模拟学生数据
        return [
          { id: '1', name: '张三', email: '<EMAIL>' },
          { id: '2', name: '李四', email: '<EMAIL>' },
        ]
      }
      return await withTimeout(() => EnrollmentModel.getStudentsByCourse(courseId))
    } catch (error) {
      console.error('课程学生查询失败:', error)
      return []
    }
  },

  // 获取学生统计信息
  async getStudentStats(teacherId: string) {
    try {
      if (isUsingMockData) {
        return {
          studentsCount: 43,
        }
      }

      const courses = await withTimeout(() => CourseModel.findAll(teacherId))
      let totalStudents = 0

      for (const course of courses) {
        const students = await withTimeout(() => EnrollmentModel.getStudentsByCourse(course.id))
        totalStudents += students.length
      }

      return {
        studentsCount: totalStudents,
      }
    } catch (error) {
      console.error('学生统计查询失败:', error)
      return {
        studentsCount: 43,
      }
    }
  }
}

// 模拟数据（用于开发测试）
export const mockData = {
  // 模拟课程数据
  courses: [
    {
      id: "660e8400-e29b-41d4-a716-446655440001",
      name: "高等数学",
      description: "大学高等数学课程，包含微积分、线性代数等内容",
      teacher_id: "550e8400-e29b-41d4-a716-446655440001",
      teacher_name: "张老师",
      student_count: 25,
      session_count: 12,
      created_at: "2024-03-01T00:00:00Z",
    },
    {
      id: "660e8400-e29b-41d4-a716-446655440002",
      name: "计算机程序设计",
      description: "Python编程基础课程",
      teacher_id: "550e8400-e29b-41d4-a716-446655440001",
      teacher_name: "张老师",
      student_count: 18,
      session_count: 8,
      created_at: "2024-03-01T00:00:00Z",
    },
  ],

  // 模拟评价数据
  evaluations: [
    {
      id: "770e8400-e29b-41d4-a716-446655440001",
      title: "第一次课程评价",
      content: "**课程表现总结**\n\n本次课程中，同学们的表现整体良好。\n\n- 积极参与课堂讨论\n- 按时完成作业\n- 需要加强理论理解",
      course_id: "660e8400-e29b-41d4-a716-446655440001",
      teacher_id: "550e8400-e29b-41d4-a716-446655440001",
      target_type: "all",
      created_at: new Date().toISOString(),
      courses: { name: "高等数学" },
      users: { name: "张老师" },
      course_sessions: null,
      course_materials: [],
      evaluation_responses: [],
    },
  ],

  // 模拟用户数据
  users: [
    {
      id: "550e8400-e29b-41d4-a716-446655440001",
      email: "<EMAIL>",
      name: "张老师",
      role: "teacher",
      created_at: "2024-01-01T00:00:00Z",
    },
  ],
}

// 检查是否使用模拟数据（当数据库连接不可用时）
const checkMockDataMode = () => {
  const hasDbUrl = !!process.env.DATABASE_URL
  console.log(`🔍 数据库模式检查: DATABASE_URL=${hasDbUrl ? '已设置' : '未设置'}`)
  return !hasDbUrl
}

export const isUsingMockData = checkMockDataMode()

// 统一的数据访问接口
export const dataService = {
  user: userService,
  course: courseService,
  evaluation: evaluationService,
  enrollment: enrollmentService,
  mock: mockData,
  isUsingMockData,
}
