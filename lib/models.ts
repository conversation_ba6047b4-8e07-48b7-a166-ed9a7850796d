import { query, transaction, cacheGet, cacheSet } from './database'

// 用户相关操作
export class UserModel {
  // 获取用户信息
  static async findById(id: string) {
    const cacheKey = `user:${id}`
    
    // 先尝试从缓存获取
    const cached = await cacheGet(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }
    
    const result = await query(
      'SELECT id, email, name, role, created_at, updated_at FROM users WHERE id = $1',
      [id]
    )
    
    if (result.rows.length > 0) {
      const user = result.rows[0]
      // 缓存用户信息 1 小时
      await cacheSet(cacheKey, JSON.stringify(user), 3600)
      return user
    }
    
    return null
  }
  
  // 根据邮箱查找用户
  static async findByEmail(email: string) {
    const result = await query(
      'SELECT id, email, name, role, created_at, updated_at FROM users WHERE email = $1',
      [email]
    )
    
    return result.rows.length > 0 ? result.rows[0] : null
  }
  
  // 创建用户
  static async create(userData: {
    email: string
    name: string
    role: 'teacher' | 'student' | 'admin'
  }) {
    const result = await query(
      `INSERT INTO users (email, name, role) 
       VALUES ($1, $2, $3) 
       RETURNING id, email, name, role, created_at, updated_at`,
      [userData.email, userData.name, userData.role]
    )
    
    return result.rows[0]
  }
  
  // 更新用户信息
  static async update(id: string, userData: Partial<{
    name: string
    role: 'teacher' | 'student' | 'admin'
  }>) {
    const fields = []
    const values = []
    let paramIndex = 1
    
    if (userData.name) {
      fields.push(`name = $${paramIndex++}`)
      values.push(userData.name)
    }
    
    if (userData.role) {
      fields.push(`role = $${paramIndex++}`)
      values.push(userData.role)
    }
    
    fields.push(`updated_at = NOW()`)
    values.push(id)
    
    const result = await query(
      `UPDATE users SET ${fields.join(', ')} WHERE id = $${paramIndex} 
       RETURNING id, email, name, role, created_at, updated_at`,
      values
    )
    
    return result.rows[0]
  }
}

// 课程相关操作
export class CourseModel {
  // 获取课程列表
  static async findAll(teacherId?: string) {
    let sql = `
      SELECT c.*, u.name as teacher_name 
      FROM courses c 
      LEFT JOIN users u ON c.teacher_id = u.id
    `
    const params = []
    
    if (teacherId) {
      sql += ' WHERE c.teacher_id = $1'
      params.push(teacherId)
    }
    
    sql += ' ORDER BY c.created_at DESC'
    
    const result = await query(sql, params)
    return result.rows
  }
  
  // 获取单个课程
  static async findById(id: string) {
    const result = await query(
      `SELECT c.*, u.name as teacher_name 
       FROM courses c 
       LEFT JOIN users u ON c.teacher_id = u.id 
       WHERE c.id = $1`,
      [id]
    )
    
    return result.rows.length > 0 ? result.rows[0] : null
  }
  
  // 创建课程
  static async create(courseData: {
    name: string
    description?: string
    teacher_id: string
  }) {
    const result = await query(
      `INSERT INTO courses (name, description, teacher_id) 
       VALUES ($1, $2, $3) 
       RETURNING *`,
      [courseData.name, courseData.description, courseData.teacher_id]
    )
    
    return result.rows[0]
  }
  
  // 获取学生的课程列表
  static async findByStudentId(studentId: string) {
    const result = await query(
      `SELECT c.*, u.name as teacher_name, e.enrolled_at
       FROM courses c
       LEFT JOIN users u ON c.teacher_id = u.id
       INNER JOIN enrollments e ON c.id = e.course_id
       WHERE e.student_id = $1
       ORDER BY e.enrolled_at DESC`,
      [studentId]
    )
    
    return result.rows
  }
}

// 评价相关操作
export class EvaluationModel {
  // 获取评价列表
  static async findAll(filters?: {
    courseId?: string
    teacherId?: string
    studentId?: string
  }) {
    let sql = `
      SELECT e.*, c.name as course_name, u.name as teacher_name
      FROM evaluations e
      LEFT JOIN courses c ON e.course_id = c.id
      LEFT JOIN users u ON e.teacher_id = u.id
    `
    
    const conditions = []
    const params = []
    let paramIndex = 1
    
    if (filters?.courseId) {
      conditions.push(`e.course_id = $${paramIndex++}`)
      params.push(filters.courseId)
    }
    
    if (filters?.teacherId) {
      conditions.push(`e.teacher_id = $${paramIndex++}`)
      params.push(filters.teacherId)
    }
    
    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ')
    }
    
    sql += ' ORDER BY e.created_at DESC'
    
    const result = await query(sql, params)
    return result.rows
  }
  
  // 创建评价
  static async create(evaluationData: {
    course_id: string
    teacher_id: string
    title: string
    content: string
    target_type: 'all' | 'individual' | 'group'
    session_id?: string
  }) {
    return await transaction(async (client) => {
      // 创建评价
      const evaluationResult = await client.query(
        `INSERT INTO evaluations (course_id, teacher_id, title, content, target_type, session_id) 
         VALUES ($1, $2, $3, $4, $5, $6) 
         RETURNING *`,
        [
          evaluationData.course_id,
          evaluationData.teacher_id,
          evaluationData.title,
          evaluationData.content,
          evaluationData.target_type,
          evaluationData.session_id
        ]
      )
      
      const evaluation = evaluationResult.rows[0]
      
      // 如果是针对所有学生的评价，创建评价目标记录
      if (evaluationData.target_type === 'all') {
        const studentsResult = await client.query(
          `SELECT student_id FROM enrollments WHERE course_id = $1`,
          [evaluationData.course_id]
        )
        
        for (const student of studentsResult.rows) {
          await client.query(
            `INSERT INTO evaluation_targets (evaluation_id, student_id) VALUES ($1, $2)`,
            [evaluation.id, student.student_id]
          )
        }
      }
      
      return evaluation
    })
  }
}

// 选课相关操作
export class EnrollmentModel {
  // 学生选课
  static async enroll(studentId: string, courseId: string) {
    try {
      const result = await query(
        `INSERT INTO enrollments (student_id, course_id) 
         VALUES ($1, $2) 
         RETURNING *`,
        [studentId, courseId]
      )
      
      return result.rows[0]
    } catch (error: any) {
      // 处理重复选课错误
      if (error.code === '23505') { // unique_violation
        throw new Error('已经选过这门课程')
      }
      throw error
    }
  }
  
  // 退课
  static async unenroll(studentId: string, courseId: string) {
    const result = await query(
      `DELETE FROM enrollments 
       WHERE student_id = $1 AND course_id = $2 
       RETURNING *`,
      [studentId, courseId]
    )
    
    return result.rowCount > 0
  }
  
  // 获取课程的学生列表
  static async getStudentsByCourse(courseId: string) {
    const result = await query(
      `SELECT u.id, u.name, u.email, e.enrolled_at
       FROM users u
       INNER JOIN enrollments e ON u.id = e.student_id
       WHERE e.course_id = $1 AND u.role = 'student'
       ORDER BY e.enrolled_at DESC`,
      [courseId]
    )
    
    return result.rows
  }
}
