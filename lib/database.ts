import { Pool, PoolClient } from 'pg'
import { createClient } from 'redis'

// PostgreSQL 连接池
let pool: Pool | null = null

// Redis 客户端
let redisClient: any = null

// 获取数据库连接池
export function getPool(): Pool {
  if (!pool) {
    const databaseUrl = process.env.DATABASE_URL

    if (!databaseUrl) {
      console.warn('⚠️ DATABASE_URL 环境变量未设置，数据库功能将不可用')
      // 返回一个模拟的连接池，避免应用崩溃
      return null as any
    }

    console.log('🔗 正在连接数据库:', databaseUrl.replace(/:[^:@]*@/, ':***@'))

    pool = new Pool({
      connectionString: databaseUrl,
      max: 10, // 减少最大连接数
      idleTimeoutMillis: 30000, // 空闲连接超时时间
      connectionTimeoutMillis: 5000, // 增加连接超时时间到 5 秒
      statement_timeout: 10000, // 查询超时时间 10 秒
    })

    // 监听连接池事件
    pool.on('connect', () => {
      console.log('✅ PostgreSQL 连接池已连接')
    })

    pool.on('error', (err) => {
      console.error('❌ PostgreSQL 连接池错误:', err)
    })
  }

  return pool
}

// 测试数据库连接
export async function testConnection(): Promise<boolean> {
  try {
    const pool = getPool()
    if (!pool) {
      console.log('⚠️ 数据库连接池未初始化，使用模拟数据')
      return false
    }

    const client = await pool.connect()
    const result = await client.query('SELECT NOW() as current_time')
    client.release()

    console.log('✅ 数据库连接测试成功:', result.rows[0].current_time)
    return true
  } catch (error) {
    console.error('❌ 数据库连接测试失败:', error)
    return false
  }
}

// 获取数据库连接
export async function getConnection(): Promise<PoolClient> {
  const pool = getPool()
  return await pool.connect()
}

// 执行查询
export async function query(text: string, params?: any[]): Promise<any> {
  const pool = getPool()
  const start = Date.now()
  
  try {
    const result = await pool.query(text, params)
    const duration = Date.now() - start
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 SQL 查询:', { text, duration: `${duration}ms`, rows: result.rowCount })
    }
    
    return result
  } catch (error) {
    console.error('❌ SQL 查询错误:', error)
    throw error
  }
}

// 事务执行
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const client = await getConnection()
  
  try {
    await client.query('BEGIN')
    const result = await callback(client)
    await client.query('COMMIT')
    return result
  } catch (error) {
    await client.query('ROLLBACK')
    throw error
  } finally {
    client.release()
  }
}

// 获取 Redis 客户端
export async function getRedisClient() {
  if (!redisClient) {
    const redisUrl = process.env.REDIS_URL
    
    if (redisUrl) {
      redisClient = createClient({
        url: redisUrl
      })
      
      redisClient.on('error', (err: any) => {
        console.error('❌ Redis 连接错误:', err)
      })
      
      redisClient.on('connect', () => {
        console.log('✅ Redis 已连接')
      })
      
      await redisClient.connect()
    }
  }
  
  return redisClient
}

// 缓存辅助函数
export async function cacheGet(key: string): Promise<string | null> {
  try {
    const client = await getRedisClient()
    if (client) {
      return await client.get(key)
    }
  } catch (error) {
    console.error('Redis GET 错误:', error)
  }
  return null
}

export async function cacheSet(key: string, value: string, ttl: number = 3600): Promise<void> {
  try {
    const client = await getRedisClient()
    if (client) {
      await client.setEx(key, ttl, value)
    }
  } catch (error) {
    console.error('Redis SET 错误:', error)
  }
}

export async function cacheDel(key: string): Promise<void> {
  try {
    const client = await getRedisClient()
    if (client) {
      await client.del(key)
    }
  } catch (error) {
    console.error('Redis DEL 错误:', error)
  }
}

// 数据库健康检查
export async function healthCheck(): Promise<{ database: boolean; redis: boolean }> {
  const result = { database: false, redis: false }
  
  // 检查 PostgreSQL
  try {
    await query('SELECT 1')
    result.database = true
  } catch (error) {
    console.error('PostgreSQL 健康检查失败:', error)
  }
  
  // 检查 Redis
  try {
    const client = await getRedisClient()
    if (client) {
      await client.ping()
      result.redis = true
    }
  } catch (error) {
    console.error('Redis 健康检查失败:', error)
  }
  
  return result
}

// 关闭连接
export async function closeConnections(): Promise<void> {
  if (pool) {
    await pool.end()
    pool = null
    console.log('✅ PostgreSQL 连接池已关闭')
  }
  
  if (redisClient) {
    await redisClient.quit()
    redisClient = null
    console.log('✅ Redis 连接已关闭')
  }
}
