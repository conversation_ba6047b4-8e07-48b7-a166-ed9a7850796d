export interface User {
  id: string
  email: string
  name: string
  role: "teacher" | "student" | "admin"
}

export async function signOut() {
  // 清除localStorage中的用户数据
  localStorage.removeItem("currentUser")
  // 触发自定义事件通知AuthProvider
  window.dispatchEvent(new CustomEvent("userLogout"))
}

export async function getCurrentUser(): Promise<User | null> {
  // 从localStorage获取当前用户
  const userData = localStorage.getItem("currentUser")
  if (userData) {
    return JSON.parse(userData)
  }
  return null
}
