export interface Notification {
  id: string
  user_id: string
  title: string
  message: string
  type: "enrollment_request" | "enrollment_approved" | "enrollment_rejected" | "general"
  read: boolean
  related_id?: string
  created_at: string
}

export interface EnrollmentRequest {
  id: string
  student_id: string
  course_id: string
  status: "pending" | "approved" | "rejected"
  application_message?: string
  teacher_response?: string
  requested_at: string
  reviewed_at?: string
  reviewed_by?: string
  student_name?: string
  student_email?: string
  course_name?: string
  teacher_name?: string
}

// Mock notification service for testing
export class NotificationService {
  private static notifications: Notification[] = [
    {
      id: "notif-1",
      user_id: "550e8400-e29b-41d4-a716-446655440001",
      title: "新的选课申请",
      message: "学生王小明申请选修您的英语写作课程",
      type: "enrollment_request",
      read: false,
      related_id: "550e8400-e29b-41d4-a716-446655440003",
      created_at: new Date(Date.now() - 86400000 * 2).toISOString(),
    },
    {
      id: "notif-2",
      user_id: "550e8400-e29b-41d4-a716-446655440001",
      title: "新的选课申请",
      message: "学生陈小红申请选修您的计算机程序设计课程",
      type: "enrollment_request",
      read: false,
      related_id: "550e8400-e29b-41d4-a716-446655440004",
      created_at: new Date(Date.now() - 86400000).toISOString(),
    },
    {
      id: "notif-3",
      user_id: "550e8400-e29b-41d4-a716-446655440003",
      title: "选课申请已批准",
      message: "您申请的高等数学课程已被批准",
      type: "enrollment_approved",
      read: false,
      related_id: "660e8400-e29b-41d4-a716-446655440001",
      created_at: new Date(Date.now() - 86400000 * 3).toISOString(),
    },
  ]

  private static enrollmentRequests: EnrollmentRequest[] = [
    {
      id: "req-1",
      student_id: "550e8400-e29b-41d4-a716-446655440003",
      course_id: "660e8400-e29b-41d4-a716-446655440003",
      status: "pending",
      application_message: "我对英语写作很感兴趣，希望能够提升我的英语表达能力。",
      requested_at: new Date(Date.now() - 86400000 * 2).toISOString(),
      student_name: "王小明",
      student_email: "<EMAIL>",
      course_name: "英语写作",
      teacher_name: "李老师",
    },
    {
      id: "req-2",
      student_id: "550e8400-e29b-41d4-a716-446655440004",
      course_id: "660e8400-e29b-41d4-a716-446655440002",
      status: "pending",
      application_message: "我想学习编程，希望老师能够批准我的申请。",
      requested_at: new Date(Date.now() - 86400000).toISOString(),
      student_name: "陈小红",
      student_email: "<EMAIL>",
      course_name: "计算机程序设计",
      teacher_name: "张老师",
    },
    {
      id: "req-3",
      student_id: "550e8400-e29b-41d4-a716-446655440005",
      course_id: "660e8400-e29b-41d4-a716-446655440001",
      status: "approved",
      application_message: "我的数学基础还不错，希望能够进一步学习高等数学。",
      requested_at: new Date(Date.now() - 86400000 * 3).toISOString(),
      reviewed_at: new Date(Date.now() - 86400000 * 2).toISOString(),
      reviewed_by: "550e8400-e29b-41d4-a716-446655440001",
      student_name: "刘小强",
      student_email: "<EMAIL>",
      course_name: "高等数学",
      teacher_name: "张老师",
    },
  ]

  static async getNotifications(userId: string): Promise<Notification[]> {
    return this.notifications
      .filter((n) => n.user_id === userId)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
  }

  static async markAsRead(notificationId: string): Promise<void> {
    const notification = this.notifications.find((n) => n.id === notificationId)
    if (notification) {
      notification.read = true
    }
  }

  static async createNotification(notification: Omit<Notification, "id" | "created_at">): Promise<void> {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}`,
      created_at: new Date().toISOString(),
    }
    this.notifications.push(newNotification)
  }

  static async getEnrollmentRequests(teacherId?: string, studentId?: string): Promise<EnrollmentRequest[]> {
    let filtered = this.enrollmentRequests

    if (teacherId) {
      // Filter by teacher's courses (mock logic)
      const teacherCourses = ["660e8400-e29b-41d4-a716-446655440001", "660e8400-e29b-41d4-a716-446655440002"]
      filtered = filtered.filter((req) => teacherCourses.includes(req.course_id))
    }

    if (studentId) {
      filtered = filtered.filter((req) => req.student_id === studentId)
    }

    return filtered.sort((a, b) => new Date(b.requested_at).getTime() - new Date(a.requested_at).getTime())
  }

  static async createEnrollmentRequest(
    request: Omit<EnrollmentRequest, "id" | "requested_at">,
  ): Promise<EnrollmentRequest> {
    const newRequest: EnrollmentRequest = {
      ...request,
      id: `req-${Date.now()}`,
      requested_at: new Date().toISOString(),
    }
    this.enrollmentRequests.push(newRequest)

    // Create notification for teacher
    await this.createNotification({
      user_id: "550e8400-e29b-41d4-a716-446655440001", // Mock teacher ID
      title: "新的选课申请",
      message: `学生${request.student_name}申请选修您的${request.course_name}课程`,
      type: "enrollment_request",
      read: false,
      related_id: request.student_id,
    })

    return newRequest
  }

  static async updateEnrollmentRequest(
    requestId: string,
    status: "approved" | "rejected",
    teacherResponse?: string,
    reviewedBy?: string,
  ): Promise<void> {
    const request = this.enrollmentRequests.find((req) => req.id === requestId)
    if (request) {
      request.status = status
      request.teacher_response = teacherResponse
      request.reviewed_at = new Date().toISOString()
      request.reviewed_by = reviewedBy

      // Create notification for student
      await this.createNotification({
        user_id: request.student_id,
        title: status === "approved" ? "选课申请已批准" : "选课申请被拒绝",
        message:
          status === "approved"
            ? `您申请的${request.course_name}课程已被批准`
            : `您申请的${request.course_name}课程申请被拒绝${teacherResponse ? `：${teacherResponse}` : ""}`,
        type: status === "approved" ? "enrollment_approved" : "enrollment_rejected",
        read: false,
        related_id: request.course_id,
      })
    }
  }

  static async getUnreadCount(userId: string): Promise<number> {
    const notifications = await this.getNotifications(userId)
    return notifications.filter((n) => !n.read).length
  }
}
