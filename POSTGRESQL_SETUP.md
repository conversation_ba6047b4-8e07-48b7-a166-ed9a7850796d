# 🐘 PostgreSQL 数据库配置指南

## 📋 配置步骤

### 1. 配置数据库连接

编辑 `.env.local` 文件，填入您的 PostgreSQL 数据库信息：

```bash
# 复制 PostgreSQL 配置模板
cp .env.local.postgresql .env.local

# 编辑配置文件
nano .env.local
```

**配置示例：**
```env
# PostgreSQL 数据库配置
DATABASE_URL=postgresql://myuser:mypassword@localhost:5432/education_platform
POSTGRES_HOST=localhost
POSTGRES_DB=education_platform
POSTGRES_USER=myuser
POSTGRES_PASSWORD=mypassword
POSTGRES_PORT=5432

# Redis 缓存配置（可选）
REDIS_URL=redis://localhost:6379

# 开发环境配置
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development
DEBUG=true
NEXT_PUBLIC_DEBUG=true
NEXT_PUBLIC_DATABASE_TYPE=postgresql
```

### 2. 初始化数据库表结构

在您的 PostgreSQL 数据库中执行以下 SQL 脚本：

```sql
-- 1. 创建数据库（如果还没有）
CREATE DATABASE education_platform;

-- 2. 连接到数据库
\c education_platform;

-- 3. 执行表结构脚本
-- 复制 scripts/01-create-tables.sql 的内容并执行

-- 4. 插入初始数据（可选）
-- 复制 scripts/02-seed-data.sql 的内容并执行
```

### 3. 数据库连接测试

启动应用后，可以通过以下方式测试数据库连接：

```bash
# 启动开发环境
./deploy.sh dev

# 测试数据库连接
curl http://localhost:3001/api/health

# 或使用调试工具
./debug.sh test-db
```

## 🔧 常见配置场景

### 场景 1：本地 PostgreSQL
```env
DATABASE_URL=postgresql://postgres:password@localhost:5432/education_platform
```

### 场景 2：远程 PostgreSQL 服务器
```env
DATABASE_URL=postgresql://username:<EMAIL>:5432/database_name
```

### 场景 3：云数据库服务（如 AWS RDS）
```env
DATABASE_URL=postgresql://username:<EMAIL>:5432/database_name
```

### 场景 4：使用 SSL 连接
```env
DATABASE_URL=****************************************/database?sslmode=require
```

## 🛠️ 数据库管理工具推荐

### 1. 命令行工具
```bash
# psql 客户端
psql -h localhost -U postgres -d education_platform

# 查看表结构
\dt

# 查看表数据
SELECT * FROM users LIMIT 5;
```

### 2. 图形化工具
- **pgAdmin** - 功能强大的 PostgreSQL 管理工具
- **DBeaver** - 通用数据库管理工具
- **DataGrip** - JetBrains 的数据库 IDE

## 🔍 故障排除

### 问题 1：连接被拒绝
```bash
# 检查 PostgreSQL 服务是否运行
sudo systemctl status postgresql

# 检查端口是否开放
netstat -tulpn | grep 5432

# 检查防火墙设置
sudo ufw status
```

### 问题 2：认证失败
```bash
# 检查用户权限
sudo -u postgres psql -c "\du"

# 重置用户密码
sudo -u postgres psql -c "ALTER USER myuser PASSWORD 'newpassword';"
```

### 问题 3：数据库不存在
```bash
# 创建数据库
sudo -u postgres createdb education_platform

# 或在 psql 中创建
sudo -u postgres psql -c "CREATE DATABASE education_platform;"
```

## 📊 性能优化建议

### 1. 连接池配置
应用已配置连接池，默认设置：
- 最大连接数：20
- 空闲超时：30秒
- 连接超时：2秒

### 2. 索引优化
数据库脚本已包含必要的索引，如需添加更多索引：

```sql
-- 为经常查询的字段添加索引
CREATE INDEX idx_custom_field ON table_name(field_name);

-- 复合索引
CREATE INDEX idx_composite ON table_name(field1, field2);
```

### 3. 查询优化
```sql
-- 使用 EXPLAIN 分析查询性能
EXPLAIN ANALYZE SELECT * FROM users WHERE email = '<EMAIL>';
```

## 🔒 安全建议

1. **使用专用数据库用户**
   ```sql
   CREATE USER app_user WITH PASSWORD 'secure_password';
   GRANT CONNECT ON DATABASE education_platform TO app_user;
   GRANT USAGE ON SCHEMA public TO app_user;
   GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
   ```

2. **限制网络访问**
   - 配置 `pg_hba.conf` 限制连接来源
   - 使用 SSL 加密连接
   - 定期更新密码

3. **备份策略**
   ```bash
   # 定期备份数据库
   pg_dump -h localhost -U postgres education_platform > backup_$(date +%Y%m%d).sql
   
   # 恢复备份
   psql -h localhost -U postgres education_platform < backup_20231218.sql
   ```

## 🚀 快速开始

```bash
# 1. 配置数据库连接
cp .env.local.postgresql .env.local
nano .env.local  # 填入您的数据库信息

# 2. 初始化数据库表结构
psql -h your_host -U your_user -d your_database -f scripts/01-create-tables.sql

# 3. 启动应用
./deploy.sh dev

# 4. 测试连接
curl http://localhost:3001/api/health
```

现在您的应用已成功连接到 PostgreSQL 数据库！🎉
