-- 插入示例用户
INSERT INTO users (id, email, name, role) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', '张老师', 'teacher'),
  ('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', '李老师', 'teacher'),
  ('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', '王小明', 'student'),
  ('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', '陈小红', 'student'),
  ('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', '刘小强', 'student'),
  ('550e8400-e29b-41d4-a716-446655440006', '<EMAIL>', '管理员', 'admin');

-- 插入示例课程
INSERT INTO courses (id, name, description, teacher_id) VALUES
  ('660e8400-e29b-41d4-a716-446655440001', '高等数学', '大学高等数学课程，包含微积分、线性代数等内容', '550e8400-e29b-41d4-a716-446655440001'),
  ('660e8400-e29b-41d4-a716-446655440002', '计算机程序设计', 'Python编程基础课程', '550e8400-e29b-41d4-a716-446655440002'),
  ('660e8400-e29b-41d4-a716-446655440003', '英语写作', '学术英语写作技巧与实践', '550e8400-e29b-41d4-a716-446655440001');

-- 插入课程会话
INSERT INTO course_sessions (course_id, session_name, session_date, session_time, description) VALUES
  ('660e8400-e29b-41d4-a716-446655440001', '第一章：极限与连续', '2024-03-01', '09:00:00', '介绍函数极限的概念和性质'),
  ('660e8400-e29b-41d4-a716-446655440001', '第二章：导数与微分', '2024-03-08', '09:00:00', '导数的定义和计算方法'),
  ('660e8400-e29b-41d4-a716-446655440002', '第一课：Python基础语法', '2024-03-02', '14:00:00', 'Python变量、数据类型和基本操作'),
  ('660e8400-e29b-41d4-a716-446655440002', '第二课：控制结构', '2024-03-09', '14:00:00', '条件语句和循环结构');

-- 插入学生选课记录
INSERT INTO enrollments (student_id, course_id) VALUES
  ('550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440001'),
  ('550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440002'),
  ('550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440001'),
  ('550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440003'),
  ('550e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440002');
