-- 插入示例课程申请
INSERT INTO enrollment_requests (student_id, course_id, status, application_message, requested_at) VALUES
  ('550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440003', 'pending', '我对英语写作很感兴趣，希望能够提升我的英语表达能力。', NOW() - INTERVAL '2 days'),
  ('550e8400-e29b-41d4-a716-446655440004', '660e8400-e29b-41d4-a716-446655440002', 'pending', '我想学习编程，希望老师能够批准我的申请。', NOW() - INTERVAL '1 day'),
  ('550e8400-e29b-41d4-a716-446655440005', '660e8400-e29b-41d4-a716-446655440001', 'approved', '我的数学基础还不错，希望能够进一步学习高等数学。', NOW() - INTERVAL '3 days'),
  ('550e8400-e29b-41d4-a716-446655440003', '660e8400-e29b-41d4-a716-446655440002', 'rejected', '希望能够学习编程技能。', NOW() - INTERVAL '5 days');

-- 插入示例通知
INSERT INTO notifications (user_id, title, message, type, related_id, created_at) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', '新的选课申请', '学生王小明申请选修您的英语写作课程', 'enrollment_request', '550e8400-e29b-41d4-a716-446655440003', NOW() - INTERVAL '2 days'),
  ('550e8400-e29b-41d4-a716-446655440001', '新的选课申请', '学生陈小红申请选修您的计算机程序设计课程', 'enrollment_request', '550e8400-e29b-41d4-a716-446655440004', NOW() - INTERVAL '1 day'),
  ('550e8400-e29b-41d4-a716-446655440005', '选课申请已批准', '您申请的高等数学课程已被批准', 'enrollment_approved', '660e8400-e29b-41d4-a716-446655440001', NOW() - INTERVAL '3 days'),
  ('550e8400-e29b-41d4-a716-446655440003', '选课申请被拒绝', '您申请的计算机程序设计课程申请被拒绝', 'enrollment_rejected', '660e8400-e29b-41d4-a716-446655440002', NOW() - INTERVAL '5 days');
