-- 数据库表结构总结
-- 本文件用于记录系统中所有数据表的结构信息

/*
===========================================
教育评价平台 - 数据库表结构总结
===========================================

1. users (用户表)
   - id: UUID (主键)
   - email: VARCHAR(255) (邮箱，唯一)
   - name: VARCHAR(255) (姓名)
   - role: VARCHAR(50) (角色: teacher/student/admin)
   - created_at: TIMESTAMP WITH TIME ZONE (创建时间)
   - updated_at: TIMESTAMP WITH TIME ZONE (更新时间)

2. courses (课程表)
   - id: UUID (主键)
   - name: VARCHAR(255) (课程名称)
   - description: TEXT (课程描述)
   - teacher_id: UUID (教师ID，外键关联users.id)
   - created_at: TIMESTAMP WITH TIME ZONE (创建时间)
   - updated_at: TIMESTAMP WITH TIME ZONE (更新时间)

3. course_sessions (课程会话表)
   - id: UUID (主键)
   - course_id: UUID (课程ID，外键关联courses.id)
   - session_name: VARCHAR(255) (会话名称)
   - session_date: DATE (会话日期)
   - session_time: TIME (会话时间)
   - description: TEXT (会话描述)
   - created_at: TIMESTAMP WITH TIME ZONE (创建时间)

4. enrollments (学生选课表)
   - id: UUID (主键)
   - student_id: UUID (学生ID，外键关联users.id)
   - course_id: UUID (课程ID，外键关联courses.id)
   - enrolled_at: TIMESTAMP WITH TIME ZONE (选课时间)
   - 唯一约束: (student_id, course_id)

5. evaluations (评价表)
   - id: UUID (主键)
   - course_id: UUID (课程ID，外键关联courses.id)
   - session_id: UUID (会话ID，外键关联course_sessions.id，可为空)
   - teacher_id: UUID (教师ID，外键关联users.id)
   - title: VARCHAR(255) (评价标题)
   - content: TEXT (评价内容)
   - target_type: VARCHAR(50) (目标类型: all/individual/group)
   - created_at: TIMESTAMP WITH TIME ZONE (创建时间)
   - updated_at: TIMESTAMP WITH TIME ZONE (更新时间)

6. evaluation_targets (评价目标表)
   - id: UUID (主键)
   - evaluation_id: UUID (评价ID，外键关联evaluations.id)
   - student_id: UUID (学生ID，外键关联users.id)
   - created_at: TIMESTAMP WITH TIME ZONE (创建时间)

7. evaluation_responses (评价回复表)
   - id: UUID (主键)
   - evaluation_id: UUID (评价ID，外键关联evaluations.id)
   - student_id: UUID (学生ID，外键关联users.id)
   - content: TEXT (回复内容)
   - created_at: TIMESTAMP WITH TIME ZONE (创建时间)
   - updated_at: TIMESTAMP WITH TIME ZONE (更新时间)
   - 唯一约束: (evaluation_id, student_id)

8. course_materials (课程材料表)
   - id: UUID (主键)
   - course_id: UUID (课程ID，外键关联courses.id)
   - evaluation_id: UUID (评价ID，外键关联evaluations.id)
   - file_name: VARCHAR(255) (文件名)
   - file_url: TEXT (文件URL)
   - file_type: VARCHAR(100) (文件类型)
   - file_size: INTEGER (文件大小)
   - uploaded_by: UUID (上传者ID，外键关联users.id)
   - created_at: TIMESTAMP WITH TIME ZONE (创建时间)

9. course_announcements (课程公告表)
   - id: UUID (主键)
   - course_id: UUID (课程ID，外键关联courses.id)
   - title: VARCHAR(255) (公告标题)
   - content: TEXT (公告内容)
   - created_by: UUID (创建者ID，外键关联users.id)
   - created_at: TIMESTAMP WITH TIME ZONE (创建时间)
   - updated_at: TIMESTAMP WITH TIME ZONE (更新时间)

10. enrollment_requests (选课申请表)
    - id: UUID (主键)
    - student_id: UUID (学生ID，外键关联users.id)
    - course_id: UUID (课程ID，外键关联courses.id)
    - status: VARCHAR(20) (状态: pending/approved/rejected)
    - message: TEXT (申请消息)
    - teacher_response: TEXT (教师回复)
    - created_at: TIMESTAMP WITH TIME ZONE (创建时间)
    - updated_at: TIMESTAMP WITH TIME ZONE (更新时间)

11. notifications (通知表)
    - id: UUID (主键)
    - user_id: UUID (用户ID，外键关联users.id)
    - title: VARCHAR(255) (通知标题)
    - message: TEXT (通知消息)
    - type: VARCHAR(50) (通知类型)
    - read: BOOLEAN (是否已读，默认false)
    - created_at: TIMESTAMP WITH TIME ZONE (创建时间)

===========================================
表关系总结:
===========================================

核心实体:
- users (用户)
- courses (课程)
- course_sessions (课程会话)

关联关系:
- enrollments: 学生与课程的多对多关系
- evaluations: 教师对课程/会话的评价
- evaluation_targets: 评价的具体目标学生
- evaluation_responses: 学生对评价的回复

辅助功能:
- course_materials: 课程相关材料
- course_announcements: 课程公告
- enrollment_requests: 选课申请流程
- notifications: 系统通知

===========================================
索引建议:
===========================================

建议创建的索引:
- users.email (唯一索引)
- users.role
- courses.teacher_id
- enrollments.student_id
- enrollments.course_id
- evaluations.course_id
- evaluations.teacher_id
- evaluation_targets.evaluation_id
- evaluation_targets.student_id
- evaluation_responses.evaluation_id
- evaluation_responses.student_id
- enrollment_requests.student_id
- enrollment_requests.course_id
- notifications.user_id
- notifications.read

*/

-- 查询所有表的统计信息
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
ORDER BY table_name, ordinal_position;
