-- 创建测试评价数据
INSERT INTO evaluations (
  id,
  title,
  content,
  course_id,
  teacher_id,
  target_type,
  created_at
) VALUES 
(
  '770e8400-e29b-41d4-a716-446655440001',
  '第一次课程评价',
  '**课程表现总结**

本次课程中，同学们的表现整体良好。

- 积极参与课堂讨论
- 按时完成作业  
- 需要加强理论理解

希望大家继续保持学习热情，在下次课程中有更好的表现。',
  '660e8400-e29b-41d4-a716-446655440001',
  '550e8400-e29b-41d4-a716-446655440001',
  'all',
  NOW()
),
(
  '770e8400-e29b-41d4-a716-446655440002',
  '编程作业评价',
  '**编程作业反馈**

本次Python编程作业完成情况：

- 代码逻辑清晰
- 变量命名规范
- 注释详细完整
- 建议多练习算法题

继续加油！',
  '660e8400-e29b-41d4-a716-446655440002',
  '550e8400-e29b-41d4-a716-446655440001',
  'all',
  NOW() - INTERVAL '1 day'
),
(
  '770e8400-e29b-41d4-a716-446655440003',
  '个人表现评价',
  '**个人学习表现**

针对您的学习情况：

- 课堂参与度很高
- 问题回答准确
- 需要加强实践练习

期待您的进一步提升！',
  '660e8400-e29b-41d4-a716-446655440001',
  '550e8400-e29b-41d4-a716-446655440001',
  'individual',
  NOW() - INTERVAL '2 days'
);

-- 为个人评价添加目标学生
INSERT INTO evaluation_targets (
  evaluation_id,
  student_id
) VALUES (
  '770e8400-e29b-41d4-a716-446655440003',
  '880e8400-e29b-41d4-a716-446655440001'
);

-- 确保学生已注册课程
INSERT INTO enrollments (
  student_id,
  course_id,
  enrollment_date,
  status
) VALUES 
(
  '880e8400-e29b-41d4-a716-446655440001',
  '660e8400-e29b-41d4-a716-446655440001',
  NOW() - INTERVAL '7 days',
  'active'
),
(
  '880e8400-e29b-41d4-a716-446655440001',
  '660e8400-e29b-41d4-a716-446655440002',
  NOW() - INTERVAL '7 days',
  'active'
)
ON CONFLICT (student_id, course_id) DO NOTHING;
