"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { BookOpen, Users, FileText, MessageCircle } from "lucide-react"
import Link from "next/link"

export default function HomePage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && user) {
      // Redirect based on user role
      switch (user.role) {
        case "teacher":
          router.push("/teacher/dashboard")
          break
        case "student":
          router.push("/student/dashboard")
          break
        case "admin":
          router.push("/admin/dashboard")
          break
      }
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  if (user) {
    return null // Will redirect based on role
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <BookOpen className="mx-auto h-16 w-16 mb-8" />
            <h1 className="text-4xl md:text-6xl font-bold mb-6">课程评价系统</h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">促进师生交流，提升教学质量的综合评价平台</p>
            <Link href="/login">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-3">
                立即登录
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">平台特色功能</h2>
            <p className="text-xl text-gray-600">为教育者和学习者提供全面的评价和反馈工具</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="text-center">
              <CardHeader>
                <FileText className="mx-auto h-12 w-12 text-blue-600 mb-4" />
                <CardTitle>富文本评价</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>支持富文本格式的详细评价内容，让反馈更加生动和具体</CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Users className="mx-auto h-12 w-12 text-green-600 mb-4" />
                <CardTitle>灵活的目标设置</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>支持全体学生、个人或群组的评价发布，满足不同教学需求</CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <MessageCircle className="mx-auto h-12 w-12 text-purple-600 mb-4" />
                <CardTitle>双向交流</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>学生可以对评价进行回复，建立师生之间的有效沟通渠道</CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <BookOpen className="mx-auto h-12 w-12 text-orange-600 mb-4" />
                <CardTitle>课程材料管理</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>支持上传和分享课程相关材料，丰富评价内容和学习资源</CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gray-50 py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">开始使用课程评价系统</h2>
          <p className="text-xl text-gray-600 mb-8">立即登录，体验高效的教学评价和反馈管理</p>
          <Link href="/login">
            <Button size="lg" className="text-lg px-8 py-3">
              立即登录
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
