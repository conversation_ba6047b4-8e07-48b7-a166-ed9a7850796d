"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { dataService } from "@/lib/data-access"
import { useAuth } from "@/components/auth-provider"
import { FileText, MessageCircle, Download, Calendar } from "lucide-react"
import Link from "next/link"

interface Evaluation {
  id: string
  title: string
  content: string
  created_at: string
  target_type: string
  courses: {
    name: string
  }
  course_sessions: {
    session_name: string
  } | null
  users: {
    name: string
  }
  course_materials: Array<{
    id: string
    file_name: string
    file_url: string
    file_type: string
  }>
  evaluation_responses: Array<{
    id: string
    content: string
    created_at: string
  }>
}

export default function StudentEvaluationsPage() {
  const { user } = useAuth()
  const [evaluations, setEvaluations] = useState<Evaluation[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user) {
      fetchEvaluations()
    }
  }, [user])

  async function fetchEvaluations() {
    try {
      if (dataService.isUsingMockData) {
        // 使用模拟数据
        setEvaluations(dataService.mock.evaluations)
        return
      }

      // 使用 PostgreSQL 数据
      const evaluations = await dataService.evaluation.findAll({ studentId: user!.id })
      setEvaluations(evaluations)
    } catch (error) {
      console.error("获取评价失败:", error)
      // 出错时使用模拟数据
      setEvaluations(dataService.mock.evaluations)
    } finally {
      setLoading(false)
    }
  }

  const renderContent = (content: string) => {
    return content
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      .replace(/\n- (.*)/g, "<li>$1</li>")
      .replace(/\n\d+\. (.*)/g, "<li>$1</li>")
      .replace(/\[([^\]]+)\]$$([^)]+)$$/g, '<a href="$2" class="text-blue-600 underline">$1</a>')
      .replace(/\n/g, "<br>")
  }

  if (!user) return null

  if (loading) {
    return (
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">我的课程评价</h1>
        <p className="text-gray-600">查看老师对您的评价和反馈</p>
      </div>

      {evaluations.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无评价</h3>
            <p className="text-gray-600">您还没有收到任何课程评价</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {evaluations.map((evaluation) => (
            <Card key={evaluation.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-xl">{evaluation.title}</CardTitle>
                    <CardDescription className="mt-2">
                      <div className="flex items-center space-x-4 text-sm">
                        <span>课程：{evaluation.courses.name}</span>
                        {evaluation.course_sessions && <span>会话：{evaluation.course_sessions.session_name}</span>}
                        <span>教师：{evaluation.users.name}</span>
                      </div>
                    </CardDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant={evaluation.target_type === "all" ? "secondary" : "default"}>
                      {evaluation.target_type === "all" ? "全体学生" : "个人评价"}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="mr-1 h-4 w-4" />
                      {new Date(evaluation.created_at).toLocaleDateString("zh-CN")}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: renderContent(evaluation.content) }}
                  />

                  {evaluation.course_materials.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">相关材料：</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {evaluation.course_materials.map((material) => (
                          <a
                            key={material.id}
                            href={material.file_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center p-2 border rounded-lg hover:bg-gray-50"
                          >
                            <Download className="mr-2 h-4 w-4 text-gray-500" />
                            <span className="text-sm">{material.file_name}</span>
                          </a>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="flex items-center text-sm text-gray-600">
                      <MessageCircle className="mr-1 h-4 w-4" />
                      {evaluation.evaluation_responses.length > 0 ? "已回复" : "未回复"}
                    </div>
                    <Link href={`/student/evaluations/${evaluation.id}`}>
                      <Button variant="outline" size="sm">
                        {evaluation.evaluation_responses.length > 0 ? "查看回复" : "回复评价"}
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
