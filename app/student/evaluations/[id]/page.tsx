"use client"

import { useEffect, useState } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { RichTextEditor } from "@/components/rich-text-editor"
import { dataService } from "@/lib/data-access"
import { useAuth } from "@/components/auth-provider"
import { ArrowLeft, Calendar, Download, MessageCircle } from "lucide-react"

interface Evaluation {
  id: string
  title: string
  content: string
  created_at: string
  target_type: string
  courses: {
    name: string
  }
  course_sessions: {
    session_name: string
  } | null
  users: {
    name: string
  }
  course_materials: Array<{
    id: string
    file_name: string
    file_url: string
    file_type: string
  }>
}

interface EvaluationResponse {
  id: string
  content: string
  created_at: string
  updated_at: string
}

export default function EvaluationDetailPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const evaluationId = params.id as string

  const [evaluation, setEvaluation] = useState<Evaluation | null>(null)
  const [response, setResponse] = useState<EvaluationResponse | null>(null)
  const [responseContent, setResponseContent] = useState("")
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState("")

  useEffect(() => {
    if (user && evaluationId) {
      fetchEvaluationDetail()
    }
  }, [user, evaluationId])

  async function fetchEvaluationDetail() {
    try {
      // Fetch evaluation details
      const { data: evaluationData } = await supabase
        .from("evaluations")
        .select(`
          *,
          courses(name),
          course_sessions(session_name),
          users(name),
          course_materials(id, file_name, file_url, file_type)
        `)
        .eq("id", evaluationId)
        .single()

      if (!evaluationData) {
        router.push("/student/evaluations")
        return
      }

      // Check if student has access to this evaluation
      let hasAccess = false

      if (evaluationData.target_type === "all") {
        // Check if student is enrolled in the course
        const { data: enrollment } = await supabase
          .from("enrollments")
          .select("id")
          .eq("student_id", user!.id)
          .eq("course_id", evaluationData.course_id)
          .single()

        hasAccess = !!enrollment
      } else {
        // Check if student is specifically targeted
        const { data: target } = await supabase
          .from("evaluation_targets")
          .select("id")
          .eq("evaluation_id", evaluationId)
          .eq("student_id", user!.id)
          .single()

        hasAccess = !!target
      }

      if (!hasAccess) {
        router.push("/student/evaluations")
        return
      }

      setEvaluation(evaluationData)

      // Fetch existing response
      const { data: responseData } = await supabase
        .from("evaluation_responses")
        .select("*")
        .eq("evaluation_id", evaluationId)
        .eq("student_id", user!.id)
        .single()

      if (responseData) {
        setResponse(responseData)
        setResponseContent(responseData.content)
      }
    } catch (error) {
      console.error("Error fetching evaluation detail:", error)
    } finally {
      setLoading(false)
    }
  }

  async function handleSubmitResponse() {
    if (!responseContent.trim()) {
      setError("请输入回复内容")
      return
    }

    setSubmitting(true)
    setError("")

    try {
      if (response) {
        // Update existing response
        const { error } = await supabase
          .from("evaluation_responses")
          .update({
            content: responseContent,
            updated_at: new Date().toISOString(),
          })
          .eq("id", response.id)

        if (error) throw error

        setResponse((prev) =>
          prev ? { ...prev, content: responseContent, updated_at: new Date().toISOString() } : null,
        )
      } else {
        // Create new response
        const { data, error } = await supabase
          .from("evaluation_responses")
          .insert({
            evaluation_id: evaluationId,
            student_id: user!.id,
            content: responseContent,
          })
          .select()
          .single()

        if (error) throw error

        setResponse(data)
      }

      setError("")
    } catch (error: any) {
      setError(error.message || "提交回复失败")
    } finally {
      setSubmitting(false)
    }
  }

  const renderContent = (content: string) => {
    return content
      .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
      .replace(/\*(.*?)\*/g, "<em>$1</em>")
      .replace(/\n- (.*)/g, "<li>$1</li>")
      .replace(/\n\d+\. (.*)/g, "<li>$1</li>")
      .replace(/\[([^\]]+)\]$$([^)]+)$$/g, '<a href="$2" class="text-blue-600 underline">$1</a>')
      .replace(/\n/g, "<br>")
  }

  if (!user) return null

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  if (!evaluation) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">评价不存在或您没有访问权限</div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">评价详情</h1>
      </div>

      <div className="space-y-6">
        {/* Evaluation Details */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-xl">{evaluation.title}</CardTitle>
                <CardDescription className="mt-2">
                  <div className="flex items-center space-x-4 text-sm">
                    <span>课程：{evaluation.courses.name}</span>
                    {evaluation.course_sessions && <span>会话：{evaluation.course_sessions.session_name}</span>}
                    <span>教师：{evaluation.users.name}</span>
                  </div>
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={evaluation.target_type === "all" ? "secondary" : "default"}>
                  {evaluation.target_type === "all" ? "全体学生" : "个人评价"}
                </Badge>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="mr-1 h-4 w-4" />
                  {new Date(evaluation.created_at).toLocaleDateString("zh-CN")}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{ __html: renderContent(evaluation.content) }}
              />

              {evaluation.course_materials.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">相关材料：</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {evaluation.course_materials.map((material) => (
                      <a
                        key={material.id}
                        href={material.file_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center p-2 border rounded-lg hover:bg-gray-50"
                      >
                        <Download className="mr-2 h-4 w-4 text-gray-500" />
                        <span className="text-sm">{material.file_name}</span>
                      </a>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Response Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageCircle className="mr-2 h-5 w-5" />
              我的回复
            </CardTitle>
            <CardDescription>{response ? "您可以修改您的回复" : "对这个评价进行回复"}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {response && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">当前回复：</span>
                    <span className="text-xs text-gray-500">
                      {response.updated_at !== response.created_at ? "已修改" : ""}
                      {new Date(response.updated_at || response.created_at).toLocaleString("zh-CN")}
                    </span>
                  </div>
                  <div
                    className="prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: renderContent(response.content) }}
                  />
                </div>
              )}

              <div>
                <RichTextEditor value={responseContent} onChange={setResponseContent} placeholder="请输入您的回复..." />
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end">
                <Button onClick={handleSubmitResponse} disabled={submitting || !responseContent.trim()}>
                  {submitting ? "提交中..." : response ? "更新回复" : "提交回复"}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
