"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/components/auth-provider"
import { BookOpen, FileText, MessageCircle, Plus, Users } from "lucide-react"
import Link from "next/link"

interface Course {
  id: string
  name: string
  description: string
  teacher_name: string
  enrolled: boolean
}

interface DashboardStats {
  enrolledCourses: number
  pendingEvaluations: number
  completedResponses: number
  availableCourses: number
}

interface NextClass {
  id: string
  course_name: string
  teacher_name: string
  classroom: string
  start_time: string
  end_time: string
  date: string
  topic: string
}

export default function StudentDashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    enrolledCourses: 0,
    pendingEvaluations: 0,
    completedResponses: 0,
    availableCourses: 0,
  })
  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([])
  const [availableCourses, setAvailableCourses] = useState<Course[]>([])
  const [recentEvaluations, setRecentEvaluations] = useState<any[]>([])
  const [nextClass, setNextClass] = useState<NextClass | null>(null)

  useEffect(() => {
    if (user) {
      fetchDashboardData()
    }
  }, [user])

  async function fetchDashboardData() {
    // Mock data for testing - 保留原有的模拟数据逻辑
    const mockEnrolledCourses = [
      {
        id: "660e8400-e29b-41d4-a716-446655440001",
        name: "高等数学",
        description: "大学高等数学课程，包含微积分、线性代数等内容",
        teacher_name: "张老师",
        enrolled: true,
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440002",
        name: "计算机程序设计",
        description: "Python编程基础课程",
        teacher_name: "张老师",
        enrolled: true,
      },
    ]

    const mockAvailableCourses = [
      {
        id: "660e8400-e29b-41d4-a716-446655440003",
        name: "英语写作",
        description: "学术英语写作技巧与实践",
        teacher_name: "李老师",
        enrolled: false,
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440004",
        name: "数据结构",
        description: "计算机科学基础数据结构课程",
        teacher_name: "王老师",
        enrolled: false,
      },
    ]

    const mockRecentEvaluations = [
      {
        id: "770e8400-e29b-41d4-a716-446655440001",
        title: "第一次课程评价",
        course_name: "高等数学",
        teacher_name: "张老师",
        created_at: new Date().toISOString(),
        has_response: false,
      },
      {
        id: "770e8400-e29b-41d4-a716-446655440002",
        title: "编程作业反馈",
        course_name: "计算机程序设计",
        teacher_name: "张老师",
        created_at: new Date(Date.now() - 86400000).toISOString(),
        has_response: true,
      },
      {
        id: "770e8400-e29b-41d4-a716-446655440003",
        title: "期中考试评价",
        course_name: "英语写作",
        teacher_name: "李老师",
        created_at: new Date(Date.now() - 172800000).toISOString(),
        has_response: false,
      },
      {
        id: "770e8400-e29b-41d4-a716-446655440004",
        title: "项目完成情况",
        course_name: "数据结构",
        teacher_name: "王老师",
        created_at: new Date(Date.now() - 259200000).toISOString(),
        has_response: true,
      },
    ]

    const mockNextClass: NextClass = {
      id: "class-001",
      course_name: "高等数学",
      teacher_name: "张老师",
      classroom: "教学楼A-201",
      start_time: "09:00",
      end_time: "10:30",
      date: new Date(Date.now() + 86400000).toISOString(), // 明天
      topic: "微积分基础理论与应用",
    }

    setNextClass(mockNextClass)
    setEnrolledCourses(mockEnrolledCourses)
    setAvailableCourses(mockAvailableCourses)
    setRecentEvaluations(mockRecentEvaluations)

    setStats({
      enrolledCourses: mockEnrolledCourses.length,
      pendingEvaluations: mockRecentEvaluations.filter((e) => !e.has_response).length,
      completedResponses: mockRecentEvaluations.filter((e) => e.has_response).length,
      availableCourses: mockAvailableCourses.length,
    })
  }

  async function handleEnrollCourse(courseId: string) {
    // Mock enrollment
    const course = availableCourses.find((c) => c.id === courseId)
    if (course) {
      setEnrolledCourses((prev) => [...prev, { ...course, enrolled: true }])
      setAvailableCourses((prev) => prev.filter((c) => c.id !== courseId))
      setStats((prev) => ({
        ...prev,
        enrolledCourses: prev.enrolledCourses + 1,
        availableCourses: prev.availableCourses - 1,
      }))
    }
  }

  if (!user) return null

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">学生面板</h1>
        <p className="text-gray-600">欢迎回来，{user.name}！</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已选课程</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.enrolledCourses}</div>
            <p className="text-xs text-muted-foreground">您当前选修的课程</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待回复评价</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingEvaluations}</div>
            <p className="text-xs text-muted-foreground">需要您回复的评价</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成回复</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedResponses}</div>
            <p className="text-xs text-muted-foreground">您已回复的评价</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">可选课程</CardTitle>
            <Plus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.availableCourses}</div>
            <p className="text-xs text-muted-foreground">可以选修的新课程</p>
          </CardContent>
        </Card>
      </div>

      {/* Upcoming Course - Enhanced UI */}
      <Card className="mb-8 overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 text-white">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold">下一次课程</h2>
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
              {nextClass && new Date(nextClass.date).toLocaleDateString() === new Date().toLocaleDateString()
                ? "今天"
                : "明天"}
            </Badge>
          </div>

          {nextClass ? (
            <div
              className="cursor-pointer hover:bg-white/10 rounded-lg p-4 transition-all"
              onClick={() => (window.location.href = `/student/courses/${nextClass.id}`)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-2xl font-bold mb-2">{nextClass.course_name}</h3>
                  <p className="text-blue-100 mb-4 text-lg">{nextClass.topic}</p>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <Users className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="text-xs text-blue-100">教师</p>
                        <p className="font-medium">{nextClass.teacher_name}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <BookOpen className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="text-xs text-blue-100">教室</p>
                        <p className="font-medium">{nextClass.classroom}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <FileText className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="text-xs text-blue-100">日期</p>
                        <p className="font-medium">{new Date(nextClass.date).toLocaleDateString("zh-CN")}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <MessageCircle className="h-4 w-4" />
                      </div>
                      <div>
                        <p className="text-xs text-blue-100">时间</p>
                        <p className="font-medium">
                          {nextClass.start_time} - {nextClass.end_time}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="text-center ml-6">
                  <div className="w-20 h-20 bg-white/20 rounded-full flex flex-col items-center justify-center mb-2">
                    <span className="text-2xl font-bold">
                      {Math.ceil((new Date(nextClass.date).getTime() - new Date().getTime()) / (1000 * 60 * 60))}
                    </span>
                    <span className="text-xs">小时</span>
                  </div>
                  <p className="text-xs text-blue-100">距离上课</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <BookOpen className="h-12 w-12 mx-auto mb-4 text-white/50" />
              <p className="text-blue-100">暂无即将到来的课程</p>
            </div>
          )}
        </div>
      </Card>

      {/* Recent Evaluations - Compact Cards */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            最近的评价
            <Link href="/student/evaluations">
              <Button variant="outline" size="sm">
                查看全部
              </Button>
            </Link>
          </CardTitle>
          <CardDescription>您最近收到的课程评价</CardDescription>
        </CardHeader>
        <CardContent>
          {recentEvaluations.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {recentEvaluations.slice(0, 4).map((evaluation) => (
                <Card
                  key={evaluation.id}
                  className="cursor-pointer hover:shadow-md transition-shadow border-l-4 border-l-blue-500"
                  onClick={() => (window.location.href = `/student/evaluations/${evaluation.id}`)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm">{evaluation.title}</h4>
                      <Badge variant={evaluation.has_response ? "default" : "destructive"} className="text-xs">
                        {evaluation.has_response ? "已回复" : "待回复"}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-600 mb-2">
                      {evaluation.course_name} - {evaluation.teacher_name}
                    </p>
                    <p className="text-xs text-gray-400">
                      {new Date(evaluation.created_at).toLocaleDateString("zh-CN")}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-8 w-8 mx-auto mb-2 text-gray-300" />
              <p>暂无评价记录</p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* My Courses - Compact */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              我的课程
              <Link href="/student/courses">
                <Button variant="outline" size="sm">
                  管理课程
                </Button>
              </Link>
            </CardTitle>
            <CardDescription>您当前选修的课程</CardDescription>
          </CardHeader>
          <CardContent>
            {enrolledCourses.length > 0 ? (
              <div className="space-y-2">
                {enrolledCourses.slice(0, 3).map((course) => (
                  <div
                    key={course.id}
                    className="flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-gray-50"
                    onClick={() => (window.location.href = `/student/courses/${course.id}`)}
                  >
                    <div>
                      <p className="font-medium text-sm">{course.name}</p>
                      <p className="text-xs text-gray-600">{course.teacher_name}</p>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      已选修
                    </Badge>
                  </div>
                ))}
                {enrolledCourses.length > 3 && (
                  <p className="text-xs text-gray-500 text-center pt-2">还有 {enrolledCourses.length - 3} 门课程...</p>
                )}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4 text-sm">暂未选修任何课程</p>
            )}
          </CardContent>
        </Card>

        {/* Available Courses - Compact */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              可选课程
              <Link href="/student/courses">
                <Button variant="outline" size="sm">
                  浏览全部
                </Button>
              </Link>
            </CardTitle>
            <CardDescription>您可以选修的新课程</CardDescription>
          </CardHeader>
          <CardContent>
            {availableCourses.length > 0 ? (
              <div className="space-y-2">
                {availableCourses.slice(0, 3).map((course) => (
                  <div
                    key={course.id}
                    className="flex items-center justify-between p-2 border rounded cursor-pointer hover:bg-gray-50"
                    onClick={() => (window.location.href = `/student/courses/apply/${course.id}`)}
                  >
                    <div className="flex-1">
                      <p className="font-medium text-sm">{course.name}</p>
                      <p className="text-xs text-gray-600">{course.teacher_name}</p>
                    </div>
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleEnrollCourse(course.id)
                      }}
                    >
                      选修
                    </Button>
                  </div>
                ))}
                {availableCourses.length > 3 && (
                  <p className="text-xs text-gray-500 text-center pt-2">
                    还有 {availableCourses.length - 3} 门可选课程...
                  </p>
                )}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4 text-sm">暂无可选课程</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>常用功能快速入口</CardDescription>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Link href="/student/evaluations">
              <Button className="w-full justify-start">
                <FileText className="mr-2 h-4 w-4" />
                查看所有评价
              </Button>
            </Link>
            <Link href="/student/courses">
              <Button variant="outline" className="w-full justify-start">
                <BookOpen className="mr-2 h-4 w-4" />
                课程管理
              </Button>
            </Link>
            <Link href="/student/requests">
              <Button variant="outline" className="w-full justify-start">
                <MessageCircle className="mr-2 h-4 w-4" />
                申请状态
              </Button>
            </Link>
            <Link href="/student/profile">
              <Button variant="outline" className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                个人资料
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
