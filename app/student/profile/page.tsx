"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/components/auth-provider"
import { User, BookOpen, Calendar, FileText } from "lucide-react"

interface StudentProfile {
  id: string
  name: string
  email: string
  phone?: string
  student_id?: string
  major?: string
  grade?: string
  bio?: string
  avatar?: string
  joined_at: string
}

interface ActivityLog {
  id: string
  type: string
  description: string
  timestamp: string
}

export default function StudentProfilePage() {
  const { user } = useAuth()
  const [profile, setProfile] = useState<StudentProfile | null>(null)
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([])
  const [loading, setLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [editedProfile, setEditedProfile] = useState<Partial<StudentProfile>>({})
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    if (user) {
      fetchProfileData()
    }
  }, [user])

  async function fetchProfileData() {
    try {
      // Mock profile data
      const mockProfile: StudentProfile = {
        id: user!.id,
        name: user!.name,
        email: user!.email,
        phone: "13800138000",
        student_id: "20210101",
        major: "计算机科学与技术",
        grade: "大三",
        bio: "热爱编程和数学，希望能够在学习中不断提升自己。",
        joined_at: "2024-01-01T00:00:00Z",
      }

      // Mock activity logs
      const mockActivityLogs: ActivityLog[] = [
        {
          id: "log-1",
          type: "enrollment",
          description: "选修了课程：高等数学",
          timestamp: "2024-03-01T10:30:00Z",
        },
        {
          id: "log-2",
          type: "evaluation_response",
          description: "回复了评价：第一次课程评价",
          timestamp: "2024-03-15T14:20:00Z",
        },
        {
          id: "log-3",
          type: "enrollment",
          description: "选修了课程：计算机程序设计",
          timestamp: "2024-03-02T09:15:00Z",
        },
        {
          id: "log-4",
          type: "evaluation_response",
          description: "回复了评价：编程作业反馈",
          timestamp: "2024-04-10T16:45:00Z",
        },
        {
          id: "log-5",
          type: "enrollment_request",
          description: "申请选修课程：英语写作",
          timestamp: "2024-04-20T11:30:00Z",
        },
      ]

      setProfile(mockProfile)
      setActivityLogs(mockActivityLogs)
      setEditedProfile(mockProfile)
    } catch (error) {
      console.error("Error fetching profile data:", error)
    } finally {
      setLoading(false)
    }
  }

  async function handleSaveProfile() {
    setError("")
    setSuccess("")

    try {
      // Validate form
      if (!editedProfile.name || !editedProfile.email) {
        throw new Error("姓名和邮箱为必填项")
      }

      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Update profile
      setProfile((prev) => ({
        ...prev!,
        ...editedProfile,
      }))

      setSuccess("个人资料更新成功")
      setIsEditing(false)

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(""), 3000)
    } catch (error: any) {
      setError(error.message || "更新个人资料失败")
    }
  }

  function getActivityIcon(type: string) {
    switch (type) {
      case "enrollment":
        return <BookOpen className="h-4 w-4 text-blue-500" />
      case "evaluation_response":
        return <FileText className="h-4 w-4 text-green-500" />
      case "enrollment_request":
        return <Calendar className="h-4 w-4 text-purple-500" />
      default:
        return <Calendar className="h-4 w-4 text-gray-500" />
    }
  }

  if (!user) return null

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">个人资料</h1>
        <p className="text-gray-600">查看和管理您的个人信息</p>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile">基本信息</TabsTrigger>
          <TabsTrigger value="activity">活动记录</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>个人信息</CardTitle>
                  <CardDescription>您的基本个人信息</CardDescription>
                </div>
                {!isEditing ? (
                  <Button onClick={() => setIsEditing(true)}>编辑资料</Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={() => setIsEditing(false)}>
                      取消
                    </Button>
                    <Button onClick={handleSaveProfile}>保存</Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {success && (
                <Alert className="mb-6">
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              {error && (
                <Alert variant="destructive" className="mb-6">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-6">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="md:w-1/3 flex flex-col items-center">
                    <div className="w-32 h-32 rounded-full bg-gray-200 flex items-center justify-center mb-4">
                      <User className="h-16 w-16 text-gray-400" />
                    </div>
                    {isEditing && <Button variant="outline">更换头像</Button>}
                  </div>

                  <div className="md:w-2/3 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="name">姓名</Label>
                        {isEditing ? (
                          <Input
                            id="name"
                            value={editedProfile.name || ""}
                            onChange={(e) => setEditedProfile((prev) => ({ ...prev, name: e.target.value }))}
                            placeholder="请输入姓名"
                          />
                        ) : (
                          <div className="flex items-center h-10 px-3 rounded-md border border-gray-200 bg-gray-50">
                            <User className="mr-2 h-4 w-4 text-gray-500" />
                            <span>{profile?.name}</span>
                          </div>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="email">邮箱</Label>
                        {isEditing ? (
                          <Input
                            id="email"
                            type="email"
                            value={editedProfile.email || ""}
                            onChange={(e) => setEditedProfile((prev) => ({ ...prev, email: e.target.value }))}
                            placeholder="请输入邮箱地址"
                          />
                        ) : (
                          <div className="flex items-center h-10 px-3 rounded-md border border-gray-200 bg-gray-50">
                            <span>{profile?.email}</span>
                          </div>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="phone">手机号码</Label>
                        {isEditing ? (
                          <Input
                            id="phone"
                            value={editedProfile.phone || ""}
                            onChange={(e) => setEditedProfile((prev) => ({ ...prev, phone: e.target.value }))}
                            placeholder="请输入手机号码"
                          />
                        ) : (
                          <div className="flex items-center h-10 px-3 rounded-md border border-gray-200 bg-gray-50">
                            <span>{profile?.phone || "未填写"}</span>
                          </div>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="student_id">学号</Label>
                        {isEditing ? (
                          <Input
                            id="student_id"
                            value={editedProfile.student_id || ""}
                            onChange={(e) => setEditedProfile((prev) => ({ ...prev, student_id: e.target.value }))}
                            placeholder="请输入学号"
                          />
                        ) : (
                          <div className="flex items-center h-10 px-3 rounded-md border border-gray-200 bg-gray-50">
                            <span>{profile?.student_id || "未填写"}</span>
                          </div>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="major">专业</Label>
                        {isEditing ? (
                          <Input
                            id="major"
                            value={editedProfile.major || ""}
                            onChange={(e) => setEditedProfile((prev) => ({ ...prev, major: e.target.value }))}
                            placeholder="请输入专业"
                          />
                        ) : (
                          <div className="flex items-center h-10 px-3 rounded-md border border-gray-200 bg-gray-50">
                            <span>{profile?.major || "未填写"}</span>
                          </div>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="grade">年级</Label>
                        {isEditing ? (
                          <Input
                            id="grade"
                            value={editedProfile.grade || ""}
                            onChange={(e) => setEditedProfile((prev) => ({ ...prev, grade: e.target.value }))}
                            placeholder="请输入年级"
                          />
                        ) : (
                          <div className="flex items-center h-10 px-3 rounded-md border border-gray-200 bg-gray-50">
                            <span>{profile?.grade || "未填写"}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="bio">个人简介</Label>
                      {isEditing ? (
                        <textarea
                          id="bio"
                          value={editedProfile.bio || ""}
                          onChange={(e) => setEditedProfile((prev) => ({ ...prev, bio: e.target.value }))}
                          placeholder="请输入个人简介"
                          rows={4}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <div className="min-h-[100px] p-3 rounded-md border border-gray-200 bg-gray-50">
                          <span>{profile?.bio || "暂无个人简介"}</span>
                        </div>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>注册时间</Label>
                        <div className="flex items-center h-10 px-3 rounded-md border border-gray-200 bg-gray-50">
                          <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                          <span>{new Date(profile?.joined_at || "").toLocaleDateString("zh-CN")}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>活动记录</CardTitle>
              <CardDescription>您在系统中的活动历史</CardDescription>
            </CardHeader>
            <CardContent>
              {activityLogs.length > 0 ? (
                <div className="space-y-4">
                  {activityLogs.map((log) => (
                    <div key={log.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                      <div className="mt-1">{getActivityIcon(log.type)}</div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{log.description}</p>
                        <p className="text-xs text-gray-500 mt-1">{new Date(log.timestamp).toLocaleString("zh-CN")}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无活动记录</h3>
                  <p className="text-gray-600">您还没有任何活动记录</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
