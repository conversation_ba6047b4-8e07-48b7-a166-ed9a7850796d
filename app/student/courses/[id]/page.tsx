"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/components/auth-provider"
import { CourseCalendar } from "@/components/course-calendar"
import { ArrowLeft, Calendar, Clock, FileText, User, Users } from "lucide-react"

interface Course {
  id: string
  name: string
  description: string
  teacher_name: string
  teacher_email: string
  student_count: number
  session_count: number
  created_at: string
}

interface Session {
  id: string
  name: string
  date: string
  time: string
  description?: string
}

interface Evaluation {
  id: string
  title: string
  created_at: string
  has_response: boolean
  response_content?: string
}

export default function StudentCourseDetailPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const courseId = params.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [sessions, setSessions] = useState<Session[]>([])
  const [evaluations, setEvaluations] = useState<Evaluation[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    if (user && user.role === "student") {
      fetchCourseData()
    }
  }, [user, courseId])

  async function fetchCourseData() {
    try {
      // Mock course data
      const mockCourse: Course = {
        id: courseId,
        name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "高等数学" : "计算机程序设计",
        description:
          courseId === "660e8400-e29b-41d4-a716-446655440001"
            ? "大学高等数学课程，包含微积分、线性代数等内容"
            : "Python编程基础课程",
        teacher_name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "张老师" : "张老师",
        teacher_email: "<EMAIL>",
        student_count: courseId === "660e8400-e29b-41d4-a716-446655440001" ? 25 : 18,
        session_count: courseId === "660e8400-e29b-41d4-a716-446655440001" ? 12 : 8,
        created_at: "2024-03-01T00:00:00Z",
      }

      // Mock sessions data
      const mockSessions: Session[] = [
        {
          id: "session-1",
          name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "第一章：极限与连续" : "第一课：Python基础语法",
          date: "2024-06-20",
          time: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "09:00-11:00" : "14:00-16:00",
          description:
            courseId === "660e8400-e29b-41d4-a716-446655440001"
              ? "介绍函数极限的概念和性质"
              : "Python变量、数据类型和基本操作",
        },
        {
          id: "session-2",
          name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "第二章：导数与微分" : "第二课：控制结构",
          date: "2024-06-22",
          time: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "09:00-11:00" : "14:00-16:00",
          description:
            courseId === "660e8400-e29b-41d4-a716-446655440001" ? "导数的定义和计算方法" : "条件语句和循环结构",
        },
        {
          id: "session-3",
          name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "第三章：微分中值定理" : "第三课：函数",
          date: "2024-06-25",
          time: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "09:00-11:00" : "14:00-16:00",
          description:
            courseId === "660e8400-e29b-41d4-a716-446655440001"
              ? "罗尔定理、拉格朗日中值定理"
              : "函数定义、参数和返回值",
        },
      ]

      // Mock evaluations data
      const mockEvaluations: Evaluation[] = [
        {
          id: "eval-1",
          title: "第一次课程评价",
          created_at: "2024-06-15T00:00:00Z",
          has_response: false,
        },
        {
          id: "eval-2",
          title: "第二次课程评价",
          created_at: "2024-06-10T00:00:00Z",
          has_response: true,
          response_content: "感谢老师的耐心教导，课程内容很有帮助。",
        },
      ]

      setCourse(mockCourse)
      setSessions(mockSessions)
      setEvaluations(mockEvaluations)
    } catch (error) {
      console.error("Error fetching course data:", error)
    } finally {
      setLoading(false)
    }
  }

  if (!user || user.role !== "student") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">课程不存在</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{course.name}</h1>
            <p className="text-gray-600">{course.description}</p>
          </div>
          <Badge variant="default">已选修</Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="schedule">课程安排</TabsTrigger>
          <TabsTrigger value="evaluations">我的评价</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">授课教师</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{course.teacher_name}</div>
                <p className="text-xs text-muted-foreground">{course.teacher_email}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">同班同学</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{course.student_count}</div>
                <p className="text-xs text-muted-foreground">选修该课程的学生</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">课程会话</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{course.session_count}</div>
                <p className="text-xs text-muted-foreground">总课时数</p>
              </CardContent>
            </Card>
          </div>

          <CourseCalendar sessions={sessions} courseId={courseId} userRole="student" />
        </TabsContent>

        {/* Schedule Tab */}
        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>课程安排</CardTitle>
              <CardDescription>查看所有课程会话的详细安排</CardDescription>
            </CardHeader>
            <CardContent>
              {sessions.length > 0 ? (
                <div className="space-y-4">
                  {sessions.map((session) => (
                    <div key={session.id} className="flex items-start justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold text-lg">{session.name}</h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(session.date).toLocaleDateString("zh-CN")}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {session.time}
                          </span>
                        </div>
                        {session.description && <p className="text-sm text-gray-600 mt-2">{session.description}</p>}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无课程安排</h3>
                  <p className="text-gray-600">该课程还没有安排具体的课程会话</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Evaluations Tab */}
        <TabsContent value="evaluations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>我的评价</CardTitle>
              <CardDescription>查看和回复课程评价</CardDescription>
            </CardHeader>
            <CardContent>
              {evaluations.length > 0 ? (
                <div className="space-y-4">
                  {evaluations.map((evaluation) => (
                    <div key={evaluation.id} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold">{evaluation.title}</h3>
                        <Badge variant={evaluation.has_response ? "default" : "destructive"}>
                          {evaluation.has_response ? "已回复" : "待回复"}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        创建时间: {new Date(evaluation.created_at).toLocaleDateString("zh-CN")}
                      </p>
                      {evaluation.has_response && evaluation.response_content && (
                        <div className="mt-3 p-3 bg-gray-50 rounded">
                          <p className="text-sm font-medium text-gray-700 mb-1">我的回复:</p>
                          <p className="text-sm text-gray-600">{evaluation.response_content}</p>
                        </div>
                      )}
                      {!evaluation.has_response && (
                        <Button size="sm" className="mt-2">
                          回复评价
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无评价</h3>
                  <p className="text-gray-600">该课程还没有收到任何评价</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
