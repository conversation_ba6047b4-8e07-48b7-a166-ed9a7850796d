"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/components/auth-provider"
import { NotificationService } from "@/lib/notifications"
import { ArrowLeft, BookOpen, User, Calendar } from "lucide-react"

interface Course {
  id: string
  name: string
  description: string
  teacher_name: string
  teacher_email: string
  student_count: number
  schedule?: string
}

export default function CourseApplicationPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const courseId = params.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [applicationMessage, setApplicationMessage] = useState("")
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    if (user && courseId) {
      fetchCourseDetails()
    }
  }, [user, courseId])

  async function fetchCourseDetails() {
    try {
      // Mock course data
      const mockCourses: Course[] = [
        {
          id: "660e8400-e29b-41d4-a716-446655440003",
          name: "英语写作",
          description: "学术英语写作技巧与实践，提升英语表达和写作能力。",
          teacher_name: "李老师",
          teacher_email: "<EMAIL>",
          student_count: 12,
          schedule: "周一、周五 10:00-12:00",
        },
        {
          id: "660e8400-e29b-41d4-a716-446655440004",
          name: "数据结构",
          description: "计算机科学基础数据结构课程，学习算法和数据组织方法。",
          teacher_name: "王老师",
          teacher_email: "<EMAIL>",
          student_count: 22,
          schedule: "周三、周五 15:00-17:00",
        },
      ]

      const courseData = mockCourses.find((c) => c.id === courseId)
      if (!courseData) {
        setError("课程不存在")
        return
      }

      setCourse(courseData)
    } catch (error) {
      setError("获取课程信息失败")
    } finally {
      setLoading(false)
    }
  }

  async function handleSubmitApplication() {
    if (!applicationMessage.trim()) {
      setError("请填写申请理由")
      return
    }

    if (!course || !user) return

    setSubmitting(true)
    setError("")

    try {
      await NotificationService.createEnrollmentRequest({
        student_id: user.id,
        course_id: course.id,
        status: "pending",
        application_message: applicationMessage,
        student_name: user.name,
        student_email: user.email,
        course_name: course.name,
        teacher_name: course.teacher_name,
      })

      setSuccess("申请已提交，请等待老师审核")

      // Redirect after 2 seconds
      setTimeout(() => {
        router.push("/student/courses")
      }, 2000)
    } catch (error: any) {
      setError(error.message || "提交申请失败")
    } finally {
      setSubmitting(false)
    }
  }

  if (!user) return null

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">课程不存在</div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <h1 className="text-3xl font-bold text-gray-900">申请选修课程</h1>
      </div>

      <div className="space-y-6">
        {/* Course Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="mr-2 h-5 w-5" />
              课程信息
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-xl font-semibold">{course.name}</h3>
                <p className="text-gray-600 mt-2">{course.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span>授课教师: {course.teacher_name}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span>当前人数: {course.student_count} 人</span>
                </div>
                {course.schedule && (
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span>上课时间: {course.schedule}</span>
                  </div>
                )}
                <div className="flex items-center space-x-2">
                  <span className="text-gray-500">联系邮箱: {course.teacher_email}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Application Form */}
        <Card>
          <CardHeader>
            <CardTitle>申请信息</CardTitle>
            <CardDescription>请填写您的申请理由，这将帮助老师了解您的学习动机</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label htmlFor="application-message">申请理由 *</Label>
                <Textarea
                  id="application-message"
                  value={applicationMessage}
                  onChange={(e) => setApplicationMessage(e.target.value)}
                  placeholder="请简要说明您申请这门课程的理由，包括您的学习目标、相关背景等..."
                  rows={6}
                  className="mt-2"
                />
                <p className="text-xs text-gray-500 mt-1">建议字数: 50-200字</p>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="flex justify-end space-x-4">
                <Button variant="outline" onClick={() => router.back()}>
                  取消
                </Button>
                <Button onClick={handleSubmitApplication} disabled={submitting || !applicationMessage.trim()}>
                  {submitting ? "提交中..." : "提交申请"}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
