"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/components/auth-provider"
import { BookOpen, Search, Plus, Users, Calendar } from "lucide-react"
import Link from "next/link"

interface Course {
  id: string
  name: string
  description: string
  teacher_name: string
  teacher_email: string
  enrolled: boolean
  student_count: number
  schedule?: string
}

export default function StudentCoursesPage() {
  const { user } = useAuth()
  const [allCourses, setAllCourses] = useState<Course[]>([])
  const [filteredCourses, setFilteredCourses] = useState<Course[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [showEnrolledOnly, setShowEnrolledOnly] = useState(false)
  const [success, setSuccess] = useState("")

  useEffect(() => {
    if (user) {
      fetchCourses()
    }
  }, [user])

  useEffect(() => {
    filterCourses()
  }, [allCourses, searchTerm, showEnrolledOnly])

  async function fetchCourses() {
    // Mock data for testing
    const mockCourses: Course[] = [
      {
        id: "660e8400-e29b-41d4-a716-446655440001",
        name: "高等数学",
        description: "大学高等数学课程，包含微积分、线性代数等内容。适合理工科学生学习。",
        teacher_name: "张老师",
        teacher_email: "<EMAIL>",
        enrolled: true,
        student_count: 25,
        schedule: "周一、周三 09:00-11:00",
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440002",
        name: "计算机程序设计",
        description: "Python编程基础课程，从零开始学习编程思维和实践技能。",
        teacher_name: "张老师",
        teacher_email: "<EMAIL>",
        enrolled: true,
        student_count: 18,
        schedule: "周二、周四 14:00-16:00",
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440003",
        name: "英语写作",
        description: "学术英语写作技巧与实践，提升英语表达和写作能力。",
        teacher_name: "李老师",
        teacher_email: "<EMAIL>",
        enrolled: false,
        student_count: 12,
        schedule: "周一、周五 10:00-12:00",
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440004",
        name: "数据结构",
        description: "计算机科学基础数据结构课程，学习算法和数据组织方法。",
        teacher_name: "王老师",
        teacher_email: "<EMAIL>",
        enrolled: false,
        student_count: 22,
        schedule: "周三、周五 15:00-17:00",
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440005",
        name: "线性代数",
        description: "数学基础课程，学习向量、矩阵和线性变换等概念。",
        teacher_name: "陈老师",
        teacher_email: "<EMAIL>",
        enrolled: false,
        student_count: 30,
        schedule: "周二、周四 09:00-11:00",
      },
    ]

    setAllCourses(mockCourses)
  }

  function filterCourses() {
    let filtered = allCourses

    if (showEnrolledOnly) {
      filtered = filtered.filter((course) => course.enrolled)
    }

    if (searchTerm) {
      filtered = filtered.filter(
        (course) =>
          course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          course.teacher_name.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    setFilteredCourses(filtered)
  }

  async function handleEnrollCourse(courseId: string) {
    setAllCourses((prev) =>
      prev.map((course) =>
        course.id === courseId ? { ...course, enrolled: true, student_count: course.student_count + 1 } : course,
      ),
    )

    const course = allCourses.find((c) => c.id === courseId)
    setSuccess(`成功选修课程：${course?.name}`)

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(""), 3000)
  }

  async function handleDropCourse(courseId: string) {
    if (confirm("确定要退选这门课程吗？")) {
      setAllCourses((prev) =>
        prev.map((course) =>
          course.id === courseId
            ? { ...course, enrolled: false, student_count: Math.max(0, course.student_count - 1) }
            : course,
        ),
      )

      const course = allCourses.find((c) => c.id === courseId)
      setSuccess(`已退选课程：${course?.name}`)

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(""), 3000)
    }
  }

  if (!user) return null

  const enrolledCount = allCourses.filter((course) => course.enrolled).length
  const availableCount = allCourses.filter((course) => !course.enrolled).length

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">课程选择</h1>
        <p className="text-gray-600">浏览和管理您的课程选择</p>
      </div>

      {success && (
        <Alert className="mb-6">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已选课程</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{enrolledCount}</div>
            <p className="text-xs text-muted-foreground">您当前选修的课程数量</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">可选课程</CardTitle>
            <Plus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{availableCount}</div>
            <p className="text-xs text-muted-foreground">可以选修的新课程</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总课程数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allCourses.length}</div>
            <p className="text-xs text-muted-foreground">系统中的所有课程</p>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>课程筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="search">搜索课程</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="搜索课程名称、描述或教师..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Button
                variant={showEnrolledOnly ? "default" : "outline"}
                onClick={() => setShowEnrolledOnly(!showEnrolledOnly)}
              >
                {showEnrolledOnly ? "显示全部" : "仅显示已选"}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Courses List */}
      <div className="space-y-6">
        {filteredCourses.map((course) => (
          <Card key={course.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <CardTitle className="text-xl">{course.name}</CardTitle>
                    <Badge variant={course.enrolled ? "default" : "secondary"}>
                      {course.enrolled ? "已选修" : "可选修"}
                    </Badge>
                  </div>
                  <CardDescription className="text-base">{course.description}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span>授课教师: {course.teacher_name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span>选课人数: {course.student_count} 人</span>
                  </div>
                  {course.schedule && (
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>上课时间: {course.schedule}</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-500">联系邮箱: {course.teacher_email}</span>
                  </div>
                </div>

                <div className="flex justify-end space-x-2">
                  {course.enrolled ? (
                    <Button
                      variant="outline"
                      onClick={() => handleDropCourse(course.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      退选课程
                    </Button>
                  ) : (
                    <Link href={`/student/courses/apply/${course.id}`}>
                      <Button>申请选修</Button>
                    </Link>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}

        {filteredCourses.length === 0 && (
          <Card>
            <CardContent className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到课程</h3>
              <p className="text-gray-600">
                {searchTerm || showEnrolledOnly ? "请尝试调整搜索条件或筛选选项" : "暂无可用课程"}
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
