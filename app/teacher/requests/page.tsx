"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useAuth } from "@/components/auth-provider"
import { NotificationService, type EnrollmentRequest } from "@/lib/notifications"
import { Clock, CheckCircle, XCircle, User, Calendar, MessageSquare, Check, X } from "lucide-react"

export default function TeacherRequestsPage() {
  const { user } = useAuth()
  const [requests, setRequests] = useState<EnrollmentRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [processingRequest, setProcessingRequest] = useState<EnrollmentRequest | null>(null)
  const [teacherResponse, setTeacherResponse] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [actionType, setActionType] = useState<"approve" | "reject">("approve")
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    if (user && user.role === "teacher") {
      fetchRequests()
    }
  }, [user])

  async function fetchRequests() {
    try {
      const requests = await NotificationService.getEnrollmentRequests(user!.id)
      setRequests(requests)
    } catch (error) {
      console.error("Error fetching requests:", error)
    } finally {
      setLoading(false)
    }
  }

  function openProcessDialog(request: EnrollmentRequest, action: "approve" | "reject") {
    setProcessingRequest(request)
    setActionType(action)
    setTeacherResponse("")
    setIsDialogOpen(true)
    setError("")
  }

  async function handleProcessRequest() {
    if (!processingRequest) return

    if (actionType === "reject" && !teacherResponse.trim()) {
      setError("拒绝申请时请填写拒绝理由")
      return
    }

    try {
      await NotificationService.updateEnrollmentRequest(
        processingRequest.id,
        actionType,
        teacherResponse || undefined,
        user!.id,
      )

      setSuccess(
        actionType === "approve"
          ? `已批准 ${processingRequest.student_name} 的申请`
          : `已拒绝 ${processingRequest.student_name} 的申请`,
      )

      await fetchRequests()
      setIsDialogOpen(false)
      setProcessingRequest(null)
      setTeacherResponse("")

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(""), 3000)
    } catch (error: any) {
      setError(error.message || "处理申请失败")
    }
  }

  function getStatusIcon(status: string) {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "approved":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  function getStatusText(status: string) {
    switch (status) {
      case "pending":
        return "待审核"
      case "approved":
        return "已批准"
      case "rejected":
        return "已拒绝"
      default:
        return status
    }
  }

  function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
    switch (status) {
      case "pending":
        return "secondary"
      case "approved":
        return "default"
      case "rejected":
        return "destructive"
      default:
        return "outline"
    }
  }

  if (!user || user.role !== "teacher") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  const pendingRequests = requests.filter((r) => r.status === "pending")
  const processedRequests = requests.filter((r) => r.status !== "pending")

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">选课申请管理</h1>
        <p className="text-gray-600">审核和管理学生的课程申请</p>
      </div>

      {success && (
        <Alert className="mb-6">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">待审核申请</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingRequests.length}</div>
            <p className="text-xs text-muted-foreground">需要您审核的申请</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已处理申请</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{processedRequests.length}</div>
            <p className="text-xs text-muted-foreground">已审核的申请</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总申请数</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{requests.length}</div>
            <p className="text-xs text-muted-foreground">所有申请记录</p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Requests */}
      {pendingRequests.length > 0 && (
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5 text-yellow-500" />
              待审核申请 ({pendingRequests.length})
            </CardTitle>
            <CardDescription>需要您审核的学生申请</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pendingRequests.map((request) => (
                <div key={request.id} className="border rounded-lg p-4 bg-yellow-50 border-yellow-200">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-lg">{request.course_name}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                        <span className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {request.student_name} ({request.student_email})
                        </span>
                        <span className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {new Date(request.requested_at).toLocaleString("zh-CN")}
                        </span>
                      </div>
                    </div>
                    <Badge variant="secondary">待审核</Badge>
                  </div>

                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">申请理由:</h4>
                    <p className="text-sm text-gray-600 bg-white p-3 rounded border">{request.application_message}</p>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openProcessDialog(request, "reject")}
                      className="text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4 mr-1" />
                      拒绝
                    </Button>
                    <Button size="sm" onClick={() => openProcessDialog(request, "approve")}>
                      <Check className="h-4 w-4 mr-1" />
                      批准
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Processed Requests */}
      <Card>
        <CardHeader>
          <CardTitle>已处理申请</CardTitle>
          <CardDescription>您已审核的申请记录</CardDescription>
        </CardHeader>
        <CardContent>
          {processedRequests.length === 0 ? (
            <div className="text-center py-8 text-gray-500">暂无已处理的申请</div>
          ) : (
            <div className="space-y-4">
              {processedRequests.map((request) => (
                <div key={request.id} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="font-semibold text-lg">{request.course_name}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                        <span className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {request.student_name} ({request.student_email})
                        </span>
                        <span className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          申请: {new Date(request.requested_at).toLocaleString("zh-CN")}
                        </span>
                        {request.reviewed_at && (
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            审核: {new Date(request.reviewed_at).toLocaleString("zh-CN")}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(request.status)}
                      <Badge variant={getStatusVariant(request.status)}>{getStatusText(request.status)}</Badge>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-1">申请理由:</h4>
                      <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">{request.application_message}</p>
                    </div>

                    {request.teacher_response && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-700 mb-1 flex items-center">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          您的回复:
                        </h4>
                        <p className="text-sm text-gray-600 bg-blue-50 p-2 rounded border-l-4 border-blue-400">
                          {request.teacher_response}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Process Request Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{actionType === "approve" ? "批准申请" : "拒绝申请"}</DialogTitle>
            <DialogDescription>
              {actionType === "approve"
                ? `确定要批准 ${processingRequest?.student_name} 的申请吗？`
                : `确定要拒绝 ${processingRequest?.student_name} 的申请吗？`}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {processingRequest && (
              <div className="p-3 bg-gray-50 rounded">
                <p className="text-sm">
                  <strong>课程:</strong> {processingRequest.course_name}
                </p>
                <p className="text-sm">
                  <strong>学生:</strong> {processingRequest.student_name}
                </p>
                <p className="text-sm">
                  <strong>申请理由:</strong> {processingRequest.application_message}
                </p>
              </div>
            )}

            <div>
              <Label htmlFor="teacher-response">{actionType === "approve" ? "批准说明 (可选)" : "拒绝理由 *"}</Label>
              <Textarea
                id="teacher-response"
                value={teacherResponse}
                onChange={(e) => setTeacherResponse(e.target.value)}
                placeholder={actionType === "approve" ? "可以添加一些欢迎词或课程要求..." : "请说明拒绝的原因..."}
                rows={3}
                className="mt-2"
              />
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleProcessRequest} variant={actionType === "approve" ? "default" : "destructive"}>
                {actionType === "approve" ? "确认批准" : "确认拒绝"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
