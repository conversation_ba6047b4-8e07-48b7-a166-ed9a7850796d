"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/components/auth-provider"
import { Users, UserPlus, Search, BookOpen } from "lucide-react"

interface Student {
  id: string
  name: string
  email: string
  enrolled_courses: string[]
}

interface Course {
  id: string
  name: string
  description: string
}

interface Enrollment {
  student_id: string
  course_id: string
  student_name: string
  course_name: string
  enrolled_at: string
}

export default function TeacherStudentsPage() {
  const { user } = useAuth()
  const [students, setStudents] = useState<Student[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [enrollments, setEnrollments] = useState<Enrollment[]>([])
  const [filteredStudents, setFilteredStudents] = useState<Student[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCourse, setSelectedCourse] = useState<string>("all")
  const [isAssignDialogOpen, setIsAssignDialogOpen] = useState(false)
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [selectedCoursesForAssignment, setSelectedCoursesForAssignment] = useState<string[]>([])
  const [success, setSuccess] = useState("")
  const [error, setError] = useState("")

  useEffect(() => {
    if (user && user.role === "teacher") {
      fetchData()
    }
  }, [user])

  useEffect(() => {
    filterStudents()
  }, [students, searchTerm, selectedCourse, enrollments])

  async function fetchData() {
    // Mock data for testing
    const mockStudents: Student[] = [
      {
        id: "550e8400-e29b-41d4-a716-446655440003",
        name: "王小明",
        email: "<EMAIL>",
        enrolled_courses: ["660e8400-e29b-41d4-a716-446655440001", "660e8400-e29b-41d4-a716-446655440002"],
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440004",
        name: "陈小红",
        email: "<EMAIL>",
        enrolled_courses: ["660e8400-e29b-41d4-a716-446655440001"],
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440005",
        name: "刘小强",
        email: "<EMAIL>",
        enrolled_courses: ["660e8400-e29b-41d4-a716-446655440002"],
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440007",
        name: "张小丽",
        email: "<EMAIL>",
        enrolled_courses: [],
      },
    ]

    const mockCourses: Course[] = [
      {
        id: "660e8400-e29b-41d4-a716-446655440001",
        name: "高等数学",
        description: "大学高等数学课程，包含微积分、线性代数等内容",
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440002",
        name: "计算机程序设计",
        description: "Python编程基础课程",
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440003",
        name: "英语写作",
        description: "学术英语写作技巧与实践",
      },
    ]

    const mockEnrollments: Enrollment[] = [
      {
        student_id: "550e8400-e29b-41d4-a716-446655440003",
        course_id: "660e8400-e29b-41d4-a716-446655440001",
        student_name: "王小明",
        course_name: "高等数学",
        enrolled_at: new Date().toISOString(),
      },
      {
        student_id: "550e8400-e29b-41d4-a716-446655440003",
        course_id: "660e8400-e29b-41d4-a716-446655440002",
        student_name: "王小明",
        course_name: "计算机程序设计",
        enrolled_at: new Date().toISOString(),
      },
      {
        student_id: "550e8400-e29b-41d4-a716-446655440004",
        course_id: "660e8400-e29b-41d4-a716-446655440001",
        student_name: "陈小红",
        course_name: "高等数学",
        enrolled_at: new Date().toISOString(),
      },
      {
        student_id: "550e8400-e29b-41d4-a716-446655440005",
        course_id: "660e8400-e29b-41d4-a716-446655440002",
        student_name: "刘小强",
        course_name: "计算机程序设计",
        enrolled_at: new Date().toISOString(),
      },
    ]

    setStudents(mockStudents)
    setCourses(mockCourses)
    setEnrollments(mockEnrollments)
  }

  function filterStudents() {
    let filtered = students

    if (searchTerm) {
      filtered = filtered.filter(
        (student) =>
          student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          student.email.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    if (selectedCourse !== "all") {
      filtered = filtered.filter((student) => student.enrolled_courses.includes(selectedCourse))
    }

    setFilteredStudents(filtered)
  }

  function handleCourseSelection(courseId: string, checked: boolean) {
    if (checked) {
      setSelectedCoursesForAssignment((prev) => [...prev, courseId])
    } else {
      setSelectedCoursesForAssignment((prev) => prev.filter((id) => id !== courseId))
    }
  }

  async function handleAssignCourses() {
    if (!selectedStudent || selectedCoursesForAssignment.length === 0) {
      setError("请选择要分配的课程")
      return
    }

    // Update student's enrolled courses
    setStudents((prev) =>
      prev.map((student) =>
        student.id === selectedStudent.id
          ? {
              ...student,
              enrolled_courses: [...new Set([...student.enrolled_courses, ...selectedCoursesForAssignment])],
            }
          : student,
      ),
    )

    // Add new enrollments
    const newEnrollments = selectedCoursesForAssignment
      .filter((courseId) => !selectedStudent.enrolled_courses.includes(courseId))
      .map((courseId) => ({
        student_id: selectedStudent.id,
        course_id: courseId,
        student_name: selectedStudent.name,
        course_name: courses.find((c) => c.id === courseId)?.name || "",
        enrolled_at: new Date().toISOString(),
      }))

    setEnrollments((prev) => [...prev, ...newEnrollments])

    setSuccess(`成功为 ${selectedStudent.name} 分配了 ${selectedCoursesForAssignment.length} 门课程`)
    setSelectedStudent(null)
    setSelectedCoursesForAssignment([])
    setIsAssignDialogOpen(false)
    setError("")

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(""), 3000)
  }

  function openAssignDialog(student: Student) {
    setSelectedStudent(student)
    setSelectedCoursesForAssignment([...student.enrolled_courses])
    setIsAssignDialogOpen(true)
    setError("")
  }

  if (!user || user.role !== "teacher") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">学生管理</h1>
        <p className="text-gray-600">管理学生选课和课程分配</p>
      </div>

      {success && (
        <Alert className="mb-6">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>学生筛选</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">搜索学生</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="搜索姓名或邮箱..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="course-filter">按课程筛选</Label>
              <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部学生</SelectItem>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Students List */}
      <Card>
        <CardHeader>
          <CardTitle>学生列表 ({filteredStudents.length})</CardTitle>
          <CardDescription>系统中的学生及其选课情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredStudents.map((student) => (
              <div key={student.id} className="flex items-start justify-between p-4 border rounded-lg">
                <div className="flex items-start space-x-4">
                  <Users className="h-8 w-8 text-gray-400 mt-1" />
                  <div>
                    <p className="font-medium text-lg">{student.name}</p>
                    <p className="text-sm text-gray-600">{student.email}</p>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 mb-1">已选课程:</p>
                      {student.enrolled_courses.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {student.enrolled_courses.map((courseId) => {
                            const course = courses.find((c) => c.id === courseId)
                            return (
                              <span
                                key={courseId}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                              >
                                <BookOpen className="h-3 w-3 mr-1" />
                                {course?.name || "未知课程"}
                              </span>
                            )
                          })}
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">暂未选修任何课程</span>
                      )}
                    </div>
                  </div>
                </div>

                <Button variant="outline" size="sm" onClick={() => openAssignDialog(student)}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  分配课程
                </Button>
              </div>
            ))}

            {filteredStudents.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                {searchTerm || selectedCourse !== "all" ? "没有找到匹配的学生" : "暂无学生数据"}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Assign Courses Dialog */}
      <Dialog open={isAssignDialogOpen} onOpenChange={setIsAssignDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>分配课程</DialogTitle>
            <DialogDescription>为 {selectedStudent?.name} 分配课程</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>选择课程:</Label>
              <div className="space-y-2 mt-2">
                {courses.map((course) => (
                  <div key={course.id} className="flex items-start space-x-2">
                    <Checkbox
                      id={course.id}
                      checked={selectedCoursesForAssignment.includes(course.id)}
                      onCheckedChange={(checked) => handleCourseSelection(course.id, checked as boolean)}
                    />
                    <div className="flex-1">
                      <Label htmlFor={course.id} className="text-sm font-medium">
                        {course.name}
                      </Label>
                      <p className="text-xs text-gray-500">{course.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAssignDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAssignCourses}>确认分配</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
