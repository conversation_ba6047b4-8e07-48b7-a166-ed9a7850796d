"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { RichTextEditor } from "@/components/rich-text-editor"
import { dataService } from "@/lib/data-access"
import { useAuth } from "@/components/auth-provider"
import { Upload, X } from "lucide-react"

interface Course {
  id: string
  name: string
}

interface CourseSession {
  id: string
  session_name: string
  session_date: string
}

interface Student {
  id: string
  name: string
  email: string
}

export default function CreateEvaluationPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [courses, setCourses] = useState<Course[]>([])
  const [sessions, setSessions] = useState<CourseSession[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [files, setFiles] = useState<File[]>([])

  const [formData, setFormData] = useState({
    title: "",
    content: "",
    courseId: "",
    sessionId: "",
    targetType: "all" as "all" | "individual" | "group",
    selectedStudents: [] as string[],
  })

  useEffect(() => {
    if (user) {
      fetchCourses()
    }
  }, [user])

  useEffect(() => {
    if (formData.courseId) {
      fetchSessions(formData.courseId)
      fetchStudents(formData.courseId)
    }
  }, [formData.courseId])

  async function fetchCourses() {
    try {
      const { data } = await supabase.from("courses").select("id, name").eq("teacher_id", user!.id).order("name")

      setCourses(data || [])
    } catch (error) {
      console.error("Error fetching courses:", error)
    }
  }

  async function fetchSessions(courseId: string) {
    try {
      const { data } = await supabase
        .from("course_sessions")
        .select("id, session_name, session_date")
        .eq("course_id", courseId)
        .order("session_date")

      setSessions(data || [])
    } catch (error) {
      console.error("Error fetching sessions:", error)
    }
  }

  async function fetchStudents(courseId: string) {
    try {
      const { data } = await supabase
        .from("enrollments")
        .select(`
          student_id,
          users!enrollments_student_id_fkey(id, name, email)
        `)
        .eq("course_id", courseId)

      const studentList =
        data?.map((enrollment) => ({
          id: enrollment.users.id,
          name: enrollment.users.name,
          email: enrollment.users.email,
        })) || []

      setStudents(studentList)
    } catch (error) {
      console.error("Error fetching students:", error)
    }
  }

  function handleFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    if (e.target.files) {
      setFiles(Array.from(e.target.files))
    }
  }

  function removeFile(index: number) {
    setFiles(files.filter((_, i) => i !== index))
  }

  function handleStudentSelection(studentId: string, checked: boolean) {
    if (checked) {
      setFormData((prev) => ({
        ...prev,
        selectedStudents: [...prev.selectedStudents, studentId],
      }))
    } else {
      setFormData((prev) => ({
        ...prev,
        selectedStudents: prev.selectedStudents.filter((id) => id !== studentId),
      }))
    }
  }

  async function uploadFiles(evaluationId: string) {
    const uploadPromises = files.map(async (file) => {
      const fileExt = file.name.split(".").pop()
      const fileName = `${Date.now()}.${fileExt}`
      const filePath = `course-materials/${fileName}`

      const { error: uploadError } = await supabase.storage.from("course-materials").upload(filePath, file)

      if (uploadError) throw uploadError

      const {
        data: { publicUrl },
      } = supabase.storage.from("course-materials").getPublicUrl(filePath)

      // Save file record to database
      await supabase.from("course_materials").insert({
        course_id: formData.courseId,
        evaluation_id: evaluationId,
        file_name: file.name,
        file_url: publicUrl,
        file_type: file.type,
        file_size: file.size,
        uploaded_by: user!.id,
      })
    })

    await Promise.all(uploadPromises)
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setLoading(true)
    setError("")

    try {
      // Create evaluation
      const { data: evaluation, error: evalError } = await supabase
        .from("evaluations")
        .insert({
          title: formData.title,
          content: formData.content,
          course_id: formData.courseId,
          session_id: formData.sessionId || null,
          teacher_id: user!.id,
          target_type: formData.targetType,
        })
        .select()
        .single()

      if (evalError) throw evalError

      // Handle target students
      if (formData.targetType !== "all" && formData.selectedStudents.length > 0) {
        const targets = formData.selectedStudents.map((studentId) => ({
          evaluation_id: evaluation.id,
          student_id: studentId,
        }))

        const { error: targetError } = await supabase.from("evaluation_targets").insert(targets)

        if (targetError) throw targetError
      }

      // Upload files if any
      if (files.length > 0) {
        await uploadFiles(evaluation.id)
      }

      router.push("/teacher/evaluations")
    } catch (error: any) {
      setError(error.message || "创建评价失败")
    } finally {
      setLoading(false)
    }
  }

  if (!user) return null

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">创建课程评价</h1>
        <p className="text-gray-600">为您的学生创建详细的课程评价</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>评价信息</CardTitle>
          <CardDescription>填写评价的基本信息和内容</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="course">选择课程 *</Label>
                <Select
                  value={formData.courseId}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, courseId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择课程" />
                  </SelectTrigger>
                  <SelectContent>
                    {courses.map((course) => (
                      <SelectItem key={course.id} value={course.id}>
                        {course.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="session">选择课程会话（可选）</Label>
                <Select
                  value={formData.sessionId}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, sessionId: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="请选择课程会话" />
                  </SelectTrigger>
                  <SelectContent>
                    {sessions.map((session) => (
                      <SelectItem key={session.id} value={session.id}>
                        {session.session_name} ({new Date(session.session_date).toLocaleDateString("zh-CN")})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="title">评价标题 *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
                placeholder="请输入评价标题"
                required
              />
            </div>

            <div>
              <Label htmlFor="content">评价内容 *</Label>
              <RichTextEditor
                value={formData.content}
                onChange={(value) => setFormData((prev) => ({ ...prev, content: value }))}
                placeholder="请输入详细的评价内容..."
              />
            </div>

            <div>
              <Label>评价对象</Label>
              <div className="space-y-3 mt-2">
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="all"
                    name="targetType"
                    value="all"
                    checked={formData.targetType === "all"}
                    onChange={(e) => setFormData((prev) => ({ ...prev, targetType: e.target.value as any }))}
                  />
                  <Label htmlFor="all">全体学生</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="radio"
                    id="individual"
                    name="targetType"
                    value="individual"
                    checked={formData.targetType === "individual"}
                    onChange={(e) => setFormData((prev) => ({ ...prev, targetType: e.target.value as any }))}
                  />
                  <Label htmlFor="individual">指定学生</Label>
                </div>
              </div>

              {formData.targetType === "individual" && students.length > 0 && (
                <div className="mt-4 p-4 border rounded-lg">
                  <Label className="text-sm font-medium">选择学生：</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                    {students.map((student) => (
                      <div key={student.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={student.id}
                          checked={formData.selectedStudents.includes(student.id)}
                          onCheckedChange={(checked) => handleStudentSelection(student.id, checked as boolean)}
                        />
                        <Label htmlFor={student.id} className="text-sm">
                          {student.name} ({student.email})
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div>
              <Label htmlFor="files">上传课程材料（可选）</Label>
              <div className="mt-2">
                <input type="file" id="files" multiple onChange={handleFileChange} className="hidden" />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => document.getElementById("files")?.click()}
                  className="w-full"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  选择文件
                </Button>

                {files.length > 0 && (
                  <div className="mt-3 space-y-2">
                    {files.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">{file.name}</span>
                        <Button type="button" variant="ghost" size="sm" onClick={() => removeFile(index)}>
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={() => router.back()}>
                取消
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "创建中..." : "创建评价"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
