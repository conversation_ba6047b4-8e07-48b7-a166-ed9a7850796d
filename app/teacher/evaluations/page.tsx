"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from "@/components/auth-provider"
import { FileText, Search, Plus, Calendar, Users, MessageCircle } from "lucide-react"
import Link from "next/link"

interface Evaluation {
  id: string
  title: string
  course_name: string
  session_name?: string
  created_at: string
  target_type: "all" | "individual" | "group"
  response_count: number
  total_students: number
}

export default function TeacherEvaluationsPage() {
  const { user } = useAuth()
  const [evaluations, setEvaluations] = useState<Evaluation[]>([])
  const [filteredEvaluations, setFilteredEvaluations] = useState<Evaluation[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [courseFilter, setCourseFilter] = useState("all")
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user && user.role === "teacher") {
      fetchEvaluations()
    }
  }, [user])

  useEffect(() => {
    filterEvaluations()
  }, [evaluations, searchTerm, courseFilter])

  async function fetchEvaluations() {
    try {
      // Mock data for testing
      const mockEvaluations: Evaluation[] = [
        {
          id: "eval-1",
          title: "第一次课程评价",
          course_name: "高等数学",
          session_name: "第一章：极限与连续",
          created_at: "2024-03-10T00:00:00Z",
          target_type: "all",
          response_count: 18,
          total_students: 25,
        },
        {
          id: "eval-2",
          title: "第二次课程评价",
          course_name: "高等数学",
          session_name: "第二章：导数与微分",
          created_at: "2024-04-05T00:00:00Z",
          target_type: "all",
          response_count: 20,
          total_students: 25,
        },
        {
          id: "eval-3",
          title: "编程作业反馈",
          course_name: "计算机程序设计",
          session_name: "第一课：Python基础语法",
          created_at: "2024-03-15T00:00:00Z",
          target_type: "individual",
          response_count: 15,
          total_students: 18,
        },
        {
          id: "eval-4",
          title: "期中考试评价",
          course_name: "计算机程序设计",
          created_at: "2024-04-20T00:00:00Z",
          target_type: "all",
          response_count: 16,
          total_students: 18,
        },
      ]

      setEvaluations(mockEvaluations)
    } catch (error) {
      console.error("Error fetching evaluations:", error)
    } finally {
      setLoading(false)
    }
  }

  function filterEvaluations() {
    let filtered = evaluations

    if (searchTerm) {
      filtered = filtered.filter(
        (evaluation) =>
          evaluation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          evaluation.course_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (evaluation.session_name && evaluation.session_name.toLowerCase().includes(searchTerm.toLowerCase())),
      )
    }

    if (courseFilter !== "all") {
      filtered = filtered.filter((evaluation) => evaluation.course_name === courseFilter)
    }

    setFilteredEvaluations(filtered)
  }

  function getTargetTypeBadge(targetType: string) {
    switch (targetType) {
      case "all":
        return <Badge variant="secondary">全体学生</Badge>
      case "individual":
        return <Badge>个人评价</Badge>
      case "group":
        return <Badge variant="outline">小组评价</Badge>
      default:
        return null
    }
  }

  if (!user || user.role !== "teacher") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  // Get unique course names for filter
  const courses = Array.from(new Set(evaluations.map((e) => e.course_name)))

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">评价管理</h1>
          <p className="text-gray-600">管理您发布的所有课程评价</p>
        </div>
        <Link href="/teacher/evaluations/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            创建评价
          </Button>
        </Link>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总评价数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{evaluations.length}</div>
            <p className="text-xs text-muted-foreground">您发布的所有评价</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">回复率</CardTitle>
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(
                (evaluations.reduce((sum, e) => sum + e.response_count, 0) /
                  evaluations.reduce((sum, e) => sum + e.total_students, 0)) *
                  100,
              )}
              %
            </div>
            <p className="text-xs text-muted-foreground">学生回复评价的比例</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">课程数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{courses.length}</div>
            <p className="text-xs text-muted-foreground">包含评价的课程数量</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>筛选评价</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="search">搜索评价</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="搜索标题、课程或会话..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="course-filter">按课程筛选</Label>
              <Select value={courseFilter} onValueChange={setCourseFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部课程</SelectItem>
                  {courses.map((course) => (
                    <SelectItem key={course} value={course}>
                      {course}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Evaluations List */}
      <Card>
        <CardHeader>
          <CardTitle>评价列表</CardTitle>
          <CardDescription>您发布的所有课程评价</CardDescription>
        </CardHeader>
        <CardContent>
          {filteredEvaluations.length > 0 ? (
            <div className="space-y-4">
              {filteredEvaluations.map((evaluation) => (
                <div key={evaluation.id} className="flex items-start justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold text-lg">{evaluation.title}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <span>课程：{evaluation.course_name}</span>
                      {evaluation.session_name && <span>会话：{evaluation.session_name}</span>}
                      <span className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(evaluation.created_at).toLocaleDateString("zh-CN")}
                      </span>
                    </div>
                    <div className="flex items-center space-x-4 mt-2">
                      {getTargetTypeBadge(evaluation.target_type)}
                      <span className="text-sm text-gray-600">
                        回复率: {evaluation.response_count}/{evaluation.total_students}
                      </span>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Link href={`/teacher/evaluations/${evaluation.id}`}>
                      <Button variant="outline" size="sm">
                        查看详情
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无评价</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || courseFilter !== "all" ? "没有找到匹配的评价" : "您还没有创建任何评价"}
              </p>
              {!searchTerm && courseFilter === "all" && (
                <Link href="/teacher/evaluations/create">
                  <Button>创建第一个评价</Button>
                </Link>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
