"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { dataService } from "@/lib/data-access"
import { useAuth } from "@/components/auth-provider"
import { BookOpen, Users, FileText, Calendar } from "lucide-react"
import Link from "next/link"

interface DashboardStats {
  coursesCount: number
  studentsCount: number
  evaluationsCount: number
  sessionsCount: number
}

export default function TeacherDashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats>({
    coursesCount: 0,
    studentsCount: 0,
    evaluationsCount: 0,
    sessionsCount: 0,
  })
  const [recentEvaluations, setRecentEvaluations] = useState<any[]>([])

  useEffect(() => {
    if (user) {
      fetchDashboardData()
    }
  }, [user])

  async function fetchDashboardData() {
    try {
      if (dataService.isUsingMockData) {
        // 使用模拟数据
        setStats({
          coursesCount: 2,
          studentsCount: 43,
          evaluationsCount: 5,
          sessionsCount: 20,
        })
        return
      }

      // 使用 PostgreSQL 数据
      const courseStats = await dataService.course.getStats(user!.id)
      const studentStats = await dataService.enrollment.getStudentStats(user!.id)
      const evaluationStats = await dataService.evaluation.getStats(user!.id)

      setStats({
        coursesCount: courseStats.coursesCount,
        studentsCount: studentStats.studentsCount,
        evaluationsCount: evaluationStats.evaluationsCount,
        sessionsCount: 20, // 暂时使用固定值
      })

      // 获取最近的评价
      const evaluations = await dataService.evaluation.findAll({ teacherId: user!.id })
      setRecentEvaluations(evaluations.slice(0, 5))
    } catch (error) {
      console.error("获取仪表板数据失败:", error)
      // 出错时使用模拟数据
      setStats({
        coursesCount: 2,
        studentsCount: 43,
        evaluationsCount: 5,
        sessionsCount: 20,
      })
      setRecentEvaluations(dataService.mock.evaluations)
    }
  }

  if (!user) return null

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">教师面板</h1>
        <p className="text-gray-600">欢迎回来，{user.name}！</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">我的课程</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.coursesCount}</div>
            <p className="text-xs text-muted-foreground">正在教授的课程数量</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">学生总数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.studentsCount}</div>
            <p className="text-xs text-muted-foreground">选修您课程的学生</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">评价总数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.evaluationsCount}</div>
            <p className="text-xs text-muted-foreground">已发布的评价数量</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">课程会话</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.sessionsCount}</div>
            <p className="text-xs text-muted-foreground">已安排的课程会话</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>常用功能快速入口</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/teacher/evaluations/create">
              <Button className="w-full justify-start">
                <FileText className="mr-2 h-4 w-4" />
                创建新评价
              </Button>
            </Link>
            <Link href="/teacher/courses/create">
              <Button variant="outline" className="w-full justify-start">
                <BookOpen className="mr-2 h-4 w-4" />
                添加新课程
              </Button>
            </Link>
            <Link href="/teacher/courses">
              <Button variant="outline" className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                管理学生选课
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>最近的评价</CardTitle>
            <CardDescription>您最近发布的评价</CardDescription>
          </CardHeader>
          <CardContent>
            {recentEvaluations.length > 0 ? (
              <div className="space-y-3">
                {recentEvaluations.map((evaluation) => (
                  <div key={evaluation.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium">{evaluation.title}</p>
                      <p className="text-sm text-gray-600">
                        {evaluation.courses?.name} - {evaluation.course_sessions?.session_name}
                      </p>
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(evaluation.created_at).toLocaleDateString("zh-CN")}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">暂无评价记录</p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
