"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useAuth } from "@/components/auth-provider"
import { ArrowLeft, Save, Plus } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

interface CourseSession {
  id: string
  name: string
  date: string
  startTime: string
  endTime: string
  location: string
}

export default function CreateCoursePage() {
  const { user } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "",
    maxStudents: "",
    location: "",
    startDate: "",
    endDate: "",
  })
  const [sessions, setSessions] = useState<CourseSession[]>([])

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const addSession = () => {
    const newSession: CourseSession = {
      id: Date.now().toString(),
      name: "",
      date: "",
      startTime: "",
      endTime: "",
      location: formData.location,
    }
    setSessions((prev) => [...prev, newSession])
  }

  const updateSession = (id: string, field: string, value: string) => {
    setSessions((prev) => prev.map((session) => (session.id === id ? { ...session, [field]: value } : session)))
  }

  const removeSession = (id: string) => {
    setSessions((prev) => prev.filter((session) => session.id !== id))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Mock course creation - in real app, this would call an API
      console.log("Creating course:", { ...formData, sessions })

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Redirect to courses list
      router.push("/teacher/courses")
    } catch (error) {
      console.error("Error creating course:", error)
    } finally {
      setLoading(false)
    }
  }

  if (!user || user.role !== "teacher") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center gap-4 mb-8">
        <Link href="/teacher/courses">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回课程列表
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">创建新课程</h1>
          <p className="text-gray-600">填写课程基本信息和课程安排</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
            <CardDescription>设置课程的基本信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">课程名称 *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="请输入课程名称"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">课程类别</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择课程类别" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="math">数学</SelectItem>
                    <SelectItem value="science">科学</SelectItem>
                    <SelectItem value="language">语言</SelectItem>
                    <SelectItem value="computer">计算机</SelectItem>
                    <SelectItem value="art">艺术</SelectItem>
                    <SelectItem value="other">其他</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">课程描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder="请输入课程描述"
                rows={4}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="maxStudents">最大学生数</Label>
                <Input
                  id="maxStudents"
                  type="number"
                  value={formData.maxStudents}
                  onChange={(e) => handleInputChange("maxStudents", e.target.value)}
                  placeholder="30"
                  min="1"
                  max="100"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="startDate">开始日期</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange("startDate", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">结束日期</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange("endDate", e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">默认上课地点</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                placeholder="请输入上课地点"
              />
            </div>
          </CardContent>
        </Card>

        {/* 课程安排 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>课程安排</CardTitle>
                <CardDescription>添加具体的课程时间安排</CardDescription>
              </div>
              <Button type="button" onClick={addSession} variant="outline">
                <Plus className="mr-2 h-4 w-4" />
                添加课次
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {sessions.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>暂无课程安排，点击"添加课次"开始添加</p>
              </div>
            ) : (
              <div className="space-y-4">
                {sessions.map((session, index) => (
                  <div key={session.id} className="p-4 border rounded-lg space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">第 {index + 1} 课</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSession(session.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        删除
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div className="space-y-2">
                        <Label>课程主题</Label>
                        <Input
                          value={session.name}
                          onChange={(e) => updateSession(session.id, "name", e.target.value)}
                          placeholder="请输入课程主题"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>上课日期</Label>
                        <Input
                          type="date"
                          value={session.date}
                          onChange={(e) => updateSession(session.id, "date", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>开始时间</Label>
                        <Input
                          type="time"
                          value={session.startTime}
                          onChange={(e) => updateSession(session.id, "startTime", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>结束时间</Label>
                        <Input
                          type="time"
                          value={session.endTime}
                          onChange={(e) => updateSession(session.id, "endTime", e.target.value)}
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>上课地点</Label>
                      <Input
                        value={session.location}
                        onChange={(e) => updateSession(session.id, "location", e.target.value)}
                        placeholder="请输入上课地点"
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* 提交按钮 */}
        <div className="flex items-center justify-end gap-4">
          <Link href="/teacher/courses">
            <Button type="button" variant="outline">
              取消
            </Button>
          </Link>
          <Button type="submit" disabled={loading || !formData.name}>
            {loading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                创建中...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                创建课程
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
