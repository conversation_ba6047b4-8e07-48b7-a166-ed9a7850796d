"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/components/auth-provider"
import { BookOpen, Users, Calendar, Clock, Plus, ArrowRight } from "lucide-react"
import Link from "next/link"

interface Course {
  id: string
  name: string
  description: string
  student_count: number
  session_count: number
  next_session?: {
    name: string
    date: string
    time: string
  }
}

export default function TeacherCoursesPage() {
  const { user } = useAuth()
  const [courses, setCourses] = useState<Course[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user && user.role === "teacher") {
      fetchCourses()
    }
  }, [user])

  async function fetchCourses() {
    try {
      // Mock data for testing
      const mockCourses: Course[] = [
        {
          id: "660e8400-e29b-41d4-a716-446655440001",
          name: "高等数学",
          description: "大学高等数学课程，包含微积分、线性代数等内容",
          student_count: 25,
          session_count: 12,
          next_session: {
            name: "第七章：多元函数微分学",
            date: "2024-06-20",
            time: "09:00-11:00",
          },
        },
        {
          id: "660e8400-e29b-41d4-a716-446655440002",
          name: "计算机程序设计",
          description: "Python编程基础课程",
          student_count: 18,
          session_count: 8,
          next_session: {
            name: "第五课：函数与模块",
            date: "2024-06-21",
            time: "14:00-16:00",
          },
        },
        {
          id: "660e8400-e29b-41d4-a716-446655440003",
          name: "英语写作",
          description: "学术英语写作技巧与实践",
          student_count: 12,
          session_count: 6,
        },
      ]

      setCourses(mockCourses)
    } catch (error) {
      console.error("Error fetching courses:", error)
    } finally {
      setLoading(false)
    }
  }

  if (!user || user.role !== "teacher") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">我的课程</h1>
          <p className="text-gray-600">管理您正在教授的课程</p>
        </div>
        <Link href="/teacher/courses/create">
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            创建新课程
          </Button>
        </Link>
      </div>

      {courses.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <BookOpen className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无课程</h3>
            <p className="text-gray-600 mb-4">您还没有创建任何课程</p>
            <Link href="/teacher/courses/create">
              <Button>创建第一个课程</Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {courses.map((course) => (
            <Card key={course.id} className="overflow-hidden">
              <div className="h-2 bg-blue-600"></div>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="truncate">{course.name}</span>
                </CardTitle>
                <CardDescription className="line-clamp-2">{course.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col items-center justify-center p-3 bg-gray-50 rounded-lg">
                      <Users className="h-5 w-5 text-blue-600 mb-1" />
                      <span className="text-lg font-semibold">{course.student_count}</span>
                      <span className="text-xs text-gray-500">学生</span>
                    </div>
                    <div className="flex flex-col items-center justify-center p-3 bg-gray-50 rounded-lg">
                      <Calendar className="h-5 w-5 text-blue-600 mb-1" />
                      <span className="text-lg font-semibold">{course.session_count}</span>
                      <span className="text-xs text-gray-500">课时</span>
                    </div>
                  </div>

                  {course.next_session && (
                    <div className="p-3 bg-blue-50 rounded-lg border border-blue-100">
                      <div className="flex items-center text-sm font-medium text-blue-800 mb-1">
                        <Clock className="h-4 w-4 mr-1" />
                        下一节课
                      </div>
                      <p className="text-sm font-medium">{course.next_session.name}</p>
                      <p className="text-xs text-gray-600">
                        {new Date(course.next_session.date).toLocaleDateString("zh-CN")} {course.next_session.time}
                      </p>
                    </div>
                  )}

                  <div className="pt-2">
                    <Link href={`/teacher/courses/${course.id}`}>
                      <Button variant="outline" className="w-full">
                        查看详情
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
