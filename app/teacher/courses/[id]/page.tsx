"use client"

import Link from "next/link"

import type React from "react"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/components/auth-provider"
import { ArrowLeft, Calendar, Clock, Edit, FileText, Plus, Trash2, User, Users } from "lucide-react"
import { CourseCalendar } from "@/components/course-calendar"

interface Course {
  id: string
  name: string
  description: string
  student_count: number
  session_count: number
  created_at: string
}

interface Session {
  id: string
  name: string
  date: string
  time: string
  description?: string
}

interface Student {
  id: string
  name: string
  email: string
  enrolled_at: string
}

interface Evaluation {
  id: string
  title: string
  created_at: string
  response_count: number
  total_students: number
}

export default function CourseDetailPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const courseId = params.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [sessions, setSessions] = useState<Session[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [evaluations, setEvaluations] = useState<Evaluation[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  const [isAddSessionDialogOpen, setIsAddSessionDialogOpen] = useState(false)
  const [newSession, setNewSession] = useState({
    name: "",
    date: "",
    time: "",
    description: "",
  })
  const [sessionError, setSessionError] = useState("")
  const [sessionSuccess, setSessionSuccess] = useState("")

  useEffect(() => {
    if (user && user.role === "teacher") {
      fetchCourseData()
    }
  }, [user, courseId])

  async function fetchCourseData() {
    try {
      // Mock course data
      const mockCourse: Course = {
        id: courseId,
        name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "高等数学" : "计算机程序设计",
        description:
          courseId === "660e8400-e29b-41d4-a716-446655440001"
            ? "大学高等数学课程，包含微积分、线性代数等内容"
            : "Python编程基础课程",
        student_count: courseId === "660e8400-e29b-41d4-a716-446655440001" ? 25 : 18,
        session_count: courseId === "660e8400-e29b-41d4-a716-446655440001" ? 12 : 8,
        created_at: "2024-03-01T00:00:00Z",
      }

      // Mock sessions data
      const mockSessions: Session[] = [
        {
          id: "session-1",
          name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "第一章：极限与连续" : "第一课：Python基础语法",
          date: "2024-03-01",
          time: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "09:00-11:00" : "14:00-16:00",
          description:
            courseId === "660e8400-e29b-41d4-a716-446655440001"
              ? "介绍函数极限的概念和性质"
              : "Python变量、数据类型和基本操作",
        },
        {
          id: "session-2",
          name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "第二章：导数与微分" : "第二课：控制结构",
          date: "2024-03-08",
          time: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "09:00-11:00" : "14:00-16:00",
          description:
            courseId === "660e8400-e29b-41d4-a716-446655440001" ? "导数的定义和计算方法" : "条件语句和循环结构",
        },
        {
          id: "session-3",
          name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "第三章：微分中值定理" : "第三课：函数",
          date: "2024-03-15",
          time: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "09:00-11:00" : "14:00-16:00",
          description:
            courseId === "660e8400-e29b-41d4-a716-446655440001"
              ? "罗尔定理、拉格朗日中值定理"
              : "函数定义、参数和返回值",
        },
      ]

      // Mock students data
      const mockStudents: Student[] = [
        {
          id: "550e8400-e29b-41d4-a716-446655440003",
          name: "王小明",
          email: "<EMAIL>",
          enrolled_at: "2024-03-01T00:00:00Z",
        },
        {
          id: "550e8400-e29b-41d4-a716-446655440004",
          name: "陈小红",
          email: "<EMAIL>",
          enrolled_at: "2024-03-02T00:00:00Z",
        },
        {
          id: "550e8400-e29b-41d4-a716-446655440005",
          name: "刘小强",
          email: "<EMAIL>",
          enrolled_at: "2024-03-03T00:00:00Z",
        },
      ]

      // Mock evaluations data
      const mockEvaluations: Evaluation[] = [
        {
          id: "eval-1",
          title: "第一次课程评价",
          created_at: "2024-03-10T00:00:00Z",
          response_count: 18,
          total_students: 25,
        },
        {
          id: "eval-2",
          title: "第二次课程评价",
          created_at: "2024-04-05T00:00:00Z",
          response_count: 20,
          total_students: 25,
        },
      ]

      setCourse(mockCourse)
      setSessions(mockSessions)
      setStudents(mockStudents)
      setEvaluations(mockEvaluations)
    } catch (error) {
      console.error("Error fetching course data:", error)
    } finally {
      setLoading(false)
    }
  }

  async function handleAddSession(e: React.FormEvent) {
    e.preventDefault()
    setSessionError("")

    try {
      // Validate form
      if (!newSession.name || !newSession.date || !newSession.time) {
        throw new Error("请填写必填字段")
      }

      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 500))

      const newSessionData: Session = {
        id: `session-${Date.now()}`,
        name: newSession.name,
        date: newSession.date,
        time: newSession.time,
        description: newSession.description,
      }

      setSessions((prev) => [...prev, newSessionData])
      setSessionSuccess("课程会话添加成功")

      // Reset form
      setNewSession({
        name: "",
        date: "",
        time: "",
        description: "",
      })

      // Close dialog after 1 second
      setTimeout(() => {
        setIsAddSessionDialogOpen(false)
        setSessionSuccess("")
      }, 1000)
    } catch (error: any) {
      setSessionError(error.message || "添加课程会话失败")
    }
  }

  if (!user || user.role !== "teacher") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">课程不存在</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{course.name}</h1>
            <p className="text-gray-600">{course.description}</p>
          </div>
          <Button variant="outline">
            <Edit className="mr-2 h-4 w-4" />
            编辑课程
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="sessions">课程会话</TabsTrigger>
          <TabsTrigger value="students">学生管理</TabsTrigger>
          <TabsTrigger value="evaluations">评价管理</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">学生人数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{course.student_count}</div>
                <p className="text-xs text-muted-foreground">选修该课程的学生</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">课程会话</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{course.session_count}</div>
                <p className="text-xs text-muted-foreground">已安排的课程会话</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">创建时间</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{new Date(course.created_at).toLocaleDateString("zh-CN")}</div>
                <p className="text-xs text-muted-foreground">课程创建日期</p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>最近的课程会话</CardTitle>
                <CardDescription>即将进行的课程会话</CardDescription>
              </CardHeader>
              <CardContent>
                {sessions.length > 0 ? (
                  <div className="space-y-3">
                    {sessions.slice(0, 3).map((session) => (
                      <div key={session.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{session.name}</p>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(session.date).toLocaleDateString("zh-CN")}</span>
                            <Clock className="h-3 w-3 ml-2" />
                            <span>{session.time}</span>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          详情
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">暂无课程会话</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>最近的评价</CardTitle>
                <CardDescription>最近发布的课程评价</CardDescription>
              </CardHeader>
              <CardContent>
                {evaluations.length > 0 ? (
                  <div className="space-y-3">
                    {evaluations.slice(0, 3).map((evaluation) => (
                      <div key={evaluation.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{evaluation.title}</p>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Calendar className="h-3 w-3" />
                            <span>{new Date(evaluation.created_at).toLocaleDateString("zh-CN")}</span>
                            <Users className="h-3 w-3 ml-2" />
                            <span>
                              {evaluation.response_count}/{evaluation.total_students} 已回复
                            </span>
                          </div>
                        </div>
                        <Button variant="ghost" size="sm">
                          详情
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">暂无评价记录</p>
                )}
              </CardContent>
            </Card>
          </div>

          <CourseCalendar sessions={sessions} courseId={courseId} userRole="teacher" />

          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
              <CardDescription>常用功能快速入口</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Button onClick={() => setIsAddSessionDialogOpen(true)}>
                <Calendar className="mr-2 h-4 w-4" />
                添加课程会话
              </Button>
              <Link href={`/teacher/evaluations/create?course=${courseId}`}>
                <Button variant="outline" className="w-full">
                  <FileText className="mr-2 h-4 w-4" />
                  创建评价
                </Button>
              </Link>
              <Link href={`/teacher/students?course=${courseId}`}>
                <Button variant="outline" className="w-full">
                  <Users className="mr-2 h-4 w-4" />
                  管理学生
                </Button>
              </Link>
              <Link href={`/teacher/requests?course=${courseId}`}>
                <Button variant="outline" className="w-full">
                  <User className="mr-2 h-4 w-4" />
                  查看申请
                </Button>
              </Link>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Sessions Tab */}
        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>课程会话</CardTitle>
                  <CardDescription>管理课程的所有会话</CardDescription>
                </div>
                <Button onClick={() => setIsAddSessionDialogOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  添加会话
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {sessions.length > 0 ? (
                <div className="space-y-4">
                  {sessions.map((session) => (
                    <div key={session.id} className="flex items-start justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold text-lg">{session.name}</h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(session.date).toLocaleDateString("zh-CN")}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {session.time}
                          </span>
                        </div>
                        {session.description && <p className="text-sm text-gray-600 mt-2">{session.description}</p>}
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无课程会话</h3>
                  <p className="text-gray-600 mb-4">您还没有为该课程创建任何会话</p>
                  <Button onClick={() => setIsAddSessionDialogOpen(true)}>添加第一个会话</Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Students Tab */}
        <TabsContent value="students" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>学生管理</CardTitle>
                  <CardDescription>管理选修该课程的学生</CardDescription>
                </div>
                <Link href={`/teacher/students?course=${courseId}`}>
                  <Button>
                    <Users className="mr-2 h-4 w-4" />
                    管理学生
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {students.length > 0 ? (
                <div className="space-y-4">
                  {students.map((student) => (
                    <div key={student.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">{student.name}</h3>
                        <p className="text-sm text-gray-600">{student.email}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          选课时间: {new Date(student.enrolled_at).toLocaleDateString("zh-CN")}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          查看详情
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          移除学生
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无学生</h3>
                  <p className="text-gray-600">该课程还没有学生选修</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Evaluations Tab */}
        <TabsContent value="evaluations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>评价管理</CardTitle>
                  <CardDescription>管理课程的所有评价</CardDescription>
                </div>
                <Link href={`/teacher/evaluations/create?course=${courseId}`}>
                  <Button>
                    <Plus className="mr-2 h-4 w-4" />
                    创建评价
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              {evaluations.length > 0 ? (
                <div className="space-y-4">
                  {evaluations.map((evaluation) => (
                    <div key={evaluation.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">{evaluation.title}</h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(evaluation.created_at).toLocaleDateString("zh-CN")}
                          </span>
                          <span className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            回复率: {evaluation.response_count}/{evaluation.total_students}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Link href={`/teacher/evaluations/${evaluation.id}`}>
                          <Button variant="outline" size="sm">
                            查看详情
                          </Button>
                        </Link>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无评价</h3>
                  <p className="text-gray-600 mb-4">您还没有为该课程创建任何评价</p>
                  <Link href={`/teacher/evaluations/create?course=${courseId}`}>
                    <Button>创建第一个评价</Button>
                  </Link>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Session Dialog */}
      <Dialog open={isAddSessionDialogOpen} onOpenChange={setIsAddSessionDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>添加课程会话</DialogTitle>
            <DialogDescription>为课程添加新的会话</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddSession} className="space-y-4">
            <div>
              <Label htmlFor="session-name">会话名称 *</Label>
              <Input
                id="session-name"
                value={newSession.name}
                onChange={(e) => setNewSession((prev) => ({ ...prev, name: e.target.value }))}
                placeholder="例如：第一章：函数与极限"
                required
              />
            </div>

            <div>
              <Label htmlFor="session-date">日期 *</Label>
              <Input
                id="session-date"
                type="date"
                value={newSession.date}
                onChange={(e) => setNewSession((prev) => ({ ...prev, date: e.target.value }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="session-time">时间 *</Label>
              <Input
                id="session-time"
                placeholder="例如：09:00-11:00"
                value={newSession.time}
                onChange={(e) => setNewSession((prev) => ({ ...prev, time: e.target.value }))}
                required
              />
            </div>

            <div>
              <Label htmlFor="session-description">描述</Label>
              <Textarea
                id="session-description"
                value={newSession.description}
                onChange={(e) => setNewSession((prev) => ({ ...prev, description: e.target.value }))}
                placeholder="请输入会话描述"
                rows={3}
              />
            </div>

            {sessionError && (
              <Alert variant="destructive">
                <AlertDescription>{sessionError}</AlertDescription>
              </Alert>
            )}

            {sessionSuccess && (
              <Alert>
                <AlertDescription>{sessionSuccess}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end space-x-2">
              <Button type="button" variant="outline" onClick={() => setIsAddSessionDialogOpen(false)}>
                取消
              </Button>
              <Button type="submit">添加会话</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
