"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/components/auth-provider"
import { Users, BookOpen, FileText, GraduationCap } from "lucide-react"
import Link from "next/link"

interface AdminStats {
  totalUsers: number
  totalTeachers: number
  totalStudents: number
  totalCourses: number
  totalEvaluations: number
  totalEnrollments: number
}

export default function AdminDashboard() {
  const { user } = useAuth()
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalTeachers: 0,
    totalStudents: 0,
    totalCourses: 0,
    totalEvaluations: 0,
    totalEnrollments: 0,
  })

  useEffect(() => {
    if (user && user.role === "admin") {
      fetchAdminStats()
    }
  }, [user])

  async function fetchAdminStats() {
    try {
      // Mock data for testing instead of Supabase calls
      const mockStats = {
        totalUsers: 6,
        totalTeachers: 2,
        totalStudents: 3,
        totalCourses: 3,
        totalEvaluations: 4,
        totalEnrollments: 5,
      }

      setStats(mockStats)
    } catch (error) {
      console.error("Error fetching admin stats:", error)
    }
  }

  if (!user || user.role !== "admin") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">管理员面板</h1>
        <p className="text-gray-600">系统概览和管理功能</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              教师: {stats.totalTeachers} | 学生: {stats.totalStudents}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">课程总数</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCourses}</div>
            <p className="text-xs text-muted-foreground">系统中的所有课程</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">评价总数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalEvaluations}</div>
            <p className="text-xs text-muted-foreground">已发布的评价数量</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">选课总数</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalEnrollments}</div>
            <p className="text-xs text-muted-foreground">学生选课记录</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>用户管理</CardTitle>
            <CardDescription>管理系统中的用户账户</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/admin/users">
              <Button className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                查看所有用户
              </Button>
            </Link>
            <Link href="/admin/users/create">
              <Button variant="outline" className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                添加新用户
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>课程管理</CardTitle>
            <CardDescription>管理课程和选课信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/admin/courses">
              <Button className="w-full justify-start">
                <BookOpen className="mr-2 h-4 w-4" />
                查看所有课程
              </Button>
            </Link>
            <Link href="/admin/enrollments">
              <Button variant="outline" className="w-full justify-start">
                <GraduationCap className="mr-2 h-4 w-4" />
                管理选课记录
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
