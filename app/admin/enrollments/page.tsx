"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useAuth } from "@/components/auth-provider"
import { Search, Users, BookOpen, Calendar, Trash2, Plus } from "lucide-react"

interface Enrollment {
  id: string
  student_id: string
  student_name: string
  student_email: string
  course_id: string
  course_name: string
  teacher_name: string
  enrolled_at: string
  status: "active" | "dropped" | "completed"
}

interface Course {
  id: string
  name: string
  teacher_name: string
}

interface Student {
  id: string
  name: string
  email: string
}

export default function AdminEnrollmentsPage() {
  const { user } = useAuth()
  const [enrollments, setEnrollments] = useState<Enrollment[]>([])
  const [filteredEnrollments, setFilteredEnrollments] = useState<Enrollment[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [courseFilter, setCourseFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [newEnrollment, setNewEnrollment] = useState({
    student_id: "",
    course_id: "",
  })
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")

  useEffect(() => {
    if (user && user.role === "admin") {
      fetchData()
    }
  }, [user])

  useEffect(() => {
    filterEnrollments()
  }, [enrollments, searchTerm, courseFilter, statusFilter])

  async function fetchData() {
    try {
      // Mock enrollments data
      const mockEnrollments: Enrollment[] = [
        {
          id: "enroll-1",
          student_id: "550e8400-e29b-41d4-a716-446655440003",
          student_name: "王小明",
          student_email: "<EMAIL>",
          course_id: "660e8400-e29b-41d4-a716-446655440001",
          course_name: "高等数学",
          teacher_name: "张老师",
          enrolled_at: "2024-03-01T00:00:00Z",
          status: "active",
        },
        {
          id: "enroll-2",
          student_id: "550e8400-e29b-41d4-a716-446655440003",
          student_name: "王小明",
          student_email: "<EMAIL>",
          course_id: "660e8400-e29b-41d4-a716-446655440002",
          course_name: "计算机程序设计",
          teacher_name: "张老师",
          enrolled_at: "2024-03-02T00:00:00Z",
          status: "active",
        },
        {
          id: "enroll-3",
          student_id: "550e8400-e29b-41d4-a716-446655440004",
          student_name: "陈小红",
          student_email: "<EMAIL>",
          course_id: "660e8400-e29b-41d4-a716-446655440001",
          course_name: "高等数学",
          teacher_name: "张老师",
          enrolled_at: "2024-03-01T00:00:00Z",
          status: "active",
        },
        {
          id: "enroll-4",
          student_id: "550e8400-e29b-41d4-a716-446655440004",
          student_name: "陈小红",
          student_email: "<EMAIL>",
          course_id: "660e8400-e29b-41d4-a716-446655440003",
          course_name: "英语写作",
          teacher_name: "李老师",
          enrolled_at: "2024-03-03T00:00:00Z",
          status: "active",
        },
        {
          id: "enroll-5",
          student_id: "550e8400-e29b-41d4-a716-446655440005",
          student_name: "刘小强",
          student_email: "<EMAIL>",
          course_id: "660e8400-e29b-41d4-a716-446655440002",
          course_name: "计算机程序设计",
          teacher_name: "张老师",
          enrolled_at: "2024-03-04T00:00:00Z",
          status: "dropped",
        },
      ]

      // Mock courses data
      const mockCourses: Course[] = [
        {
          id: "660e8400-e29b-41d4-a716-446655440001",
          name: "高等数学",
          teacher_name: "张老师",
        },
        {
          id: "660e8400-e29b-41d4-a716-446655440002",
          name: "计算机程序设计",
          teacher_name: "张老师",
        },
        {
          id: "660e8400-e29b-41d4-a716-446655440003",
          name: "英语写作",
          teacher_name: "李老师",
        },
      ]

      // Mock students data
      const mockStudents: Student[] = [
        {
          id: "550e8400-e29b-41d4-a716-446655440003",
          name: "王小明",
          email: "<EMAIL>",
        },
        {
          id: "550e8400-e29b-41d4-a716-446655440004",
          name: "陈小红",
          email: "<EMAIL>",
        },
        {
          id: "550e8400-e29b-41d4-a716-446655440005",
          name: "刘小强",
          email: "<EMAIL>",
        },
      ]

      setEnrollments(mockEnrollments)
      setCourses(mockCourses)
      setStudents(mockStudents)
    } catch (error) {
      console.error("Error fetching data:", error)
    }
  }

  function filterEnrollments() {
    let filtered = enrollments

    if (searchTerm) {
      filtered = filtered.filter(
        (enrollment) =>
          enrollment.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          enrollment.student_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          enrollment.course_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          enrollment.teacher_name.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    if (courseFilter !== "all") {
      filtered = filtered.filter((enrollment) => enrollment.course_id === courseFilter)
    }

    if (statusFilter !== "all") {
      filtered = filtered.filter((enrollment) => enrollment.status === statusFilter)
    }

    setFilteredEnrollments(filtered)
  }

  async function handleAddEnrollment() {
    setError("")

    try {
      if (!newEnrollment.student_id || !newEnrollment.course_id) {
        throw new Error("请选择学生和课程")
      }

      // Check if enrollment already exists
      const existingEnrollment = enrollments.find(
        (e) => e.student_id === newEnrollment.student_id && e.course_id === newEnrollment.course_id,
      )

      if (existingEnrollment) {
        throw new Error("该学生已经选修了这门课程")
      }

      // Mock API call
      await new Promise((resolve) => setTimeout(resolve, 500))

      const student = students.find((s) => s.id === newEnrollment.student_id)
      const course = courses.find((c) => c.id === newEnrollment.course_id)

      const newEnrollmentData: Enrollment = {
        id: `enroll-${Date.now()}`,
        student_id: newEnrollment.student_id,
        student_name: student?.name || "",
        student_email: student?.email || "",
        course_id: newEnrollment.course_id,
        course_name: course?.name || "",
        teacher_name: course?.teacher_name || "",
        enrolled_at: new Date().toISOString(),
        status: "active",
      }

      setEnrollments((prev) => [...prev, newEnrollmentData])
      setSuccess("选课记录添加成功")
      setIsAddDialogOpen(false)
      setNewEnrollment({ student_id: "", course_id: "" })

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(""), 3000)
    } catch (error: any) {
      setError(error.message || "添加选课记录失败")
    }
  }

  async function handleRemoveEnrollment(enrollmentId: string) {
    if (confirm("确定要删除这条选课记录吗？")) {
      setEnrollments((prev) => prev.filter((e) => e.id !== enrollmentId))
      setSuccess("选课记录删除成功")

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(""), 3000)
    }
  }

  function getStatusBadge(status: string) {
    switch (status) {
      case "active":
        return <Badge variant="default">进行中</Badge>
      case "dropped":
        return <Badge variant="destructive">已退选</Badge>
      case "completed":
        return <Badge variant="secondary">已完成</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  if (!user || user.role !== "admin") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  const activeEnrollments = enrollments.filter((e) => e.status === "active").length
  const droppedEnrollments = enrollments.filter((e) => e.status === "dropped").length
  const completedEnrollments = enrollments.filter((e) => e.status === "completed").length

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">选课管理</h1>
          <p className="text-gray-600">管理系统中的所有选课记录</p>
        </div>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          添加选课记录
        </Button>
      </div>

      {success && (
        <Alert className="mb-6">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总选课数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{enrollments.length}</div>
            <p className="text-xs text-muted-foreground">所有选课记录</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">进行中</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeEnrollments}</div>
            <p className="text-xs text-muted-foreground">正在进行的选课</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已退选</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{droppedEnrollments}</div>
            <p className="text-xs text-muted-foreground">学生退选的课程</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedEnrollments}</div>
            <p className="text-xs text-muted-foreground">已完成的课程</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>筛选选课记录</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="search">搜索</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="搜索学生、课程或教师..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="course-filter">按课程筛选</Label>
              <Select value={courseFilter} onValueChange={setCourseFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部课程</SelectItem>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="status-filter">按状态筛选</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">进行中</SelectItem>
                  <SelectItem value="dropped">已退选</SelectItem>
                  <SelectItem value="completed">已完成</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Enrollments List */}
      <Card>
        <CardHeader>
          <CardTitle>选课记录 ({filteredEnrollments.length})</CardTitle>
          <CardDescription>系统中的所有选课记录</CardDescription>
        </CardHeader>
        <CardContent>
          {filteredEnrollments.length > 0 ? (
            <div className="space-y-4">
              {filteredEnrollments.map((enrollment) => (
                <div key={enrollment.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="flex items-center space-x-4 mb-2">
                      <h3 className="font-semibold">{enrollment.student_name}</h3>
                      <span className="text-sm text-gray-600">{enrollment.student_email}</span>
                      {getStatusBadge(enrollment.status)}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>课程：{enrollment.course_name}</span>
                      <span>教师：{enrollment.teacher_name}</span>
                      <span>选课时间：{new Date(enrollment.enrolled_at).toLocaleDateString("zh-CN")}</span>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveEnrollment(enrollment.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || courseFilter !== "all" || statusFilter !== "all"
                ? "没有找到匹配的选课记录"
                : "暂无选课记录"}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Enrollment Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>添加选课记录</DialogTitle>
            <DialogDescription>为学生添加新的选课记录</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="student-select">选择学生</Label>
              <Select
                value={newEnrollment.student_id}
                onValueChange={(value) => setNewEnrollment((prev) => ({ ...prev, student_id: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择学生" />
                </SelectTrigger>
                <SelectContent>
                  {students.map((student) => (
                    <SelectItem key={student.id} value={student.id}>
                      {student.name} ({student.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="course-select">选择课程</Label>
              <Select
                value={newEnrollment.course_id}
                onValueChange={(value) => setNewEnrollment((prev) => ({ ...prev, course_id: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择课程" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((course) => (
                    <SelectItem key={course.id} value={course.id}>
                      {course.name} - {course.teacher_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddEnrollment}>添加选课记录</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
