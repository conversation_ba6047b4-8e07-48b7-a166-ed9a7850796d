"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/components/auth-provider"
import { Users, Edit, Trash2, Plus, Search } from "lucide-react"

interface User {
  id: string
  name: string
  email: string
  role: "teacher" | "student" | "admin"
  created_at: string
}

interface Course {
  id: string
  name: string
  teacher_id: string
}

export default function AdminUsersPage() {
  const { user } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [courses, setCourses] = useState<Course[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [error, setError] = useState("")

  const [newUser, setNewUser] = useState({
    name: "",
    email: "",
    role: "student" as "teacher" | "student" | "admin",
    password: "",
    confirmPassword: "",
  })

  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false)
  const [passwordEditUser, setPasswordEditUser] = useState<User | null>(null)
  const [passwordData, setPasswordData] = useState({
    newPassword: "",
    confirmPassword: "",
  })

  useEffect(() => {
    if (user && user.role === "admin") {
      fetchUsers()
      fetchCourses()
    }
  }, [user])

  useEffect(() => {
    filterUsers()
  }, [users, searchTerm, roleFilter])

  async function fetchUsers() {
    // Mock data for testing
    const mockUsers: User[] = [
      {
        id: "550e8400-e29b-41d4-a716-446655440001",
        name: "张老师",
        email: "<EMAIL>",
        role: "teacher",
        created_at: new Date().toISOString(),
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440002",
        name: "李老师",
        email: "<EMAIL>",
        role: "teacher",
        created_at: new Date().toISOString(),
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440003",
        name: "王小明",
        email: "<EMAIL>",
        role: "student",
        created_at: new Date().toISOString(),
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440004",
        name: "陈小红",
        email: "<EMAIL>",
        role: "student",
        created_at: new Date().toISOString(),
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440005",
        name: "刘小强",
        email: "<EMAIL>",
        role: "student",
        created_at: new Date().toISOString(),
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440006",
        name: "管理员",
        email: "<EMAIL>",
        role: "admin",
        created_at: new Date().toISOString(),
      },
    ]

    setUsers(mockUsers)
  }

  async function fetchCourses() {
    // Mock courses data
    const mockCourses: Course[] = [
      {
        id: "660e8400-e29b-41d4-a716-446655440001",
        name: "高等数学",
        teacher_id: "550e8400-e29b-41d4-a716-446655440001",
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440002",
        name: "计算机程序设计",
        teacher_id: "550e8400-e29b-41d4-a716-446655440001",
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440003",
        name: "英语写作",
        teacher_id: "550e8400-e29b-41d4-a716-446655440002",
      },
    ]

    setCourses(mockCourses)
  }

  function filterUsers() {
    let filtered = users

    if (searchTerm) {
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase()),
      )
    }

    if (roleFilter !== "all") {
      filtered = filtered.filter((user) => user.role === roleFilter)
    }

    setFilteredUsers(filtered)
  }

  async function handleCreateUser() {
    if (!newUser.name || !newUser.email) {
      setError("请填写所有必填字段")
      return
    }

    if (newUser.password && newUser.password !== newUser.confirmPassword) {
      setError("密码和确认密码不匹配")
      return
    }

    const newUserData: User = {
      id: `new-${Date.now()}`,
      name: newUser.name,
      email: newUser.email,
      role: newUser.role,
      created_at: new Date().toISOString(),
    }

    setUsers((prev) => [...prev, newUserData])
    setNewUser({ name: "", email: "", role: "student", password: "", confirmPassword: "" })
    setIsCreateDialogOpen(false)
    setError("")
  }

  async function handleUpdateUser() {
    if (!editingUser) return

    setUsers((prev) => prev.map((u) => (u.id === editingUser.id ? editingUser : u)))
    setEditingUser(null)
    setIsEditDialogOpen(false)
    setError("")
  }

  async function handleDeleteUser(userId: string) {
    if (confirm("确定要删除这个用户吗？")) {
      setUsers((prev) => prev.filter((u) => u.id !== userId))
    }
  }

  function getRoleBadgeVariant(role: string) {
    switch (role) {
      case "admin":
        return "destructive"
      case "teacher":
        return "default"
      case "student":
        return "secondary"
      default:
        return "outline"
    }
  }

  function getRoleText(role: string) {
    switch (role) {
      case "admin":
        return "管理员"
      case "teacher":
        return "教师"
      case "student":
        return "学生"
      default:
        return role
    }
  }

  async function handleResetPassword() {
    if (!passwordEditUser) return

    if (!passwordData.newPassword) {
      setError("请输入新密码")
      return
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError("密码和确认密码不匹配")
      return
    }

    // Mock password update
    console.log(`Password updated for user: ${passwordEditUser.name}`)

    setPasswordData({ newPassword: "", confirmPassword: "" })
    setPasswordEditUser(null)
    setIsPasswordDialogOpen(false)
    setError("")
  }

  if (!user || user.role !== "admin") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">用户管理</h1>
        <p className="text-gray-600">管理系统中的所有用户账户</p>
      </div>

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>用户筛选和操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 items-end">
            <div className="flex-1">
              <Label htmlFor="search">搜索用户</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="搜索姓名或邮箱..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="role-filter">角色筛选</Label>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="admin">管理员</SelectItem>
                  <SelectItem value="teacher">教师</SelectItem>
                  <SelectItem value="student">学生</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  添加用户
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>添加新用户</DialogTitle>
                  <DialogDescription>创建新的用户账户</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="new-name">姓名 *</Label>
                    <Input
                      id="new-name"
                      value={newUser.name}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, name: e.target.value }))}
                      placeholder="请输入姓名"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-email">邮箱 *</Label>
                    <Input
                      id="new-email"
                      type="email"
                      value={newUser.email}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, email: e.target.value }))}
                      placeholder="请输入邮箱地址"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-role">角色</Label>
                    <Select
                      value={newUser.role}
                      onValueChange={(value: any) => setNewUser((prev) => ({ ...prev, role: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="student">学生</SelectItem>
                        <SelectItem value="teacher">教师</SelectItem>
                        <SelectItem value="admin">管理员</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="new-password">密码</Label>
                    <Input
                      id="new-password"
                      type="password"
                      value={newUser.password}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, password: e.target.value }))}
                      placeholder="请输入密码（测试模式可选）"
                    />
                  </div>
                  <div>
                    <Label htmlFor="confirm-password">确认密码</Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      value={newUser.confirmPassword}
                      onChange={(e) => setNewUser((prev) => ({ ...prev, confirmPassword: e.target.value }))}
                      placeholder="请再次输入密码"
                    />
                  </div>

                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      取消
                    </Button>
                    <Button onClick={handleCreateUser}>创建用户</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表 ({filteredUsers.length})</CardTitle>
          <CardDescription>系统中的所有用户账户</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredUsers.map((userData) => (
              <div key={userData.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <Users className="h-8 w-8 text-gray-400" />
                  <div>
                    <p className="font-medium">{userData.name}</p>
                    <p className="text-sm text-gray-600">{userData.email}</p>
                    <p className="text-xs text-gray-500">
                      创建时间: {new Date(userData.created_at).toLocaleDateString("zh-CN")}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Badge variant={getRoleBadgeVariant(userData.role)}>{getRoleText(userData.role)}</Badge>

                  <Dialog open={isEditDialogOpen && editingUser?.id === userData.id} onOpenChange={setIsEditDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm" onClick={() => setEditingUser({ ...userData })}>
                        <Edit className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>编辑用户信息</DialogTitle>
                        <DialogDescription>修改用户的基本信息</DialogDescription>
                      </DialogHeader>
                      {editingUser && (
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="edit-name">姓名</Label>
                            <Input
                              id="edit-name"
                              value={editingUser.name}
                              onChange={(e) =>
                                setEditingUser((prev) => (prev ? { ...prev, name: e.target.value } : null))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor="edit-email">邮箱</Label>
                            <Input
                              id="edit-email"
                              type="email"
                              value={editingUser.email}
                              onChange={(e) =>
                                setEditingUser((prev) => (prev ? { ...prev, email: e.target.value } : null))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor="edit-role">角色</Label>
                            <Select
                              value={editingUser.role}
                              onValueChange={(value: any) =>
                                setEditingUser((prev) => (prev ? { ...prev, role: value } : null))
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="student">学生</SelectItem>
                                <SelectItem value="teacher">教师</SelectItem>
                                <SelectItem value="admin">管理员</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex justify-end space-x-2">
                            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                              取消
                            </Button>
                            <Button onClick={handleUpdateUser}>保存更改</Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setPasswordEditUser(userData)
                      setIsPasswordDialogOpen(true)
                    }}
                    className="text-orange-600 hover:text-orange-700"
                  >
                    重置密码
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteUser(userData.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            {filteredUsers.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                {searchTerm || roleFilter !== "all" ? "没有找到匹配的用户" : "暂无用户数据"}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Password Reset Dialog */}
      <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>重置用户密码</DialogTitle>
            <DialogDescription>为用户 "{passwordEditUser?.name}" 设置新密码</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="new-password">新密码</Label>
              <Input
                id="new-password"
                type="password"
                value={passwordData.newPassword}
                onChange={(e) => setPasswordData((prev) => ({ ...prev, newPassword: e.target.value }))}
                placeholder="请输入新密码"
              />
            </div>
            <div>
              <Label htmlFor="confirm-new-password">确认新密码</Label>
              <Input
                id="confirm-new-password"
                type="password"
                value={passwordData.confirmPassword}
                onChange={(e) => setPasswordData((prev) => ({ ...prev, confirmPassword: e.target.value }))}
                placeholder="请再次输入新密码"
              />
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsPasswordDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleResetPassword}>重置密码</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
