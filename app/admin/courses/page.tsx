"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useAuth } from "@/components/auth-provider"
import { BookOpen, Edit, Trash2, Plus, Users } from "lucide-react"

interface Course {
  id: string
  name: string
  description: string
  teacher_id: string
  teacher_name: string
  student_count: number
  created_at: string
}

interface Teacher {
  id: string
  name: string
  email: string
}

export default function AdminCoursesPage() {
  const { user } = useAuth()
  const [courses, setCourses] = useState<Course[]>([])
  const [teachers, setTeachers] = useState<Teacher[]>([])
  const [editingCourse, setEditingCourse] = useState<Course | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [error, setError] = useState("")

  const [newCourse, setNewCourse] = useState({
    name: "",
    description: "",
    teacher_id: "",
  })

  useEffect(() => {
    if (user && user.role === "admin") {
      fetchCourses()
      fetchTeachers()
    }
  }, [user])

  async function fetchCourses() {
    // Mock data for testing
    const mockCourses: Course[] = [
      {
        id: "660e8400-e29b-41d4-a716-446655440001",
        name: "高等数学",
        description: "大学高等数学课程，包含微积分、线性代数等内容",
        teacher_id: "550e8400-e29b-41d4-a716-446655440001",
        teacher_name: "张老师",
        student_count: 25,
        created_at: new Date().toISOString(),
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440002",
        name: "计算机程序设计",
        description: "Python编程基础课程",
        teacher_id: "550e8400-e29b-41d4-a716-446655440001",
        teacher_name: "张老师",
        student_count: 18,
        created_at: new Date().toISOString(),
      },
      {
        id: "660e8400-e29b-41d4-a716-446655440003",
        name: "英语写作",
        description: "学术英语写作技巧与实践",
        teacher_id: "550e8400-e29b-41d4-a716-446655440002",
        teacher_name: "李老师",
        student_count: 12,
        created_at: new Date().toISOString(),
      },
    ]

    setCourses(mockCourses)
  }

  async function fetchTeachers() {
    // Mock teachers data
    const mockTeachers: Teacher[] = [
      {
        id: "550e8400-e29b-41d4-a716-446655440001",
        name: "张老师",
        email: "<EMAIL>",
      },
      {
        id: "550e8400-e29b-41d4-a716-446655440002",
        name: "李老师",
        email: "<EMAIL>",
      },
    ]

    setTeachers(mockTeachers)
  }

  async function handleCreateCourse() {
    if (!newCourse.name || !newCourse.teacher_id) {
      setError("请填写所有必填字段")
      return
    }

    const teacher = teachers.find((t) => t.id === newCourse.teacher_id)
    const newCourseData: Course = {
      id: `course-${Date.now()}`,
      name: newCourse.name,
      description: newCourse.description,
      teacher_id: newCourse.teacher_id,
      teacher_name: teacher?.name || "",
      student_count: 0,
      created_at: new Date().toISOString(),
    }

    setCourses((prev) => [...prev, newCourseData])
    setNewCourse({ name: "", description: "", teacher_id: "" })
    setIsCreateDialogOpen(false)
    setError("")
  }

  async function handleUpdateCourse() {
    if (!editingCourse) return

    const teacher = teachers.find((t) => t.id === editingCourse.teacher_id)
    const updatedCourse = {
      ...editingCourse,
      teacher_name: teacher?.name || editingCourse.teacher_name,
    }

    setCourses((prev) => prev.map((c) => (c.id === editingCourse.id ? updatedCourse : c)))
    setEditingCourse(null)
    setIsEditDialogOpen(false)
    setError("")
  }

  async function handleDeleteCourse(courseId: string) {
    if (confirm("确定要删除这个课程吗？这将同时删除相关的评价和选课记录。")) {
      setCourses((prev) => prev.filter((c) => c.id !== courseId))
    }
  }

  if (!user || user.role !== "admin") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">课程管理</h1>
        <p className="text-gray-600">管理系统中的所有课程信息</p>
      </div>

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>课程操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-end">
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  添加课程
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>添加新课程</DialogTitle>
                  <DialogDescription>创建新的课程</DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="new-course-name">课程名称 *</Label>
                    <Input
                      id="new-course-name"
                      value={newCourse.name}
                      onChange={(e) => setNewCourse((prev) => ({ ...prev, name: e.target.value }))}
                      placeholder="请输入课程名称"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-course-description">课程描述</Label>
                    <Textarea
                      id="new-course-description"
                      value={newCourse.description}
                      onChange={(e) => setNewCourse((prev) => ({ ...prev, description: e.target.value }))}
                      placeholder="请输入课程描述"
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-course-teacher">授课教师 *</Label>
                    <Select
                      value={newCourse.teacher_id}
                      onValueChange={(value) => setNewCourse((prev) => ({ ...prev, teacher_id: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="请选择教师" />
                      </SelectTrigger>
                      <SelectContent>
                        {teachers.map((teacher) => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.name} ({teacher.email})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                      取消
                    </Button>
                    <Button onClick={handleCreateCourse}>创建课程</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Courses List */}
      <Card>
        <CardHeader>
          <CardTitle>课程列表 ({courses.length})</CardTitle>
          <CardDescription>系统中的所有课程</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {courses.map((course) => (
              <div key={course.id} className="flex items-start justify-between p-4 border rounded-lg">
                <div className="flex items-start space-x-4">
                  <BookOpen className="h-8 w-8 text-blue-600 mt-1" />
                  <div>
                    <p className="font-medium text-lg">{course.name}</p>
                    <p className="text-sm text-gray-600 mt-1">{course.description}</p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>教师: {course.teacher_name}</span>
                      <span className="flex items-center">
                        <Users className="h-4 w-4 mr-1" />
                        {course.student_count} 名学生
                      </span>
                      <span>创建时间: {new Date(course.created_at).toLocaleDateString("zh-CN")}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Dialog open={isEditDialogOpen && editingCourse?.id === course.id} onOpenChange={setIsEditDialogOpen}>
                    <DialogTrigger asChild>
                      <Button variant="outline" size="sm" onClick={() => setEditingCourse({ ...course })}>
                        <Edit className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-md">
                      <DialogHeader>
                        <DialogTitle>编辑课程信息</DialogTitle>
                        <DialogDescription>修改课程的基本信息</DialogDescription>
                      </DialogHeader>
                      {editingCourse && (
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="edit-course-name">课程名称</Label>
                            <Input
                              id="edit-course-name"
                              value={editingCourse.name}
                              onChange={(e) =>
                                setEditingCourse((prev) => (prev ? { ...prev, name: e.target.value } : null))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor="edit-course-description">课程描述</Label>
                            <Textarea
                              id="edit-course-description"
                              value={editingCourse.description}
                              onChange={(e) =>
                                setEditingCourse((prev) => (prev ? { ...prev, description: e.target.value } : null))
                              }
                              rows={3}
                            />
                          </div>
                          <div>
                            <Label htmlFor="edit-course-teacher">授课教师</Label>
                            <Select
                              value={editingCourse.teacher_id}
                              onValueChange={(value) =>
                                setEditingCourse((prev) => (prev ? { ...prev, teacher_id: value } : null))
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {teachers.map((teacher) => (
                                  <SelectItem key={teacher.id} value={teacher.id}>
                                    {teacher.name} ({teacher.email})
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="flex justify-end space-x-2">
                            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                              取消
                            </Button>
                            <Button onClick={handleUpdateCourse}>保存更改</Button>
                          </div>
                        </div>
                      )}
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteCourse(course.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}

            {courses.length === 0 && <div className="text-center py-8 text-gray-500">暂无课程数据</div>}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
