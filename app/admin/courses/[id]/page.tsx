"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { useAuth } from "@/components/auth-provider"
import { CourseCalendar } from "@/components/course-calendar"
import { ArrowLeft, Calendar, Clock, Edit, Trash2, User, Users } from "lucide-react"

interface Course {
  id: string
  name: string
  description: string
  teacher_name: string
  teacher_email: string
  student_count: number
  session_count: number
  created_at: string
}

interface Session {
  id: string
  name: string
  date: string
  time: string
  description?: string
}

interface Student {
  id: string
  name: string
  email: string
  enrolled_at: string
}

export default function AdminCourseDetailPage() {
  const { user } = useAuth()
  const params = useParams()
  const router = useRouter()
  const courseId = params.id as string

  const [course, setCourse] = useState<Course | null>(null)
  const [sessions, setSessions] = useState<Session[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    if (user && user.role === "admin") {
      fetchCourseData()
    }
  }, [user, courseId])

  async function fetchCourseData() {
    try {
      // Mock course data
      const mockCourse: Course = {
        id: courseId,
        name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "高等数学" : "计算机程序设计",
        description:
          courseId === "660e8400-e29b-41d4-a716-446655440001"
            ? "大学高等数学课程，包含微积分、线性代数等内容"
            : "Python编程基础课程",
        teacher_name: "张老师",
        teacher_email: "<EMAIL>",
        student_count: courseId === "660e8400-e29b-41d4-a716-446655440001" ? 25 : 18,
        session_count: courseId === "660e8400-e29b-41d4-a716-446655440001" ? 12 : 8,
        created_at: "2024-03-01T00:00:00Z",
      }

      // Mock sessions data
      const mockSessions: Session[] = [
        {
          id: "session-1",
          name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "第一章：极限与连续" : "第一课：Python基础语法",
          date: "2024-06-20",
          time: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "09:00-11:00" : "14:00-16:00",
          description:
            courseId === "660e8400-e29b-41d4-a716-446655440001"
              ? "介绍函数极限的概念和性质"
              : "Python变量、数据类型和基本操作",
        },
        {
          id: "session-2",
          name: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "第二章：导数与微分" : "第二课：控制结构",
          date: "2024-06-22",
          time: courseId === "660e8400-e29b-41d4-a716-446655440001" ? "09:00-11:00" : "14:00-16:00",
          description:
            courseId === "660e8400-e29b-41d4-a716-446655440001" ? "导数的定义和计算方法" : "条件语句和循环结构",
        },
      ]

      // Mock students data
      const mockStudents: Student[] = [
        {
          id: "student-1",
          name: "王小明",
          email: "<EMAIL>",
          enrolled_at: "2024-03-01T00:00:00Z",
        },
        {
          id: "student-2",
          name: "陈小红",
          email: "<EMAIL>",
          enrolled_at: "2024-03-02T00:00:00Z",
        },
      ]

      setCourse(mockCourse)
      setSessions(mockSessions)
      setStudents(mockStudents)
    } catch (error) {
      console.error("Error fetching course data:", error)
    } finally {
      setLoading(false)
    }
  }

  if (!user || user.role !== "admin") {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">您没有访问权限</div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">加载中...</div>
      </div>
    )
  }

  if (!course) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">课程不存在</div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <Button variant="ghost" onClick={() => router.back()} className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{course.name}</h1>
            <p className="text-gray-600">{course.description}</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Edit className="mr-2 h-4 w-4" />
              编辑课程
            </Button>
            <Button variant="outline" className="text-red-600 hover:text-red-700">
              <Trash2 className="mr-2 h-4 w-4" />
              删除课程
            </Button>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">概览</TabsTrigger>
          <TabsTrigger value="schedule">课程安排</TabsTrigger>
          <TabsTrigger value="students">学生管理</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">授课教师</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{course.teacher_name}</div>
                <p className="text-xs text-muted-foreground">{course.teacher_email}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">学生人数</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{course.student_count}</div>
                <p className="text-xs text-muted-foreground">选修该课程的学生</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">课程会话</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{course.session_count}</div>
                <p className="text-xs text-muted-foreground">已安排的课程会话</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">创建时间</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{new Date(course.created_at).toLocaleDateString("zh-CN")}</div>
                <p className="text-xs text-muted-foreground">课程创建日期</p>
              </CardContent>
            </Card>
          </div>

          <CourseCalendar sessions={sessions} courseId={courseId} userRole="admin" />
        </TabsContent>

        {/* Schedule Tab */}
        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>课程安排</CardTitle>
              <CardDescription>管理课程的所有会话安排</CardDescription>
            </CardHeader>
            <CardContent>
              {sessions.length > 0 ? (
                <div className="space-y-4">
                  {sessions.map((session) => (
                    <div key={session.id} className="flex items-start justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold text-lg">{session.name}</h3>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(session.date).toLocaleDateString("zh-CN")}
                          </span>
                          <span className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {session.time}
                          </span>
                        </div>
                        {session.description && <p className="text-sm text-gray-600 mt-2">{session.description}</p>}
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无课程安排</h3>
                  <p className="text-gray-600">该课程还没有安排具体的课程会话</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Students Tab */}
        <TabsContent value="students" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>学生管理</CardTitle>
              <CardDescription>管理选修该课程的学生</CardDescription>
            </CardHeader>
            <CardContent>
              {students.length > 0 ? (
                <div className="space-y-4">
                  {students.map((student) => (
                    <div key={student.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <h3 className="font-semibold">{student.name}</h3>
                        <p className="text-sm text-gray-600">{student.email}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          选课时间: {new Date(student.enrolled_at).toLocaleDateString("zh-CN")}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          查看详情
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          移除学生
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">暂无学生</h3>
                  <p className="text-gray-600">该课程还没有学生选修</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
