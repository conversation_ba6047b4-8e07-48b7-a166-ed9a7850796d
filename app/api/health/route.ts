import { NextResponse } from 'next/server'
import { testConnection } from '@/lib/database'
import { isUsingMockData } from '@/lib/data-access'

export async function GET() {
  const startTime = Date.now()

  try {
    console.log('🏥 开始健康检查...')

    // 基本信息
    const basicInfo = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown',
      usingMockData: isUsingMockData,
      databaseUrl: process.env.DATABASE_URL ? '已配置' : '未配置',
    }

    console.log('📊 基本信息:', basicInfo)

    // 数据库连接测试（快速超时）
    let databaseStatus = false
    let databaseError = null

    if (!isUsingMockData) {
      try {
        console.log('🔍 测试数据库连接...')
        databaseStatus = await Promise.race([
          testConnection(),
          new Promise<boolean>((_, reject) =>
            setTimeout(() => reject(new Error('数据库连接超时')), 2000) // 2秒超时
          )
        ])
        console.log('✅ 数据库连接测试完成:', databaseStatus)
      } catch (error) {
        databaseError = error instanceof Error ? error.message : '未知错误'
        console.error('❌ 数据库连接测试失败:', databaseError)
      }
    } else {
      console.log('⚠️ 使用模拟数据模式，跳过数据库测试')
      databaseStatus = true // 模拟数据模式认为是健康的
    }

    const responseTime = Date.now() - startTime

    const healthStatus = {
      status: databaseStatus ? 'healthy' : 'unhealthy',
      ...basicInfo,
      database: {
        connected: databaseStatus,
        error: databaseError,
        usingMockData: isUsingMockData,
      },
      performance: {
        responseTime: `${responseTime}ms`,
        healthy: responseTime < 3000, // 3秒内认为健康
      },
      services: {
        api: 'ok',
        database: databaseStatus ? 'ok' : (isUsingMockData ? 'mock' : 'error'),
      }
    }

    console.log('🎯 健康检查完成:', healthStatus)

    // 根据状态返回适当的 HTTP 状态码
    const httpStatus = databaseStatus && responseTime < 5000 ? 200 : 503

    return NextResponse.json(healthStatus, { status: httpStatus })

  } catch (error) {
    const responseTime = Date.now() - startTime
    const errorMessage = error instanceof Error ? error.message : '未知错误'

    console.error('💥 健康检查失败:', errorMessage)

    const errorStatus = {
      status: 'error',
      timestamp: new Date().toISOString(),
      error: errorMessage,
      performance: {
        responseTime: `${responseTime}ms`,
        healthy: false,
      },
      services: {
        api: 'error',
        database: 'unknown',
      }
    }

    return NextResponse.json(errorStatus, { status: 500 })
  }
}
