"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { BookOpen, User, GraduationCap, Shield } from "lucide-react"

export default function LoginPage() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()

  async function handleQuickLogin(role: "teacher" | "student" | "admin") {
    setLoading(true)
    setError("")

    try {
      // 模拟用户数据
      const mockUsers = {
        teacher: {
          id: "550e8400-e29b-41d4-a716-446655440001",
          email: "<EMAIL>",
          name: "张老师",
          role: "teacher",
        },
        student: {
          id: "550e8400-e29b-41d4-a716-446655440003",
          email: "<EMAIL>",
          name: "王小明",
          role: "student",
        },
        admin: {
          id: "550e8400-e29b-41d4-a716-446655440006",
          email: "<EMAIL>",
          name: "管理员",
          role: "admin",
        },
      }

      const userData = mockUsers[role]

      // 将用户数据存储到localStorage用于测试
      localStorage.setItem("currentUser", JSON.stringify(userData))

      // 触发自定义事件通知AuthProvider
      window.dispatchEvent(new CustomEvent("userLogin", { detail: userData }))

      // 根据角色重定向
      switch (role) {
        case "teacher":
          router.push("/teacher/dashboard")
          break
        case "student":
          router.push("/student/dashboard")
          break
        case "admin":
          router.push("/admin/dashboard")
          break
      }
    } catch (error: any) {
      setError("登录失败，请重试")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <BookOpen className="mx-auto h-12 w-12 text-blue-600" />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">课程评价系统</h2>
          <p className="mt-2 text-sm text-gray-600">选择您的身份快速登录（测试模式）</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>快速登录</CardTitle>
            <CardDescription>选择您的身份进入系统</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={() => handleQuickLogin("teacher")}
              disabled={loading}
              className="w-full h-16 text-lg"
              variant="default"
            >
              <User className="mr-3 h-6 w-6" />
              <div className="text-left">
                <div className="font-semibold">教师登录</div>
                <div className="text-sm opacity-90">张老师 - 管理课程和评价</div>
              </div>
            </Button>

            <Button
              onClick={() => handleQuickLogin("student")}
              disabled={loading}
              className="w-full h-16 text-lg"
              variant="outline"
            >
              <GraduationCap className="mr-3 h-6 w-6" />
              <div className="text-left">
                <div className="font-semibold">学生登录</div>
                <div className="text-sm opacity-70">王小明 - 查看评价和回复</div>
              </div>
            </Button>

            <Button
              onClick={() => handleQuickLogin("admin")}
              disabled={loading}
              className="w-full h-16 text-lg"
              variant="secondary"
            >
              <Shield className="mr-3 h-6 w-6" />
              <div className="text-left">
                <div className="font-semibold">管理员登录</div>
                <div className="text-sm opacity-70">管理员 - 系统管理</div>
              </div>
            </Button>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="mt-6 text-xs text-gray-500 text-center">
              <p>⚠️ 当前为测试模式，无需真实账号密码</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
