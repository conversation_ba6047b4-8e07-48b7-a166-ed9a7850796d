{"name": "course-evaluation-system", "version": "1.0.0", "description": "基于React和PostgreSQL的课程评价系统", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:start\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "server:start": "cd server && node src/index.js", "client:dev": "cd client && npm run dev", "build": "npm run client:build", "client:build": "cd client && npm run build", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "db:setup": "cd server && npm run db:generate && npm run db:push && npm run db:seed", "db:migrate": "cd server && npx prisma migrate dev", "db:seed": "cd server && npm run db:seed", "db:studio": "cd server && npx prisma studio", "start": "npm run server:start"}, "keywords": ["react", "postgresql", "education", "course-evaluation", "nodejs", "express"], "author": "Course Evaluation System", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}