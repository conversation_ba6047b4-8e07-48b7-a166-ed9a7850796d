{"name": "course-evaluation-system", "version": "1.0.0", "description": "基于React和PostgreSQL的课程评价系统", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "npm run client:build", "client:build": "cd client && npm run build", "server:start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "db:migrate": "cd server && npx prisma migrate dev", "db:seed": "cd server && npx prisma db seed", "db:studio": "cd server && npx prisma studio"}, "keywords": ["react", "postgresql", "education", "course-evaluation", "nodejs", "express"], "author": "Course Evaluation System", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}