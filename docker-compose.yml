# Docker Compose 配置文件
# 注意：version 字段在新版本中已废弃，可以移除

services:
  # Redis 缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: education-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - cache
  # 生产环境服务
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379}
    env_file:
      - .env.production
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 开发环境服务（用于调试）
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**********************************************************/teacher
      - POSTGRES_HOST=************
      - POSTGRES_DB=teacher
      - POSTGRES_USER=HU2CwUKaiG
      - POSTGRES_PASSWORD=uBF_QQgLwPiN3.72
      - POSTGRES_PORT=5034
      - REDIS_URL=${REDIS_URL:-redis://redis:6379}
      - DEBUG=true
      - NEXT_PUBLIC_DEBUG=true
      - NEXT_PUBLIC_DATABASE_TYPE=postgresql
    env_file:
      - .env.local
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - dev

  # Nginx 反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app-network
    profiles:
      - nginx

networks:
  app-network:
    driver: bridge

volumes:
  node_modules:
  next_cache:
  redis_data:
