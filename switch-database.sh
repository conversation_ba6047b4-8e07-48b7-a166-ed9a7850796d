#!/bin/bash

# PostgreSQL 数据库配置脚本
# 用于管理 PostgreSQL 数据库配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "🔄 数据库切换工具"
    echo ""
    echo "使用方法: $0 [supabase|postgresql]"
    echo ""
    echo "可用选项:"
    echo "  supabase     - 切换到 Supabase 数据库"
    echo "  postgresql   - 切换到本地 PostgreSQL 数据库"
    echo "  status       - 显示当前数据库配置状态"
    echo "  help         - 显示此帮助信息"
}

# 检查当前数据库配置
check_current_config() {
    log_info "检查当前数据库配置..."
    
    if [ -f ".env.local" ]; then
        if grep -q "DATABASE_URL" .env.local; then
            echo "✅ 当前使用: PostgreSQL"
            return 0
        elif grep -q "NEXT_PUBLIC_SUPABASE_URL" .env.local; then
            echo "✅ 当前使用: Supabase"
            return 1
        fi
    fi
    
    echo "❓ 无法确定当前数据库配置"
    return 2
}

# 切换到 Supabase
switch_to_supabase() {
    log_info "切换到 Supabase 数据库..."
    
    # 备份当前配置
    if [ -f ".env.local" ]; then
        cp .env.local .env.local.backup.$(date +%Y%m%d_%H%M%S)
        log_info "已备份当前配置"
    fi
    
    # 检查是否有 Supabase 配置备份
    if [ -f ".env.local.supabase" ]; then
        cp .env.local.supabase .env.local
        log_success "已恢复 Supabase 配置"
    else
        # 创建 Supabase 配置模板
        cat > .env.local << EOF
# 开发环境配置文件 - Supabase 版本
# 请填入您的实际 Supabase 项目配置

# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# 开发环境特定配置
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=development

# 调试选项
DEBUG=true
NEXT_PUBLIC_DEBUG=true

# 应用配置
NEXT_PUBLIC_DATABASE_TYPE=supabase
EOF
        log_warning "已创建 Supabase 配置模板，请编辑 .env.local 填入您的实际配置"
    fi
    
    # 停止 PostgreSQL 容器
    log_info "停止 PostgreSQL 容器..."
    docker-compose stop postgres redis 2>/dev/null || true
    
    log_success "已切换到 Supabase 数据库"
    log_info "请重启开发环境: ./deploy.sh dev"
}

# 切换到 PostgreSQL
switch_to_postgresql() {
    log_info "切换到 PostgreSQL 数据库..."
    
    # 备份当前配置
    if [ -f ".env.local" ]; then
        # 如果当前是 Supabase 配置，保存为备份
        if grep -q "NEXT_PUBLIC_SUPABASE_URL" .env.local; then
            cp .env.local .env.local.supabase
            log_info "已备份 Supabase 配置"
        fi
        cp .env.local .env.local.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 使用 PostgreSQL 配置
    if [ -f ".env.local.postgresql" ]; then
        cp .env.local.postgresql .env.local
        log_success "已应用 PostgreSQL 配置"
    else
        log_error ".env.local.postgresql 文件不存在"
        return 1
    fi
    
    log_info "PostgreSQL 配置已应用"
    log_warning "请确保您的外部 PostgreSQL 数据库正在运行"
    log_info "数据库连接信息请在 .env.local 中配置"
    
    log_success "已切换到 PostgreSQL 数据库"
    log_info "请重启开发环境: ./deploy.sh dev"
}

# 显示状态
show_status() {
    echo "📊 数据库配置状态"
    echo "===================="
    
    check_current_config
    
    echo ""
    echo "📁 配置文件:"
    echo "  .env.local              $([ -f ".env.local" ] && echo "✅ 存在" || echo "❌ 不存在")"
    echo "  .env.local.postgresql   $([ -f ".env.local.postgresql" ] && echo "✅ 存在" || echo "❌ 不存在")"
    echo "  .env.local.supabase     $([ -f ".env.local.supabase" ] && echo "✅ 存在" || echo "❌ 不存在")"
    
    echo ""
    echo "🐳 Docker 容器状态:"
    echo "  PostgreSQL              🔗 外部数据库"
    
    if docker-compose ps redis 2>/dev/null | grep -q "Up"; then
        echo "  Redis                   ✅ 运行中"
    else
        echo "  Redis                   ❌ 未运行"
    fi
    
    if docker-compose --profile dev ps app-dev 2>/dev/null | grep -q "Up"; then
        echo "  应用容器                ✅ 运行中"
    else
        echo "  应用容器                ❌ 未运行"
    fi
}

# 主函数
main() {
    case "${1:-help}" in
        "supabase")
            switch_to_supabase
            ;;
        "postgresql")
            switch_to_postgresql
            ;;
        "status")
            show_status
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

main "$@"
