# ⚡ 快速启动指南

## 🎯 5分钟快速部署

### 1. 环境检查
```bash
# 检查 Docker 环境
docker --version
docker-compose --version
```

### 2. 配置 PostgreSQL
1. 访问 [supabase.com](https://supabase.com) 创建项目
2. 获取项目 URL 和 API Keys
3. 在 SQL Editor 中执行数据库脚本：
   ```sql
   -- 依次执行 scripts/ 目录下的 SQL 文件
   -- 01-create-tables.sql
   -- 02-seed-data.sql
   -- 其他脚本...
   ```

### 3. 配置环境变量

**开发环境：**
```bash
cp .env.local.example .env.local
nano .env.local  # 填入您的 PostgreSQL 配置
```

**生产环境：**
```bash
cp .env.production.example .env.production
nano .env.production  # 填入您的 PostgreSQL 配置
```

### 4. 一键启动

**开发环境（推荐）：**
```bash
./deploy.sh dev
# 访问: http://localhost:3001
```

**生产环境：**
```bash
./deploy.sh prod
# 访问: http://localhost:3000
```

## 🔧 常用调试命令

```bash
# 查看实时日志
./debug.sh dev-logs

# 健康检查
./health-check.sh

# 进入容器调试
./debug.sh dev-shell

# 测试数据库连接
./debug.sh test-db

# 检查环境配置
./debug.sh check-env
```

## 🚨 快速故障排除

### 问题1: 容器启动失败
```bash
# 查看错误日志
./debug.sh dev-logs

# 检查环境变量
./debug.sh check-env

# 重置开发环境
./debug.sh reset-dev
```

### 问题2: 无法访问应用
```bash
# 检查端口占用
netstat -tulpn | grep :300

# 检查容器状态
docker-compose ps

# 健康检查
./health-check.sh
```

### 问题3: PostgreSQL 连接失败
```bash
# 测试数据库连接
./debug.sh test-db

# 检查环境变量
./debug.sh check-env

# 查看详细错误
./debug.sh dev-logs
```

## 📋 环境隔离说明

| 环境 | 端口 | 配置文件 | 容器名 | 用途 |
|------|------|----------|--------|------|
| 开发 | 3001 | .env.local | app-dev | 开发调试、热重载 |
| 生产 | 3000 | .env.production | app | 正式部署 |

## 🛠️ 开发工作流

1. **启动开发环境**
   ```bash
   ./deploy.sh dev
   ```

2. **实时查看日志**
   ```bash
   ./debug.sh dev-logs
   ```

3. **代码修改** - 支持热重载，保存即生效

4. **调试问题**
   ```bash
   ./debug.sh dev-shell  # 进入容器
   ./debug.sh test-db    # 测试数据库
   ```

5. **部署到生产**
   ```bash
   ./deploy.sh prod
   ```

## 📞 获取帮助

```bash
# 查看部署脚本帮助
./deploy.sh help

# 查看调试工具帮助
./debug.sh help

# 运行健康检查
./health-check.sh
```

## 🔒 安全提醒

- ❌ 不要将 `.env.local` 和 `.env.production` 提交到版本控制
- ✅ 定期备份环境配置：`./debug.sh backup-env`
- ✅ 使用强密码和安全的 API Keys
- ✅ 定期更新 Docker 镜像和依赖包

---

**🎉 恭喜！您的教育评价平台已成功部署！**

开发环境: http://localhost:3001  
生产环境: http://localhost:3000
