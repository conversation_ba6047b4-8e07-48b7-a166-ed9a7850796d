#!/bin/bash

# 课程评价系统启动脚本

echo "🚀 正在启动课程评价系统..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误：未找到Node.js，请先安装Node.js 18或更高版本"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误：未找到npm，请先安装npm"
    exit 1
fi

# PostgreSQL已配置，跳过检测
echo "✅ PostgreSQL数据库已配置"

echo "📦 正在安装依赖..."

# 安装根目录依赖
npm install

# 安装服务端依赖
echo "📦 安装服务端依赖..."
cd server && npm install

# 安装客户端依赖
echo "📦 安装客户端依赖..."
cd ../client && npm install

# 返回根目录
cd ..

echo "🗄️  正在设置数据库..."

# 检查.env文件是否存在
if [ ! -f "server/.env" ]; then
    echo "⚠️  警告：未找到server/.env文件，请根据server/.env.example创建配置文件"
    echo "📝 正在复制示例配置文件..."
    cp server/.env.example server/.env
    echo "✅ 已创建server/.env文件，请编辑数据库连接信息"
fi

# 生成Prisma客户端
echo "🔧 生成Prisma客户端..."
cd server && npx prisma generate

# 运行数据库迁移
echo "🗄️  运行数据库迁移..."
npx prisma migrate dev --name init

# 填充种子数据
echo "🌱 填充种子数据..."
npx prisma db seed

cd ..

echo "✅ 系统初始化完成！"
echo ""
echo "🎯 启动开发服务器..."
echo "   前端地址: http://localhost:5173"
echo "   后端地址: http://localhost:3000"
echo ""
echo "👤 演示账号："
echo "   教师: teacher / 123456"
echo "   学生: student / 123456"
echo ""
echo "🚀 正在启动服务..."

# 启动开发服务器
npm run dev
