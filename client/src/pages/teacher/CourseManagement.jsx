import { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  InputNumber, 
  Select, 
  message, 
  Space, 
  Tag,
  Popconfirm
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UserOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import api from '../../utils/api'

const { TextArea } = Input
const { Option } = Select

function CourseManagement() {
  const navigate = useNavigate()
  const [courses, setCourses] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCourse, setEditingCourse] = useState(null)
  const [form] = Form.useForm()
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 获取课程列表
  const fetchCourses = async (page = 1, pageSize = 10) => {
    setLoading(true)
    try {
      const response = await api.get('/courses', {
        params: { page, limit: pageSize }
      })
      
      if (response.data.success) {
        setCourses(response.data.data)
        setPagination({
          current: page,
          pageSize,
          total: response.data.pagination.total
        })
      }
    } catch (error) {
      message.error('获取课程列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCourses()
  }, [])

  // 处理表格分页
  const handleTableChange = (pagination) => {
    fetchCourses(pagination.current, pagination.pageSize)
  }

  // 打开创建/编辑模态框
  const openModal = (course = null) => {
    setEditingCourse(course)
    setModalVisible(true)

    if (course) {
      // 将价格从分转换为元显示
      form.setFieldsValue({
        ...course,
        price: course.price / 100
      })
    } else {
      form.resetFields()
    }
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    setEditingCourse(null)
    form.resetFields()
  }

  // 提交表单
  const handleSubmit = async (values) => {
    try {
      // 将价格转换为分（数据库存储单位）
      const submitData = {
        ...values,
        price: values.price * 100
      }

      if (editingCourse) {
        // 更新课程
        await api.put(`/courses/${editingCourse.id}`, submitData)
        message.success('课程更新成功')
      } else {
        // 创建课程
        await api.post('/courses', submitData)
        message.success('课程创建成功')
      }

      closeModal()
      fetchCourses(pagination.current, pagination.pageSize)
    } catch (error) {
      message.error(error.response?.data?.message || '操作失败')
    }
  }

  // 删除课程
  const handleDelete = async (courseId) => {
    try {
      await api.delete(`/courses/${courseId}`)
      message.success('课程删除成功')
      fetchCourses(pagination.current, pagination.pageSize)
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 日语课程类型映射
  const courseTypeMap = {
    'FREE_CONVERSATION': '自由会话',
    'SPEAKING': '口语课程',
    'BUSINESS': '商务日语',
    'WRITING': '写作课程',
    'GRAMMAR': '语法课程',
    'JLPT': 'JLPT N5-1 对策',
    'GRADUATE_INTERVIEW': '研究生面试对策',
    'JOB_INTERVIEW': '求职就职面试',
    'SPEECH_GUIDANCE': '演讲指导',
    'GAOKAO_JAPANESE': '高考日语',
    'OTHER': '其他课程'
  }

  // 表格列定义
  const columns = [
    {
      title: '课程名称',
      dataIndex: 'name',
      key: 'name',
      width: 180
    },
    {
      title: '课程类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
      render: (type) => (
        <Tag color="blue">{courseTypeMap[type] || type}</Tag>
      )
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      width: 100,
      align: 'center',
      render: (price) => (
        <span style={{ color: price === 0 ? '#52c41a' : '#ff6b6b', fontWeight: 'bold' }}>
          {price === 0 ? '免费' : `¥${(price / 100).toFixed(0)}`}
        </span>
      )
    },
    {
      title: '时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 80,
      align: 'center',
      render: (duration) => `${duration}分钟`
    },
    {
      title: '选课人数',
      key: 'enrollmentCount',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Tag color="blue">
          <UserOutlined /> {record._count?.enrollments || 0}
        </Tag>
      )
    },
    {
      title: '评价数量',
      key: 'evaluationCount',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Tag color="green">
          {record._count?.evaluations || 0}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/courses/${record.id}`)}
          >
            详情
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这门课程吗？"
            description="删除后将无法恢复，相关的评价数据也会被删除。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>🎌 日语课程管理</h2>
        <p>管理您的日语课程类型，设置课程价格和时长</p>
      </div>

      {/* 操作按钮 */}
      <Card className="content-card" style={{ marginBottom: '16px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => openModal()}
        >
          创建课程
        </Button>
      </Card>

      {/* 课程列表 */}
      <Card className="content-card">
        <Table
          columns={columns}
          dataSource={courses}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 创建/编辑课程模态框 */}
      <Modal
        title={editingCourse ? '编辑课程' : '创建课程'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="课程名称"
            rules={[{ required: true, message: '请输入课程名称' }]}
          >
            <Input placeholder="请输入课程名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="课程类型"
            rules={[{ required: true, message: '请选择课程类型' }]}
          >
            <Select placeholder="请选择课程类型">
              <Option value="FREE_CONVERSATION">自由会话（免费体验）</Option>
              <Option value="SPEAKING">口语课程</Option>
              <Option value="BUSINESS">商务日语</Option>
              <Option value="WRITING">写作课程</Option>
              <Option value="GRAMMAR">语法课程</Option>
              <Option value="JLPT">JLPT N5-1 对策</Option>
              <Option value="GRADUATE_INTERVIEW">研究生面试对策</Option>
              <Option value="JOB_INTERVIEW">求职就职面试</Option>
              <Option value="SPEECH_GUIDANCE">演讲指导</Option>
              <Option value="GAOKAO_JAPANESE">高考日语</Option>
              <Option value="OTHER">其他课程</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="课程描述"
          >
            <TextArea
              rows={4}
              placeholder="请输入课程描述"
            />
          </Form.Item>

          <Form.Item
            name="price"
            label="课程价格（元）"
            rules={[{ required: true, message: '请输入课程价格' }]}
          >
            <InputNumber
              min={0}
              max={1000}
              placeholder="请输入价格，0表示免费"
              style={{ width: '100%' }}
              formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/¥\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            name="duration"
            label="课程时长（分钟）"
            rules={[{ required: true, message: '请输入课程时长' }]}
          >
            <InputNumber
              min={15}
              max={180}
              placeholder="请输入时长"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={closeModal}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCourse ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default CourseManagement
