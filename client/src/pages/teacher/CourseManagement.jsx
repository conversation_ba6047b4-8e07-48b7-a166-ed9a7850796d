import { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  InputNumber, 
  Select, 
  message, 
  Space, 
  Tag,
  Popconfirm
} from 'antd'
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  UserOutlined
} from '@ant-design/icons'
import api from '../../utils/api'

const { TextArea } = Input
const { Option } = Select

function CourseManagement() {
  const [courses, setCourses] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCourse, setEditingCourse] = useState(null)
  const [form] = Form.useForm()
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 获取课程列表
  const fetchCourses = async (page = 1, pageSize = 10) => {
    setLoading(true)
    try {
      const response = await api.get('/courses', {
        params: { page, limit: pageSize }
      })
      
      if (response.data.success) {
        setCourses(response.data.data)
        setPagination({
          current: page,
          pageSize,
          total: response.data.pagination.total
        })
      }
    } catch (error) {
      message.error('获取课程列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchCourses()
  }, [])

  // 处理表格分页
  const handleTableChange = (pagination) => {
    fetchCourses(pagination.current, pagination.pageSize)
  }

  // 打开创建/编辑模态框
  const openModal = (course = null) => {
    setEditingCourse(course)
    setModalVisible(true)
    
    if (course) {
      form.setFieldsValue(course)
    } else {
      form.resetFields()
    }
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    setEditingCourse(null)
    form.resetFields()
  }

  // 提交表单
  const handleSubmit = async (values) => {
    try {
      if (editingCourse) {
        // 更新课程
        await api.put(`/courses/${editingCourse.id}`, values)
        message.success('课程更新成功')
      } else {
        // 创建课程
        await api.post('/courses', values)
        message.success('课程创建成功')
      }
      
      closeModal()
      fetchCourses(pagination.current, pagination.pageSize)
    } catch (error) {
      message.error(error.response?.data?.message || '操作失败')
    }
  }

  // 删除课程
  const handleDelete = async (courseId) => {
    try {
      await api.delete(`/courses/${courseId}`)
      message.success('课程删除成功')
      fetchCourses(pagination.current, pagination.pageSize)
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '课程代码',
      dataIndex: 'code',
      key: 'code',
      width: 120
    },
    {
      title: '课程名称',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: '学分',
      dataIndex: 'credits',
      key: 'credits',
      width: 80,
      align: 'center'
    },
    {
      title: '学期',
      dataIndex: 'semester',
      key: 'semester',
      width: 120
    },
    {
      title: '年份',
      dataIndex: 'year',
      key: 'year',
      width: 100,
      align: 'center'
    },
    {
      title: '选课人数',
      key: 'enrollmentCount',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Tag color="blue">
          <UserOutlined /> {record._count?.enrollments || 0}
        </Tag>
      )
    },
    {
      title: '评价数量',
      key: 'evaluationCount',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Tag color="green">
          {record._count?.evaluations || 0}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => message.info('查看详情功能开发中...')}
          >
            详情
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这门课程吗？"
            description="删除后将无法恢复，相关的评价数据也会被删除。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>课程管理</h2>
        <p>管理您的课程信息，包括创建、编辑和删除课程</p>
      </div>

      {/* 操作按钮 */}
      <Card className="content-card" style={{ marginBottom: '16px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => openModal()}
        >
          创建课程
        </Button>
      </Card>

      {/* 课程列表 */}
      <Card className="content-card">
        <Table
          columns={columns}
          dataSource={courses}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 创建/编辑课程模态框 */}
      <Modal
        title={editingCourse ? '编辑课程' : '创建课程'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="课程名称"
            rules={[{ required: true, message: '请输入课程名称' }]}
          >
            <Input placeholder="请输入课程名称" />
          </Form.Item>

          <Form.Item
            name="code"
            label="课程代码"
            rules={[{ required: true, message: '请输入课程代码' }]}
          >
            <Input placeholder="请输入课程代码，如：MATH001" />
          </Form.Item>

          <Form.Item
            name="description"
            label="课程描述"
          >
            <TextArea 
              rows={4} 
              placeholder="请输入课程描述" 
            />
          </Form.Item>

          <Form.Item
            name="credits"
            label="学分"
            rules={[{ required: true, message: '请输入学分' }]}
          >
            <InputNumber 
              min={1} 
              max={10} 
              placeholder="请输入学分"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="semester"
            label="学期"
            rules={[{ required: true, message: '请选择学期' }]}
          >
            <Select placeholder="请选择学期">
              <Option value="春季学期">春季学期</Option>
              <Option value="夏季学期">夏季学期</Option>
              <Option value="秋季学期">秋季学期</Option>
              <Option value="冬季学期">冬季学期</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="year"
            label="年份"
            rules={[{ required: true, message: '请输入年份' }]}
          >
            <InputNumber 
              min={2020} 
              max={2030} 
              placeholder="请输入年份"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={closeModal}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCourse ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default CourseManagement
