import { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Rate, 
  message, 
  Space, 
  Tag,
  Divider
} from 'antd'
import { 
  PlusOutlined, 
  EyeOutlined,
  MessageOutlined
} from '@ant-design/icons'
import api from '../../utils/api'
import RichTextEditor from '../../components/RichTextEditor'
import MarkdownRenderer from '../../components/MarkdownRenderer'

const { Option } = Select
const { TextArea } = Input

function EvaluationManagement() {
  const [evaluations, setEvaluations] = useState([])
  const [courses, setCourses] = useState([])
  const [students, setStudents] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [viewModalVisible, setViewModalVisible] = useState(false)
  const [selectedEvaluation, setSelectedEvaluation] = useState(null)
  const [form] = Form.useForm()
  const [replyForm] = Form.useForm()
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 获取评价列表
  const fetchEvaluations = async (page = 1, pageSize = 10) => {
    setLoading(true)
    try {
      const response = await api.get('/evaluations', {
        params: { page, limit: pageSize }
      })
      
      if (response.data.success) {
        setEvaluations(response.data.data)
        setPagination({
          current: page,
          pageSize,
          total: response.data.pagination.total
        })
      }
    } catch (error) {
      message.error('获取评价列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取课程列表
  const fetchCourses = async () => {
    try {
      const response = await api.get('/courses')
      if (response.data.success) {
        setCourses(response.data.data)
      }
    } catch (error) {
      console.error('获取课程列表失败:', error)
    }
  }

  // 获取学生列表
  const fetchStudents = async (courseId) => {
    if (!courseId) {
      setStudents([])
      return
    }
    
    try {
      const response = await api.get('/users/students', {
        params: { courseId }
      })
      if (response.data.success) {
        setStudents(response.data.data)
      }
    } catch (error) {
      console.error('获取学生列表失败:', error)
    }
  }

  useEffect(() => {
    fetchEvaluations()
    fetchCourses()
  }, [])

  // 处理表格分页
  const handleTableChange = (pagination) => {
    fetchEvaluations(pagination.current, pagination.pageSize)
  }

  // 打开创建评价模态框
  const openCreateModal = () => {
    setModalVisible(true)
    form.resetFields()
  }

  // 关闭创建评价模态框
  const closeCreateModal = () => {
    setModalVisible(false)
    form.resetFields()
    setStudents([])
  }

  // 查看评价详情
  const viewEvaluation = async (evaluationId) => {
    try {
      const response = await api.get(`/evaluations/${evaluationId}`)
      if (response.data.success) {
        setSelectedEvaluation(response.data.data)
        setViewModalVisible(true)
      }
    } catch (error) {
      message.error('获取评价详情失败')
    }
  }

  // 提交评价
  const handleSubmit = async (values) => {
    try {
      await api.post('/evaluations', values)
      message.success('评价创建成功')
      closeCreateModal()
      fetchEvaluations(pagination.current, pagination.pageSize)
    } catch (error) {
      message.error(error.response?.data?.message || '创建评价失败')
    }
  }

  // 提交回复
  const handleReply = async (values) => {
    try {
      await api.post(`/evaluations/${selectedEvaluation.id}/reply`, values)
      message.success('回复成功')
      replyForm.resetFields()
      // 重新获取评价详情
      viewEvaluation(selectedEvaluation.id)
    } catch (error) {
      message.error('回复失败')
    }
  }

  // 表格列定义
  const columns = [
    {
      title: '课程',
      dataIndex: ['course', 'name'],
      key: 'courseName',
      width: 150
    },
    {
      title: '学生',
      dataIndex: ['student', 'nickname'],
      key: 'studentName',
      width: 120,
      render: (nickname, record) => nickname || record.student?.username
    },
    {
      title: '评价标题',
      dataIndex: 'title',
      key: 'title',
      width: 200
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating) => (
        <Rate disabled value={rating} style={{ fontSize: '14px' }} />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: '回复状态',
      key: 'replyStatus',
      width: 100,
      render: (_, record) => (
        <Tag color={record.replies?.length > 0 ? 'green' : 'orange'}>
          {record.replies?.length > 0 ? '已回复' : '待回复'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => viewEvaluation(record.id)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<MessageOutlined />}
            onClick={() => viewEvaluation(record.id)}
          >
            回复
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>✍️ 学生评价管理</h2>
        <p>为学生创建详细的学习评价，跟踪学习进度和表现</p>
      </div>

      {/* 操作按钮 */}
      <Card className="content-card" style={{ marginBottom: '16px' }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={openCreateModal}
          size="large"
        >
          📝 为学生写评价
        </Button>
      </Card>

      {/* 评价列表 */}
      <Card className="content-card">
        <Table
          columns={columns}
          dataSource={evaluations}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 创建评价模态框 */}
      <Modal
        title="创建评价"
        open={modalVisible}
        onCancel={closeCreateModal}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="courseId"
            label="选择课程"
            rules={[{ required: true, message: '请选择课程' }]}
          >
            <Select 
              placeholder="请选择课程"
              onChange={fetchStudents}
            >
              {courses.map(course => (
                <Option key={course.id} value={course.id}>
                  {course.name} ({course.type})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="studentId"
            label="选择学生"
            rules={[{ required: true, message: '请选择学生' }]}
          >
            <Select placeholder="请先选择课程">
              {students.map(student => (
                <Option key={student.id} value={student.id}>
                  {student.nickname || student.username} ({student.username})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="评价标题"
            rules={[{ required: true, message: '请输入评价标题' }]}
          >
            <Input placeholder="请输入评价标题" />
          </Form.Item>

          <Form.Item
            name="content"
            label="评价内容"
            rules={[{ required: true, message: '请输入评价内容' }]}
          >
            <RichTextEditor
              placeholder="请输入详细的评价内容，支持富文本格式和文件上传"
            />
          </Form.Item>

          <Form.Item
            name="rating"
            label="评分"
            initialValue={5}
          >
            <Rate />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={closeCreateModal}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建评价
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看评价详情模态框 */}
      <Modal
        title="评价详情"
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedEvaluation && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <h3>{selectedEvaluation.title}</h3>
              <p style={{ color: '#666', marginBottom: '8px' }}>
                课程：{selectedEvaluation.course?.name} |
                学生：{selectedEvaluation.student?.nickname || selectedEvaluation.student?.username} |
                评分：<Rate disabled value={selectedEvaluation.rating} style={{ fontSize: '14px' }} />
              </p>
              <p style={{ color: '#999', fontSize: '12px' }}>
                创建时间：{new Date(selectedEvaluation.createdAt).toLocaleString()}
              </p>
            </div>
            
            <div style={{ marginBottom: '24px' }}>
              <h4>评价内容：</h4>
              <div style={{
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '6px'
              }}>
                <MarkdownRenderer content={selectedEvaluation.content} />
              </div>
            </div>

            <Divider />

            <div style={{ marginBottom: '16px' }}>
              <h4>回复记录：</h4>
              {selectedEvaluation.replies?.length > 0 ? (
                selectedEvaluation.replies.map((reply, index) => (
                  <div key={reply.id} style={{ 
                    marginBottom: '12px',
                    padding: '12px',
                    background: reply.user.role === 'TEACHER' ? '#e6f7ff' : '#f6ffed',
                    borderRadius: '6px'
                  }}>
                    <div style={{ marginBottom: '8px' }}>
                      <strong>{reply.user.nickname || reply.user.username}</strong>
                      <span style={{ color: '#999', marginLeft: '8px', fontSize: '12px' }}>
                        {new Date(reply.createdAt).toLocaleString()}
                      </span>
                    </div>
                    <div>
                      <MarkdownRenderer content={reply.content} />
                    </div>
                  </div>
                ))
              ) : (
                <p style={{ color: '#999' }}>暂无回复</p>
              )}
            </div>

            <Form
              form={replyForm}
              onFinish={handleReply}
            >
              <Form.Item
                name="content"
                rules={[{ required: true, message: '请输入回复内容' }]}
              >
                <RichTextEditor
                  placeholder="请输入回复内容，支持富文本格式和文件上传..."
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Button type="primary" htmlType="submit">
                  发送回复
                </Button>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default EvaluationManagement
