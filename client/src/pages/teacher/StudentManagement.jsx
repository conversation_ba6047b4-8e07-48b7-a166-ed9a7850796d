import { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Input, 
  Select, 
  Space, 
  Tag,
  Avatar,
  Tooltip,
  message
} from 'antd'
import { 
  UserOutlined,
  SearchOutlined,
  EyeOutlined,
  MailOutlined
} from '@ant-design/icons'
import api from '../../utils/api'

const { Search } = Input
const { Option } = Select

function StudentManagement() {
  const [students, setStudents] = useState([])
  const [courses, setCourses] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [selectedCourse, setSelectedCourse] = useState('')
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 获取学生列表
  const fetchStudents = async (page = 1, pageSize = 10, search = '', courseId = '') => {
    setLoading(true)
    try {
      const params = { 
        page, 
        limit: pageSize,
        role: 'STUDENT'
      }
      
      if (search) params.search = search
      if (courseId) params.courseId = courseId

      const response = await api.get('/users', { params })
      
      if (response.data.success) {
        setStudents(response.data.data)
        setPagination({
          current: page,
          pageSize,
          total: response.data.pagination.total
        })
      }
    } catch (error) {
      message.error('获取学生列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取课程列表
  const fetchCourses = async () => {
    try {
      const response = await api.get('/courses')
      if (response.data.success) {
        setCourses(response.data.data)
      }
    } catch (error) {
      console.error('获取课程列表失败:', error)
    }
  }

  useEffect(() => {
    fetchStudents()
    fetchCourses()
  }, [])

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value)
    fetchStudents(1, pagination.pageSize, value, selectedCourse)
  }

  // 处理课程筛选
  const handleCourseFilter = (courseId) => {
    setSelectedCourse(courseId)
    fetchStudents(1, pagination.pageSize, searchText, courseId)
  }

  // 处理表格分页
  const handleTableChange = (pagination) => {
    fetchStudents(pagination.current, pagination.pageSize, searchText, selectedCourse)
  }

  // 查看学生详情
  const viewStudentDetail = (studentId) => {
    message.info('学生详情功能开发中...')
  }

  // 发送邮件
  const sendEmail = (email) => {
    window.open(`mailto:${email}`)
  }

  // 表格列定义
  const columns = [
    {
      title: '头像',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 80,
      render: (avatar, record) => (
        <Avatar 
          src={avatar} 
          icon={<UserOutlined />}
          size="large"
        />
      )
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      render: (name, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{name}</div>
          <div style={{ color: '#999', fontSize: '12px' }}>@{record.username}</div>
        </div>
      )
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      render: (email) => (
        <Tooltip title="点击发送邮件">
          <Button 
            type="link" 
            icon={<MailOutlined />}
            onClick={() => sendEmail(email)}
            style={{ padding: 0 }}
          >
            {email}
          </Button>
        </Tooltip>
      )
    },
    {
      title: '选课数量',
      key: 'courseCount',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Tag color="blue">
          {record._count?.enrollments || 0} 门
        </Tag>
      )
    },
    {
      title: '评价数量',
      key: 'evaluationCount',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Tag color="green">
          {record._count?.evaluations || 0} 条
        </Tag>
      )
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => viewStudentDetail(record.id)}
          >
            详情
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>学生管理</h2>
        <p>查看和管理选课学生信息</p>
      </div>

      {/* 搜索和筛选 */}
      <Card className="content-card" style={{ marginBottom: '16px' }}>
        <Space size="large" wrap>
          <div>
            <span style={{ marginRight: '8px' }}>搜索学生：</span>
            <Search
              placeholder="输入姓名、用户名或邮箱"
              allowClear
              style={{ width: 250 }}
              onSearch={handleSearch}
              enterButton={<SearchOutlined />}
            />
          </div>
          
          <div>
            <span style={{ marginRight: '8px' }}>筛选课程：</span>
            <Select
              placeholder="选择课程"
              allowClear
              style={{ width: 200 }}
              onChange={handleCourseFilter}
            >
              {courses.map(course => (
                <Option key={course.id} value={course.id}>
                  {course.name}
                </Option>
              ))}
            </Select>
          </div>
        </Space>
      </Card>

      {/* 学生列表 */}
      <Card className="content-card">
        <div style={{ marginBottom: '16px' }}>
          <Space>
            <span>共找到 {pagination.total} 名学生</span>
            {selectedCourse && (
              <Tag color="blue" closable onClose={() => handleCourseFilter('')}>
                已筛选课程
              </Tag>
            )}
            {searchText && (
              <Tag color="green" closable onClose={() => handleSearch('')}>
                搜索: {searchText}
              </Tag>
            )}
          </Space>
        </div>
        
        <Table
          columns={columns}
          dataSource={students}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  )
}

export default StudentManagement
