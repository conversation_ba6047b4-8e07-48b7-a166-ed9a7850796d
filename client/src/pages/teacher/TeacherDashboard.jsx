import { useState, useEffect } from 'react'
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  List, 
  Avatar, 
  Tag, 
  Progress, 
  Calendar, 
  Badge, 
  Button, 
  Space, 
  Typography,
  Divider,
  Empty
} from 'antd'
import {
  BookOutlined,
  UserOutlined,
  FileTextOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  PlusOutlined,
  EyeOutlined,
  StarOutlined,
  TeamOutlined,
  CalendarOutlined,
  RiseOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import api from '../../utils/api'

const { Title, Text } = Typography

function TeacherDashboard() {
  const navigate = useNavigate()
  const [stats, setStats] = useState({
    totalCourses: 0,
    totalStudents: 0,
    totalEvaluations: 0,
    avgRating: 0
  })
  const [recentSessions, setRecentSessions] = useState([])
  const [recentEvaluations, setRecentEvaluations] = useState([])
  const [loading, setLoading] = useState(true)

  // 日语课程类型映射
  const courseTypeMap = {
    'FREE_CONVERSATION': '自由会话',
    'SPEAKING': '口语课程',
    'BUSINESS': '商务日语',
    'WRITING': '写作课程',
    'GRAMMAR': '语法课程',
    'JLPT': 'JLPT对策',
    'GRADUATE_INTERVIEW': '研究生面试',
    'JOB_INTERVIEW': '就职面试',
    'SPEECH_GUIDANCE': '演讲指导',
    'GAOKAO_JAPANESE': '高考日语',
    'OTHER': '其他课程'
  }

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // 分批获取数据，避免同时发起太多请求
      // 首先获取课程数据
      const coursesRes = await api.get('/courses')
      if (coursesRes.data.success) {
        setStats(prev => ({ ...prev, totalCourses: coursesRes.data.data.length }))
      }

      // 延迟一点再获取其他数据
      await new Promise(resolve => setTimeout(resolve, 100))

      // 获取课程安排数据
      const sessionsRes = await api.get('/sessions')
      if (sessionsRes.data.success) {
        const sessions = sessionsRes.data.data
        setRecentSessions(sessions.slice(0, 5))

        // 计算学生数量（去重）
        const uniqueStudents = new Set(sessions.map(s => s.studentId))
        setStats(prev => ({ ...prev, totalStudents: uniqueStudents.size }))
      }

      // 再延迟一点获取评价数据
      await new Promise(resolve => setTimeout(resolve, 100))

      // 获取评价数据
      const evaluationsRes = await api.get('/evaluations?page=1&limit=10')
      if (evaluationsRes.data.success) {
        const evaluations = evaluationsRes.data.data
        setRecentEvaluations(evaluations.slice(0, 5))
        setStats(prev => ({
          ...prev,
          totalEvaluations: evaluations.length,
          avgRating: evaluations.length > 0
            ? (evaluations.reduce((sum, e) => sum + e.rating, 0) / evaluations.length).toFixed(1)
            : 0
        }))
      }
    } catch (error) {
      console.error('获取仪表盘数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // 防止重复调用
    let isMounted = true

    const loadData = async () => {
      if (isMounted) {
        await fetchDashboardData()
      }
    }

    loadData()

    return () => {
      isMounted = false
    }
  }, [])

  // 日历数据处理
  const getCalendarData = (value) => {
    const dateStr = value.format('YYYY-MM-DD')
    const daySessions = recentSessions.filter(session => 
      dayjs(session.sessionDate).format('YYYY-MM-DD') === dateStr
    )
    
    return daySessions.map(session => ({
      type: session.status === 'COMPLETED' ? 'success' : 
            session.status === 'CANCELLED' ? 'error' : 'processing',
      content: `${session.title}`
    }))
  }

  // 日历单元格渲染
  const dateCellRender = (value) => {
    const listData = getCalendarData(value)
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {listData.map((item, index) => (
          <li key={index}>
            <Badge status={item.type} text={item.content} />
          </li>
        ))}
      </ul>
    )
  }

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 欢迎标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, color: '#ff6b6b' }}>
          🎌 日语教师工作台
        </Title>
        <Text type="secondary">
          欢迎回来！今天是 {dayjs().format('YYYY年MM月DD日')}
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="课程类型"
              value={stats.totalCourses}
              prefix={<BookOutlined style={{ color: '#ff6b6b' }} />}
              suffix="种"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="学生总数"
              value={stats.totalStudents}
              prefix={<TeamOutlined style={{ color: '#52c41a' }} />}
              suffix="人"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="评价总数"
              value={stats.totalEvaluations}
              prefix={<MessageOutlined style={{ color: '#1890ff' }} />}
              suffix="条"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均评分"
              value={stats.avgRating}
              prefix={<StarOutlined style={{ color: '#faad14' }} />}
              suffix="/ 5.0"
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card style={{ marginBottom: '24px' }}>
        <Title level={4}>🚀 快速操作</Title>
        <Space wrap>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/courses')}
          >
            创建课程
          </Button>
          <Button 
            icon={<CalendarOutlined />}
            onClick={() => navigate('/sessions')}
          >
            安排课程
          </Button>
          <Button 
            icon={<FileTextOutlined />}
            onClick={() => navigate('/evaluations')}
          >
            写评价
          </Button>
          <Button 
            icon={<TeamOutlined />}
            onClick={() => navigate('/students')}
          >
            学生管理
          </Button>
        </Space>
      </Card>

      <Row gutter={[16, 16]}>
        {/* 最近课程安排 */}
        <Col xs={24} lg={12}>
          <Card 
            title="📅 最近课程安排" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/sessions')}
              >
                查看全部
              </Button>
            }
          >
            {recentSessions.length > 0 ? (
              <List
                dataSource={recentSessions}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={<ClockCircleOutlined />} />}
                      title={item.title}
                      description={
                        <Space direction="vertical" size={4}>
                          <Text type="secondary">
                            学生：{item.student?.nickname || item.student?.username}
                          </Text>
                          <Text type="secondary">
                            时间：{dayjs(item.sessionDate).format('MM-DD HH:mm')}
                          </Text>
                        </Space>
                      }
                    />
                    <Tag color={
                      item.status === 'COMPLETED' ? 'green' :
                      item.status === 'CANCELLED' ? 'red' : 'blue'
                    }>
                      {item.status === 'COMPLETED' ? '已完成' :
                       item.status === 'CANCELLED' ? '已取消' : '已安排'}
                    </Tag>
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无课程安排" />
            )}
          </Card>
        </Col>

        {/* 最近评价 */}
        <Col xs={24} lg={12}>
          <Card 
            title="💬 最近评价" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/evaluations')}
              >
                查看全部
              </Button>
            }
          >
            {recentEvaluations.length > 0 ? (
              <List
                dataSource={recentEvaluations}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={<UserOutlined />} />}
                      title={item.title}
                      description={
                        <Space direction="vertical" size={4}>
                          <Text type="secondary">
                            学生：{item.student?.nickname || item.student?.username}
                          </Text>
                          <Text type="secondary">
                            课程：{item.course?.name}
                          </Text>
                          <div>
                            {[...Array(5)].map((_, i) => (
                              <StarOutlined 
                                key={i} 
                                style={{ 
                                  color: i < item.rating ? '#faad14' : '#d9d9d9' 
                                }} 
                              />
                            ))}
                          </div>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无评价记录" />
            )}
          </Card>
        </Col>
      </Row>

      {/* 课程日历 */}
      <Card style={{ marginTop: '24px' }} title="📅 课程日历">
        <Calendar 
          dateCellRender={dateCellRender}
          style={{ background: '#fff' }}
        />
      </Card>
    </div>
  )
}

export default TeacherDashboard
