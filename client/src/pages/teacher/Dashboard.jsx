import { useState, useEffect } from 'react'
import { Row, Col, Card, Statistic, List, Avatar, Tag, Progress, Calendar, Badge } from 'antd'
import {
  BookOutlined,
  UserOutlined,
  FileTextOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  MessageOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

function TeacherDashboard() {
  const [stats, setStats] = useState({
    totalCourses: 8,
    totalStudents: 156,
    totalEvaluations: 342,
    avgRating: 4.6
  })

  const [recentEvaluations] = useState([
    {
      id: 1,
      studentName: '张三',
      courseName: '高等数学',
      rating: 5,
      comment: '老师讲解清晰，课程内容丰富',
      createdAt: '2024-01-15'
    },
    {
      id: 2,
      studentName: '李四',
      courseName: '线性代数',
      rating: 4,
      comment: '课程难度适中，希望增加更多练习',
      createdAt: '2024-01-14'
    },
    {
      id: 3,
      studentName: '王五',
      courseName: '概率论',
      rating: 5,
      comment: '非常喜欢这门课程',
      createdAt: '2024-01-13'
    }
  ])

  const [upcomingClasses] = useState([
    {
      id: 1,
      courseName: '高等数学',
      time: '09:00-10:40',
      classroom: '教学楼A101',
      studentCount: 45
    },
    {
      id: 2,
      courseName: '线性代数',
      time: '14:00-15:40',
      classroom: '教学楼B203',
      studentCount: 38
    }
  ])

  // 日历数据
  const getListData = (value) => {
    const dateStr = value.format('YYYY-MM-DD')
    const events = {
      '2024-01-18': [
        { type: 'success', content: '高等数学课程' },
        { type: 'warning', content: '作业批改' }
      ],
      '2024-01-19': [
        { type: 'error', content: '期末考试' }
      ],
      '2024-01-20': [
        { type: 'success', content: '线性代数课程' }
      ]
    }
    return events[dateStr] || []
  }

  const dateCellRender = (value) => {
    const listData = getListData(value)
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {listData.map((item, index) => (
          <li key={index}>
            <Badge status={item.type} text={item.content} />
          </li>
        ))}
      </ul>
    )
  }

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>教师工作台</h2>
        <p>欢迎回来！今天是 {dayjs().format('YYYY年MM月DD日 dddd')}</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="管理课程"
              value={stats.totalCourses}
              prefix={<BookOutlined style={{ color: '#1890ff' }} />}
              suffix="门"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="学生总数"
              value={stats.totalStudents}
              prefix={<UserOutlined style={{ color: '#52c41a' }} />}
              suffix="人"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="评价总数"
              value={stats.totalEvaluations}
              prefix={<FileTextOutlined style={{ color: '#faad14' }} />}
              suffix="条"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="平均评分"
              value={stats.avgRating}
              prefix={<TrophyOutlined style={{ color: '#f5222d' }} />}
              suffix="/ 5.0"
              precision={1}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 今日课程安排 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <ClockCircleOutlined style={{ marginRight: '8px' }} />
                今日课程安排
              </span>
            }
            className="content-card"
          >
            <List
              dataSource={upcomingClasses}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<BookOutlined />} style={{ backgroundColor: '#1890ff' }} />}
                    title={item.courseName}
                    description={
                      <div>
                        <div>时间：{item.time}</div>
                        <div>地点：{item.classroom}</div>
                        <div>学生：{item.studentCount}人</div>
                      </div>
                    }
                  />
                  <Tag color="blue">进行中</Tag>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 最新评价 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <MessageOutlined style={{ marginRight: '8px' }} />
                最新学生评价
              </span>
            }
            className="content-card"
          >
            <List
              dataSource={recentEvaluations}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>{item.studentName} - {item.courseName}</span>
                        <div>
                          {'★'.repeat(item.rating)}
                          <span style={{ color: '#faad14', marginLeft: '4px' }}>
                            {item.rating}.0
                          </span>
                        </div>
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: '4px' }}>{item.comment}</div>
                        <small style={{ color: '#999' }}>{item.createdAt}</small>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 课程进度 */}
        <Col xs={24} lg={12}>
          <Card title="课程进度" className="content-card">
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>高等数学</span>
                <span>75%</span>
              </div>
              <Progress percent={75} status="active" />
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>线性代数</span>
                <span>60%</span>
              </div>
              <Progress percent={60} />
            </div>
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>概率论</span>
                <span>45%</span>
              </div>
              <Progress percent={45} />
            </div>
          </Card>
        </Col>

        {/* 课程日历 */}
        <Col xs={24} lg={12}>
          <Card title="课程日历" className="content-card">
            <Calendar 
              fullscreen={false} 
              dateCellRender={dateCellRender}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default TeacherDashboard
