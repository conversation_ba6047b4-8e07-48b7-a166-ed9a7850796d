import { useState, useEffect } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Descriptions, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Avatar, 
  List,
  Calendar,
  Badge,
  Modal,
  Form,
  Input,
  DatePicker,
  InputNumber,
  Select,
  message,
  Typography,
  Divider,
  Statistic
} from 'antd'
import { 
  ArrowLeftOutlined,
  EditOutlined,
  PlusOutlined,
  UserOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  StarOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import api from '../../utils/api'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

function CourseDetail() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [course, setCourse] = useState(null)
  const [sessions, setSessions] = useState([])
  const [students, setStudents] = useState([])
  const [loading, setLoading] = useState(true)
  const [sessionModalVisible, setSessionModalVisible] = useState(false)
  const [form] = Form.useForm()

  // 日语课程类型映射
  const courseTypeMap = {
    'FREE_CONVERSATION': '自由会话',
    'SPEAKING': '口语课程',
    'BUSINESS': '商务日语',
    'WRITING': '写作课程',
    'GRAMMAR': '语法课程',
    'JLPT': 'JLPT N5-1 对策',
    'GRADUATE_INTERVIEW': '研究生面试对策',
    'JOB_INTERVIEW': '求职就职面试',
    'SPEECH_GUIDANCE': '演讲指导',
    'GAOKAO_JAPANESE': '高考日语',
    'OTHER': '其他课程'
  }

  // 获取课程详情
  const fetchCourseDetail = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/courses/${id}`)
      if (response.data.success) {
        setCourse(response.data.data)
      }
    } catch (error) {
      message.error('获取课程详情失败')
      navigate('/courses')
    } finally {
      setLoading(false)
    }
  }

  // 获取课程安排
  const fetchSessions = async () => {
    try {
      const response = await api.get(`/sessions?courseId=${id}`)
      if (response.data.success) {
        setSessions(response.data.data)
      }
    } catch (error) {
      console.error('获取课程安排失败:', error)
    }
  }

  // 获取学生列表
  const fetchStudents = async () => {
    try {
      const response = await api.get('/users/students')
      if (response.data.success) {
        setStudents(response.data.data)
      }
    } catch (error) {
      console.error('获取学生列表失败:', error)
    }
  }

  useEffect(() => {
    if (id) {
      fetchCourseDetail()
      fetchSessions()
      fetchStudents()
    }
  }, [id])

  // 创建课程安排
  const handleCreateSession = async (values) => {
    try {
      const sessionData = {
        ...values,
        courseId: id,
        sessionDate: values.sessionDate.toISOString()
      }
      
      await api.post('/sessions', sessionData)
      message.success('课程安排创建成功')
      setSessionModalVisible(false)
      form.resetFields()
      fetchSessions()
    } catch (error) {
      message.error('创建课程安排失败')
    }
  }

  // 日历数据处理
  const getCalendarData = (value) => {
    const dateStr = value.format('YYYY-MM-DD')
    const daySessions = sessions.filter(session => 
      dayjs(session.sessionDate).format('YYYY-MM-DD') === dateStr
    )
    
    return daySessions.map(session => ({
      type: session.status === 'COMPLETED' ? 'success' : 
            session.status === 'CANCELLED' ? 'error' : 'processing',
      content: `${session.student?.nickname || session.student?.username}`
    }))
  }

  // 日历单元格渲染
  const dateCellRender = (value) => {
    const listData = getCalendarData(value)
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {listData.map((item, index) => (
          <li key={index}>
            <Badge status={item.type} text={item.content} />
          </li>
        ))}
      </ul>
    )
  }

  // 课程安排表格列
  const sessionColumns = [
    {
      title: '学生',
      dataIndex: ['student', 'nickname'],
      key: 'student',
      render: (nickname, record) => nickname || record.student?.username
    },
    {
      title: '课程标题',
      dataIndex: 'title',
      key: 'title'
    },
    {
      title: '上课时间',
      dataIndex: 'sessionDate',
      key: 'sessionDate',
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '时长',
      dataIndex: 'duration',
      key: 'duration',
      render: (duration) => `${duration}分钟`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'SCHEDULED': { text: '已安排', color: 'blue' },
          'COMPLETED': { text: '已完成', color: 'green' },
          'CANCELLED': { text: '已取消', color: 'red' },
          'RESCHEDULED': { text: '已改期', color: 'orange' }
        }
        return (
          <Tag color={statusMap[status]?.color}>
            {statusMap[status]?.text || status}
          </Tag>
        )
      }
    }
  ]

  if (loading || !course) {
    return <div>加载中...</div>
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 返回按钮 */}
      <Button 
        icon={<ArrowLeftOutlined />} 
        onClick={() => navigate('/courses')}
        style={{ marginBottom: '16px' }}
      >
        返回课程列表
      </Button>

      {/* 课程基本信息 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Title level={2} style={{ margin: 0, color: '#ff6b6b' }}>
              🎌 {course.name}
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              {courseTypeMap[course.type]}
            </Text>
            
            <Divider />
            
            <Descriptions column={2}>
              <Descriptions.Item label="课程类型">
                <Tag color="blue">{courseTypeMap[course.type]}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="课程价格">
                <Text style={{ color: course.price === 0 ? '#52c41a' : '#ff6b6b', fontWeight: 'bold' }}>
                  {course.price === 0 ? '免费体验' : `¥${(course.price / 100).toFixed(0)}`}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="课程时长">
                {course.duration}分钟
              </Descriptions.Item>
              <Descriptions.Item label="任课教师">
                {course.teacher?.nickname || course.teacher?.username}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(course.createdAt).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="课程状态">
                <Tag color={course.isActive ? 'green' : 'red'}>
                  {course.isActive ? '活跃' : '暂停'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>

            {course.description && (
              <>
                <Divider />
                <div>
                  <Title level={4}>课程描述</Title>
                  <Text>{course.description}</Text>
                </div>
              </>
            )}
          </Col>
          
          <Col xs={24} lg={8}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="选课学生"
                  value={course.enrollments?.length || 0}
                  prefix={<TeamOutlined />}
                  suffix="人"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="课程安排"
                  value={sessions.length}
                  prefix={<CalendarOutlined />}
                  suffix="节"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="已完成"
                  value={sessions.filter(s => s.status === 'COMPLETED').length}
                  prefix={<StarOutlined />}
                  suffix="节"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="评价数量"
                  value={course._count?.evaluations || 0}
                  prefix={<FileTextOutlined />}
                  suffix="条"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* 课程安排管理 */}
      <Card 
        title="📅 课程安排管理" 
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setSessionModalVisible(true)}
          >
            新增课程安排
          </Button>
        }
        style={{ marginBottom: '24px' }}
      >
        <Table
          columns={sessionColumns}
          dataSource={sessions}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* 课程日历 */}
      <Card title="📅 课程日历">
        <Calendar 
          dateCellRender={dateCellRender}
        />
      </Card>

      {/* 新增课程安排模态框 */}
      <Modal
        title="新增课程安排"
        open={sessionModalVisible}
        onCancel={() => {
          setSessionModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateSession}
        >
          <Form.Item
            name="studentId"
            label="选择学生"
            rules={[{ required: true, message: '请选择学生' }]}
          >
            <Select placeholder="请选择学生">
              {course.enrollments?.map(enrollment => (
                <Option key={enrollment.student.id} value={enrollment.student.id}>
                  {enrollment.student.nickname || enrollment.student.username}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="课程标题"
            rules={[{ required: true, message: '请输入课程标题' }]}
          >
            <Input placeholder="请输入课程标题" />
          </Form.Item>

          <Form.Item
            name="sessionDate"
            label="上课时间"
            rules={[{ required: true, message: '请选择上课时间' }]}
          >
            <DatePicker 
              showTime 
              format="YYYY-MM-DD HH:mm"
              placeholder="请选择上课时间"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="duration"
            label="课程时长（分钟）"
            initialValue={course.duration}
            rules={[{ required: true, message: '请输入课程时长' }]}
          >
            <InputNumber 
              min={15} 
              max={180} 
              placeholder="请输入时长"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="location"
            label="上课地点"
          >
            <Input placeholder="如：线上教室、教学楼A101等" />
          </Form.Item>

          <Form.Item
            name="description"
            label="课程描述"
          >
            <TextArea 
              rows={3} 
              placeholder="请输入课程描述或备注" 
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setSessionModalVisible(false)
                form.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default CourseDetail
