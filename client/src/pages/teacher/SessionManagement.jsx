import { useState, useEffect } from 'react'
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  DatePicker, 
  InputNumber,
  message, 
  Space, 
  Tag,
  Calendar,
  Badge,
  Tooltip
} from 'antd'
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  CalendarOutlined,
  ClockCircleOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'
import api from '../../utils/api'

const { TextArea } = Input
const { Option } = Select

function SessionManagement() {
  const [sessions, setSessions] = useState([])
  const [courses, setCourses] = useState([])
  const [students, setStudents] = useState([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [calendarVisible, setCalendarVisible] = useState(false)
  const [editingSession, setEditingSession] = useState(null)
  const [form] = Form.useForm()
  const [selectedCourse, setSelectedCourse] = useState('')

  // 课程状态映射
  const statusMap = {
    'SCHEDULED': { text: '已安排', color: 'blue' },
    'COMPLETED': { text: '已完成', color: 'green' },
    'CANCELLED': { text: '已取消', color: 'red' },
    'RESCHEDULED': { text: '已改期', color: 'orange' }
  }

  // 获取课程安排列表
  const fetchSessions = async () => {
    setLoading(true)
    try {
      const response = await api.get('/sessions')
      if (response.data.success) {
        setSessions(response.data.data)
      }
    } catch (error) {
      message.error('获取课程安排失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取课程列表
  const fetchCourses = async () => {
    try {
      const response = await api.get('/courses')
      if (response.data.success) {
        setCourses(response.data.data)
      }
    } catch (error) {
      console.error('获取课程列表失败:', error)
    }
  }

  // 获取学生列表
  const fetchStudents = async (courseId) => {
    if (!courseId) {
      setStudents([])
      return
    }
    
    try {
      const response = await api.get('/users/students', {
        params: { courseId }
      })
      if (response.data.success) {
        setStudents(response.data.data)
      }
    } catch (error) {
      console.error('获取学生列表失败:', error)
    }
  }

  useEffect(() => {
    fetchSessions()
    fetchCourses()
  }, [])

  // 打开创建/编辑模态框
  const openModal = (session = null) => {
    setEditingSession(session)
    setModalVisible(true)
    
    if (session) {
      form.setFieldsValue({
        ...session,
        sessionDate: dayjs(session.sessionDate)
      })
      fetchStudents(session.courseId)
    } else {
      form.resetFields()
      setStudents([])
    }
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    setEditingSession(null)
    form.resetFields()
    setStudents([])
    setSelectedCourse('')
  }

  // 处理课程选择
  const handleCourseChange = (courseId) => {
    setSelectedCourse(courseId)
    fetchStudents(courseId)
    form.setFieldsValue({ studentId: undefined })
  }

  // 提交表单
  const handleSubmit = async (values) => {
    try {
      const submitData = {
        ...values,
        sessionDate: values.sessionDate.toISOString()
      }
      
      if (editingSession) {
        await api.put(`/sessions/${editingSession.id}`, submitData)
        message.success('课程安排更新成功')
      } else {
        await api.post('/sessions', submitData)
        message.success('课程安排创建成功')
      }
      
      closeModal()
      fetchSessions()
    } catch (error) {
      message.error(error.response?.data?.message || '操作失败')
    }
  }

  // 删除课程安排
  const handleDelete = async (sessionId) => {
    try {
      await api.delete(`/sessions/${sessionId}`)
      message.success('课程安排删除成功')
      fetchSessions()
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 日历数据处理
  const getCalendarData = (value) => {
    const dateStr = value.format('YYYY-MM-DD')
    const daySessions = sessions.filter(session => 
      dayjs(session.sessionDate).format('YYYY-MM-DD') === dateStr
    )
    
    return daySessions.map(session => ({
      type: session.status === 'COMPLETED' ? 'success' : 
            session.status === 'CANCELLED' ? 'error' : 'processing',
      content: `${session.title} - ${session.student?.nickname || '未知学生'}`
    }))
  }

  // 日历单元格渲染
  const dateCellRender = (value) => {
    const listData = getCalendarData(value)
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {listData.map((item, index) => (
          <li key={index}>
            <Badge status={item.type} text={item.content} />
          </li>
        ))}
      </ul>
    )
  }

  // 表格列定义
  const columns = [
    {
      title: '课程',
      dataIndex: ['course', 'name'],
      key: 'courseName',
      width: 150
    },
    {
      title: '学生',
      dataIndex: ['student', 'nickname'],
      key: 'studentName',
      width: 100
    },
    {
      title: '课程标题',
      dataIndex: 'title',
      key: 'title',
      width: 200
    },
    {
      title: '上课时间',
      dataIndex: 'sessionDate',
      key: 'sessionDate',
      width: 150,
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 80,
      align: 'center',
      render: (duration) => `${duration}分钟`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={statusMap[status]?.color}>
          {statusMap[status]?.text || status}
        </Tag>
      )
    },
    {
      title: '地点',
      dataIndex: 'location',
      key: 'location',
      width: 120
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => openModal(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>📅 课程安排管理</h2>
        <p>管理日语课程的时间安排和学生分配</p>
      </div>

      {/* 操作按钮 */}
      <Card className="content-card" style={{ marginBottom: '16px' }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openModal()}
          >
            新增课程安排
          </Button>
          <Button
            icon={<CalendarOutlined />}
            onClick={() => setCalendarVisible(true)}
          >
            日历视图
          </Button>
        </Space>
      </Card>

      {/* 课程安排列表 */}
      <Card className="content-card">
        <Table
          columns={columns}
          dataSource={sessions}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 创建/编辑课程安排模态框 */}
      <Modal
        title={editingSession ? '编辑课程安排' : '新增课程安排'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="courseId"
            label="选择课程"
            rules={[{ required: true, message: '请选择课程' }]}
          >
            <Select 
              placeholder="请选择课程"
              onChange={handleCourseChange}
            >
              {courses.map(course => (
                <Option key={course.id} value={course.id}>
                  {course.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="studentId"
            label="选择学生"
            rules={[{ required: true, message: '请选择学生' }]}
          >
            <Select placeholder="请先选择课程">
              {students.map(student => (
                <Option key={student.id} value={student.id}>
                  {student.nickname || student.username}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="课程标题"
            rules={[{ required: true, message: '请输入课程标题' }]}
          >
            <Input placeholder="请输入课程标题" />
          </Form.Item>

          <Form.Item
            name="sessionDate"
            label="上课时间"
            rules={[{ required: true, message: '请选择上课时间' }]}
          >
            <DatePicker 
              showTime 
              format="YYYY-MM-DD HH:mm"
              placeholder="请选择上课时间"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="duration"
            label="课程时长（分钟）"
            rules={[{ required: true, message: '请输入课程时长' }]}
          >
            <InputNumber 
              min={15} 
              max={180} 
              placeholder="请输入时长"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="location"
            label="上课地点"
          >
            <Input placeholder="如：线上教室、教学楼A101等" />
          </Form.Item>

          <Form.Item
            name="status"
            label="课程状态"
            initialValue="SCHEDULED"
          >
            <Select>
              <Option value="SCHEDULED">已安排</Option>
              <Option value="COMPLETED">已完成</Option>
              <Option value="CANCELLED">已取消</Option>
              <Option value="RESCHEDULED">已改期</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="课程描述"
          >
            <TextArea 
              rows={3} 
              placeholder="请输入课程描述或备注" 
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={closeModal}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingSession ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 日历视图模态框 */}
      <Modal
        title={
          <span>
            <CalendarOutlined style={{ marginRight: '8px' }} />
            课程安排日历
          </span>
        }
        open={calendarVisible}
        onCancel={() => setCalendarVisible(false)}
        footer={null}
        width={800}
      >
        <Calendar 
          dateCellRender={dateCellRender}
        />
      </Modal>
    </div>
  )
}

export default SessionManagement
