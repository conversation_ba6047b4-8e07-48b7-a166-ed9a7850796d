import { useState } from 'react'
import { Form, Input, Button, Card, Select, message, Tabs } from 'antd'
import { UserOutlined, LockOutlined, BookOutlined } from '@ant-design/icons'
import { useAuthStore } from '../store/authStore'

const { Option } = Select
const { TabPane } = Tabs

function LoginPage() {
  const [loginForm] = Form.useForm()
  const [registerForm] = Form.useForm()
  const { login, register, loading } = useAuthStore()

  // 处理登录
  const handleLogin = async (values) => {
    const result = await login(values)
    if (result.success) {
      message.success('登录成功！')
    } else {
      message.error(result.message)
    }
  }

  // 处理注册
  const handleRegister = async (values) => {
    const result = await register(values)
    if (result.success) {
      message.success('注册成功！')
    } else {
      message.error(result.message)
    }
  }

  return (
    <div className="login-container">
      <Card className="login-card">
        <div className="login-header">
          <BookOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
          <h1>课程评价系统</h1>
          <p>促进师生互动，提升教学质量</p>
        </div>
        
        <div className="login-form">
          <Tabs defaultActiveKey="login" centered>
            <TabPane tab="登录" key="login">
              <Form
                form={loginForm}
                name="login"
                onFinish={handleLogin}
                autoComplete="off"
                size="large"
              >
                <Form.Item
                  name="role"
                  rules={[{ required: true, message: '请选择用户类型!' }]}
                >
                  <Select placeholder="请选择用户类型">
                    <Option value="teacher">教师</Option>
                    <Option value="student">学生</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="username"
                  rules={[{ required: true, message: '请输入用户名!' }]}
                >
                  <Input 
                    prefix={<UserOutlined />} 
                    placeholder="用户名" 
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[{ required: true, message: '请输入密码!' }]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="密码"
                  />
                </Form.Item>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    block
                  >
                    登录
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>

            <TabPane tab="注册" key="register">
              <Form
                form={registerForm}
                name="register"
                onFinish={handleRegister}
                autoComplete="off"
                size="large"
              >
                <Form.Item
                  name="role"
                  rules={[{ required: true, message: '请选择用户类型!' }]}
                >
                  <Select placeholder="请选择用户类型">
                    <Option value="teacher">教师</Option>
                    <Option value="student">学生</Option>
                  </Select>
                </Form.Item>

                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名!' },
                    { min: 3, message: '用户名至少3个字符!' }
                  ]}
                >
                  <Input 
                    prefix={<UserOutlined />} 
                    placeholder="用户名" 
                  />
                </Form.Item>

                <Form.Item
                  name="name"
                  rules={[{ required: true, message: '请输入真实姓名!' }]}
                >
                  <Input 
                    prefix={<UserOutlined />} 
                    placeholder="真实姓名" 
                  />
                </Form.Item>

                <Form.Item
                  name="email"
                  rules={[
                    { required: true, message: '请输入邮箱!' },
                    { type: 'email', message: '请输入有效的邮箱地址!' }
                  ]}
                >
                  <Input 
                    prefix={<UserOutlined />} 
                    placeholder="邮箱地址" 
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码!' },
                    { min: 6, message: '密码至少6个字符!' }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="密码"
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认密码!' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve()
                        }
                        return Promise.reject(new Error('两次输入的密码不一致!'))
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="确认密码"
                  />
                </Form.Item>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    block
                  >
                    注册
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>
          </Tabs>

          <div style={{ textAlign: 'center', marginTop: '20px', color: '#666' }}>
            <p>演示账号：</p>
            <p>教师：teacher / 123456</p>
            <p>学生：student / 123456</p>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default LoginPage
