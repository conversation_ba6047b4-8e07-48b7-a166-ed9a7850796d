import { useState } from 'react'
import { Form, Input, Button, Card, Select, message, Tabs } from 'antd'
import { UserOutlined, LockOutlined, BookOutlined } from '@ant-design/icons'
import { useAuthStore } from '../store/authStore'

const { Option } = Select
const { TabPane } = Tabs

function LoginPage() {
  const [loginForm] = Form.useForm()
  const [registerForm] = Form.useForm()
  const { login, register, loading } = useAuthStore()

  // 处理登录
  const handleLogin = async (values) => {
    const result = await login(values)
    if (result.success) {
      message.success('登录成功！')
    } else {
      message.error(result.message)
    }
  }

  // 处理注册
  const handleRegister = async (values) => {
    const result = await register(values)
    if (result.success) {
      message.success('注册成功！')
    } else {
      message.error(result.message)
    }
  }

  return (
    <div className="login-container">
      <Card className="login-card">
        <div className="login-header">
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎌</div>
          <h1>日语教学管理平台</h1>
          <p>专业的日语学习与教学管理系统</p>
        </div>
        
        <div className="login-form">
          <Tabs defaultActiveKey="login" centered>
            <TabPane tab="登录" key="login">
              <Form
                form={loginForm}
                name="login"
                onFinish={handleLogin}
                autoComplete="off"
                size="large"
              >


                <Form.Item
                  name="username"
                  rules={[{ required: true, message: '请输入用户名!' }]}
                >
                  <Input 
                    prefix={<UserOutlined />} 
                    placeholder="用户名" 
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[{ required: true, message: '请输入密码!' }]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="密码"
                  />
                </Form.Item>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    block
                  >
                    登录
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>

            <TabPane tab="注册" key="register">
              <Form
                form={registerForm}
                name="register"
                onFinish={handleRegister}
                autoComplete="off"
                size="large"
              >
                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名!' },
                    { min: 3, message: '用户名至少3个字符!' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="用户名"
                  />
                </Form.Item>

                <Form.Item
                  name="nickname"
                  rules={[{ required: false }]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="昵称（可选）"
                  />
                </Form.Item>

                <Form.Item
                  name="phone"
                  rules={[
                    { required: true, message: '请输入手机号!' },
                    { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号!' }
                  ]}
                >
                  <Input
                    prefix={<UserOutlined />}
                    placeholder="手机号"
                  />
                </Form.Item>

                <Form.Item
                  name="registrationCode"
                  rules={[{ required: true, message: '请输入注册验证码!' }]}
                >
                  <Input
                    prefix={<LockOutlined />}
                    placeholder="注册验证码"
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码!' },
                    { min: 6, message: '密码至少6个字符!' }
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="密码"
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认密码!' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve()
                        }
                        return Promise.reject(new Error('两次输入的密码不一致!'))
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    prefix={<LockOutlined />}
                    placeholder="确认密码"
                  />
                </Form.Item>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    block
                  >
                    注册
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>
          </Tabs>

          <div style={{ textAlign: 'center', marginTop: '20px', color: '#666' }}>
            <p>🎌 日语教学管理平台</p>
            <p>教师：teacher / teacher123</p>
            <p>学生：student1-5 / 123456</p>
            <p style={{ fontSize: '12px', marginTop: '10px' }}>
              注册验证码：JAPANESE2024
            </p>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default LoginPage
