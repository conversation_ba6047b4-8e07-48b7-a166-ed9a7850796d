import { useState, useEffect } from 'react'
import { Row, Col, Card, Statistic, List, Avatar, Tag, Progress, Timeline } from 'antd'
import {
  BookOutlined,
  FileTextOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  StarOutlined
} from '@ant-design/icons'
import dayjs from 'dayjs'

function StudentDashboard() {
  const [stats, setStats] = useState({
    enrolledCourses: 3,
    totalEvaluations: 12,
    avgRating: 4.3,
    completedTasks: 8
  })

  const [recentEvaluations] = useState([
    {
      id: 1,
      courseName: '高等数学',
      teacherName: '张教授',
      rating: 5,
      title: '第一次课程评价',
      content: '学习态度认真，课堂参与度高，作业完成质量优秀。',
      createdAt: '2024-01-15',
      hasReply: false
    },
    {
      id: 2,
      courseName: '线性代数',
      teacherName: '李老师',
      rating: 4,
      title: '期中评价',
      content: '基础知识掌握较好，建议加强练习。',
      createdAt: '2024-01-12',
      hasReply: true
    },
    {
      id: 3,
      courseName: '大学物理',
      teacherName: '王教授',
      rating: 4,
      title: '实验课评价',
      content: '实验操作规范，数据记录完整。',
      createdAt: '2024-01-10',
      hasReply: false
    }
  ])

  const [upcomingClasses] = useState([
    {
      id: 1,
      courseName: '高等数学',
      time: '09:00-10:40',
      classroom: '教学楼A101',
      teacher: '张教授',
      type: '理论课'
    },
    {
      id: 2,
      courseName: '线性代数',
      time: '14:00-15:40',
      classroom: '教学楼B203',
      teacher: '李老师',
      type: '习题课'
    }
  ])

  const [learningProgress] = useState([
    {
      course: '高等数学',
      progress: 75,
      status: 'active'
    },
    {
      course: '线性代数',
      progress: 60,
      status: 'normal'
    },
    {
      course: '大学物理',
      progress: 45,
      status: 'normal'
    }
  ])

  const [recentActivities] = useState([
    {
      time: '2024-01-15 10:30',
      content: '收到高等数学课程评价',
      type: 'evaluation'
    },
    {
      time: '2024-01-14 16:20',
      content: '提交了线性代数作业',
      type: 'homework'
    },
    {
      time: '2024-01-13 09:15',
      content: '参加了大学物理实验课',
      type: 'class'
    },
    {
      time: '2024-01-12 14:45',
      content: '回复了期中评价',
      type: 'reply'
    }
  ])

  const getActivityIcon = (type) => {
    switch (type) {
      case 'evaluation':
        return <FileTextOutlined style={{ color: '#1890ff' }} />
      case 'homework':
        return <BookOutlined style={{ color: '#52c41a' }} />
      case 'class':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />
      case 'reply':
        return <MessageOutlined style={{ color: '#722ed1' }} />
      default:
        return <ClockCircleOutlined />
    }
  }

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>学习概览</h2>
        <p>欢迎回来！今天是 {dayjs().format('YYYY年MM月DD日 dddd')}</p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="选修课程"
              value={stats.enrolledCourses}
              prefix={<BookOutlined style={{ color: '#1890ff' }} />}
              suffix="门"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="收到评价"
              value={stats.totalEvaluations}
              prefix={<FileTextOutlined style={{ color: '#52c41a' }} />}
              suffix="条"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="平均评分"
              value={stats.avgRating}
              prefix={<StarOutlined style={{ color: '#faad14' }} />}
              suffix="/ 5.0"
              precision={1}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card className="stat-card">
            <Statistic
              title="完成任务"
              value={stats.completedTasks}
              prefix={<TrophyOutlined style={{ color: '#f5222d' }} />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* 今日课程安排 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <ClockCircleOutlined style={{ marginRight: '8px' }} />
                今日课程安排
              </span>
            }
            className="content-card"
          >
            <List
              dataSource={upcomingClasses}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<BookOutlined />} style={{ backgroundColor: '#52c41a' }} />}
                    title={item.courseName}
                    description={
                      <div>
                        <div>时间：{item.time}</div>
                        <div>地点：{item.classroom}</div>
                        <div>教师：{item.teacher}</div>
                      </div>
                    }
                  />
                  <Tag color="green">{item.type}</Tag>
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 最新评价 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <span>
                <MessageOutlined style={{ marginRight: '8px' }} />
                最新收到的评价
              </span>
            }
            className="content-card"
          >
            <List
              dataSource={recentEvaluations}
              renderItem={(item) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<FileTextOutlined />} />}
                    title={
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>{item.courseName} - {item.title}</span>
                        <div>
                          {'★'.repeat(item.rating)}
                          <span style={{ color: '#faad14', marginLeft: '4px' }}>
                            {item.rating}.0
                          </span>
                        </div>
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: '4px' }}>{item.content}</div>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <small style={{ color: '#999' }}>
                            {item.teacherName} · {item.createdAt}
                          </small>
                          {item.hasReply && <Tag color="blue">已回复</Tag>}
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 学习进度 */}
        <Col xs={24} lg={12}>
          <Card title="学习进度" className="content-card">
            {learningProgress.map((item, index) => (
              <div key={index} style={{ marginBottom: '16px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                  <span>{item.course}</span>
                  <span>{item.progress}%</span>
                </div>
                <Progress 
                  percent={item.progress} 
                  status={item.status}
                  strokeColor={
                    item.status === 'active' ? '#1890ff' : '#52c41a'
                  }
                />
              </div>
            ))}
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card title="最近活动" className="content-card">
            <Timeline
              items={recentActivities.map((activity, index) => ({
                dot: getActivityIcon(activity.type),
                children: (
                  <div>
                    <div>{activity.content}</div>
                    <small style={{ color: '#999' }}>{activity.time}</small>
                  </div>
                )
              }))}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default StudentDashboard
