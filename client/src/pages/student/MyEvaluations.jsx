import { useState, useEffect } from 'react'
import { 
  Card, 
  List, 
  Rate, 
  Tag, 
  Button, 
  Modal, 
  Form, 
  Input, 
  message, 
  Empty,
  Divider,
  Avatar,
  Space
} from 'antd'
import { 
  FileTextOutlined,
  MessageOutlined,
  CalendarOutlined,
  BookOutlined,
  SendOutlined
} from '@ant-design/icons'
import api from '../../utils/api'

const { TextArea } = Input

function MyEvaluations() {
  const [evaluations, setEvaluations] = useState([])
  const [loading, setLoading] = useState(false)
  const [replyModalVisible, setReplyModalVisible] = useState(false)
  const [selectedEvaluation, setSelectedEvaluation] = useState(null)
  const [replyForm] = Form.useForm()
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 获取我的评价列表
  const fetchMyEvaluations = async (page = 1, pageSize = 10) => {
    setLoading(true)
    try {
      const response = await api.get('/evaluations', {
        params: { page, limit: pageSize }
      })
      
      if (response.data.success) {
        setEvaluations(response.data.data)
        setPagination({
          current: page,
          pageSize,
          total: response.data.pagination.total
        })
      }
    } catch (error) {
      message.error('获取评价列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchMyEvaluations()
  }, [])

  // 打开回复模态框
  const openReplyModal = (evaluation) => {
    setSelectedEvaluation(evaluation)
    setReplyModalVisible(true)
    replyForm.resetFields()
  }

  // 关闭回复模态框
  const closeReplyModal = () => {
    setReplyModalVisible(false)
    setSelectedEvaluation(null)
    replyForm.resetFields()
  }

  // 提交回复
  const handleReply = async (values) => {
    try {
      await api.post(`/evaluations/${selectedEvaluation.id}/reply`, values)
      message.success('回复成功')
      closeReplyModal()
      fetchMyEvaluations(pagination.current, pagination.pageSize)
    } catch (error) {
      message.error('回复失败')
    }
  }

  // 处理分页
  const handlePageChange = (page, pageSize) => {
    fetchMyEvaluations(page, pageSize)
  }

  // 渲染评价项
  const renderEvaluationItem = (item) => {
    const hasReplies = item.replies && item.replies.length > 0
    const myReplies = item.replies?.filter(reply => reply.user.role === 'STUDENT') || []
    const teacherReplies = item.replies?.filter(reply => reply.user.role === 'TEACHER') || []

    return (
      <List.Item key={item.id}>
        <Card 
          style={{ width: '100%' }}
          bodyStyle={{ padding: '20px' }}
        >
          {/* 评价头部信息 */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <div>
                <h3 style={{ margin: 0, marginBottom: '8px' }}>
                  <FileTextOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  {item.title}
                </h3>
                <Space size="middle">
                  <span>
                    <BookOutlined style={{ marginRight: '4px' }} />
                    {item.course?.name}
                  </span>
                  <span>
                    <CalendarOutlined style={{ marginRight: '4px' }} />
                    {new Date(item.createdAt).toLocaleDateString()}
                  </span>
                  <Rate disabled value={item.rating} style={{ fontSize: '14px' }} />
                </Space>
              </div>
              <div>
                {hasReplies ? (
                  <Tag color="green">已有回复</Tag>
                ) : (
                  <Tag color="orange">待回复</Tag>
                )}
              </div>
            </div>
          </div>

          {/* 评价内容 */}
          <div style={{ 
            background: '#f8f9fa', 
            padding: '16px', 
            borderRadius: '8px',
            marginBottom: '16px'
          }}>
            <div style={{ 
              color: '#666', 
              fontSize: '12px', 
              marginBottom: '8px' 
            }}>
              来自教师：{item.teacher?.name}
            </div>
            <div style={{ 
              whiteSpace: 'pre-wrap',
              lineHeight: '1.6'
            }}>
              {item.content}
            </div>
          </div>

          {/* 回复区域 */}
          {hasReplies && (
            <div style={{ marginBottom: '16px' }}>
              <Divider orientation="left" style={{ margin: '16px 0 12px 0' }}>
                <span style={{ fontSize: '14px', color: '#666' }}>
                  <MessageOutlined style={{ marginRight: '4px' }} />
                  对话记录
                </span>
              </Divider>
              
              {item.replies.map((reply, index) => (
                <div 
                  key={reply.id} 
                  style={{ 
                    marginBottom: '12px',
                    display: 'flex',
                    justifyContent: reply.user.role === 'STUDENT' ? 'flex-end' : 'flex-start'
                  }}
                >
                  <div style={{
                    maxWidth: '70%',
                    padding: '12px 16px',
                    borderRadius: '12px',
                    background: reply.user.role === 'STUDENT' ? '#1890ff' : '#f0f0f0',
                    color: reply.user.role === 'STUDENT' ? 'white' : '#333'
                  }}>
                    <div style={{ 
                      fontSize: '12px', 
                      opacity: 0.8,
                      marginBottom: '4px'
                    }}>
                      {reply.user.name} · {new Date(reply.createdAt).toLocaleString()}
                    </div>
                    <div>{reply.content}</div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 回复按钮 */}
          <div style={{ textAlign: 'right' }}>
            <Button 
              type="primary" 
              icon={<MessageOutlined />}
              onClick={() => openReplyModal(item)}
            >
              {hasReplies ? '继续对话' : '回复评价'}
            </Button>
          </div>
        </Card>
      </List.Item>
    )
  }

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>我的评价</h2>
        <p>查看教师对您的课程评价，并进行互动交流</p>
      </div>

      {/* 评价列表 */}
      <div>
        {evaluations.length > 0 ? (
          <List
            loading={loading}
            dataSource={evaluations}
            renderItem={renderEvaluationItem}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条评价`,
              onChange: handlePageChange
            }}
          />
        ) : (
          <Card className="content-card">
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂无评价记录"
            />
          </Card>
        )}
      </div>

      {/* 回复模态框 */}
      <Modal
        title={
          <span>
            <MessageOutlined style={{ marginRight: '8px' }} />
            回复评价
          </span>
        }
        open={replyModalVisible}
        onCancel={closeReplyModal}
        footer={null}
        width={600}
      >
        {selectedEvaluation && (
          <div>
            {/* 评价信息 */}
            <div style={{ 
              background: '#f8f9fa', 
              padding: '16px', 
              borderRadius: '8px',
              marginBottom: '20px'
            }}>
              <h4 style={{ margin: '0 0 8px 0' }}>{selectedEvaluation.title}</h4>
              <div style={{ color: '#666', fontSize: '14px', marginBottom: '12px' }}>
                {selectedEvaluation.course?.name} · {selectedEvaluation.teacher?.name}
              </div>
              <div style={{ lineHeight: '1.6' }}>
                {selectedEvaluation.content}
              </div>
            </div>

            {/* 回复表单 */}
            <Form
              form={replyForm}
              onFinish={handleReply}
              layout="vertical"
            >
              <Form.Item
                name="content"
                label="您的回复"
                rules={[{ required: true, message: '请输入回复内容' }]}
              >
                <TextArea 
                  rows={4} 
                  placeholder="请输入您的回复内容..."
                  showCount
                  maxLength={500}
                />
              </Form.Item>
              
              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Space>
                  <Button onClick={closeReplyModal}>
                    取消
                  </Button>
                  <Button 
                    type="primary" 
                    htmlType="submit"
                    icon={<SendOutlined />}
                  >
                    发送回复
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default MyEvaluations
