import { useState, useEffect } from 'react'
import { 
  Row, 
  Col, 
  Card, 
  Statistic, 
  List, 
  Avatar, 
  Tag, 
  Progress, 
  Button, 
  Space, 
  Typography,
  Divider,
  Empty,
  Timeline
} from 'antd'
import {
  BookOutlined,
  ClockCircleOutlined,
  MessageOutlined,
  StarOutlined,
  TrophyOutlined,
  CalendarOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import api from '../../utils/api'
import MarkdownRenderer from '../../components/MarkdownRenderer'

const { Title, Text } = Typography

function StudentDashboard() {
  const navigate = useNavigate()
  const [stats, setStats] = useState({
    totalCourses: 0,
    completedSessions: 0,
    totalEvaluations: 0,
    avgRating: 0
  })
  const [myCourses, setMyCourses] = useState([])
  const [upcomingSessions, setUpcomingSessions] = useState([])
  const [recentEvaluations, setRecentEvaluations] = useState([])
  const [loading, setLoading] = useState(true)

  // 日语课程类型映射
  const courseTypeMap = {
    'FREE_CONVERSATION': '自由会话',
    'SPEAKING': '口语课程',
    'BUSINESS': '商务日语',
    'WRITING': '写作课程',
    'GRAMMAR': '语法课程',
    'JLPT': 'JLPT对策',
    'GRADUATE_INTERVIEW': '研究生面试',
    'JOB_INTERVIEW': '就职面试',
    'SPEECH_GUIDANCE': '演讲指导',
    'GAOKAO_JAPANESE': '高考日语',
    'OTHER': '其他课程'
  }

  // 获取学生仪表盘数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      const [coursesRes, sessionsRes, evaluationsRes] = await Promise.all([
        api.get('/courses'),
        api.get('/sessions'),
        api.get('/evaluations')
      ])

      if (coursesRes.data.success) {
        setMyCourses(coursesRes.data.data)
        setStats(prev => ({ ...prev, totalCourses: coursesRes.data.data.length }))
      }

      if (sessionsRes.data.success) {
        const sessions = sessionsRes.data.data
        const upcoming = sessions
          .filter(s => dayjs(s.sessionDate).isAfter(dayjs()))
          .sort((a, b) => dayjs(a.sessionDate) - dayjs(b.sessionDate))
          .slice(0, 3)
        setUpcomingSessions(upcoming)
        
        const completed = sessions.filter(s => s.status === 'COMPLETED')
        setStats(prev => ({ ...prev, completedSessions: completed.length }))
      }

      if (evaluationsRes.data.success) {
        const evaluations = evaluationsRes.data.data
        setRecentEvaluations(evaluations.slice(0, 3))
        setStats(prev => ({ 
          ...prev, 
          totalEvaluations: evaluations.length,
          avgRating: evaluations.length > 0 
            ? (evaluations.reduce((sum, e) => sum + e.rating, 0) / evaluations.length).toFixed(1)
            : 0
        }))
      }
    } catch (error) {
      console.error('获取学生仪表盘数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchDashboardData()
  }, [])

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 欢迎标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, color: '#ff6b6b' }}>
          🎌 我的日语学习中心
        </Title>
        <Text type="secondary">
          がんばって！今天是 {dayjs().format('YYYY年MM月DD日')}
        </Text>
      </div>

      {/* 学习统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="选修课程"
              value={stats.totalCourses}
              prefix={<BookOutlined style={{ color: '#ff6b6b' }} />}
              suffix="门"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="完成课时"
              value={stats.completedSessions}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              suffix="节"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="收到评价"
              value={stats.totalEvaluations}
              prefix={<MessageOutlined style={{ color: '#1890ff' }} />}
              suffix="条"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="平均评分"
              value={stats.avgRating}
              prefix={<StarOutlined style={{ color: '#faad14' }} />}
              suffix="/ 5.0"
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card style={{ marginBottom: '24px' }}>
        <Title level={4}>🚀 快速操作</Title>
        <Space wrap>
          <Button 
            type="primary" 
            icon={<EyeOutlined />}
            onClick={() => navigate('/evaluations')}
          >
            查看评价
          </Button>
          <Button 
            icon={<BookOutlined />}
            onClick={() => navigate('/courses')}
          >
            我的课程
          </Button>
          <Button 
            icon={<CalendarOutlined />}
            onClick={() => navigate('/sessions')}
          >
            课程安排
          </Button>
        </Space>
      </Card>

      <Row gutter={[16, 16]}>
        {/* 我的课程 */}
        <Col xs={24} lg={8}>
          <Card 
            title="📚 我的课程" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/courses')}
              >
                查看全部
              </Button>
            }
          >
            {myCourses.length > 0 ? (
              <List
                dataSource={myCourses.slice(0, 4)}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar style={{ backgroundColor: '#ff6b6b' }}>
                        {courseTypeMap[item.type]?.charAt(0) || '日'}
                      </Avatar>}
                      title={item.name}
                      description={
                        <Space direction="vertical" size={4}>
                          <Tag color="blue">{courseTypeMap[item.type]}</Tag>
                          <Text type="secondary">
                            {item.price === 0 ? '免费' : `¥${(item.price / 100).toFixed(0)}`} / {item.duration}分钟
                          </Text>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无选修课程" />
            )}
          </Card>
        </Col>

        {/* 即将上课 */}
        <Col xs={24} lg={8}>
          <Card 
            title="⏰ 即将上课" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/sessions')}
              >
                查看全部
              </Button>
            }
          >
            {upcomingSessions.length > 0 ? (
              <Timeline>
                {upcomingSessions.map(session => (
                  <Timeline.Item 
                    key={session.id}
                    dot={<PlayCircleOutlined style={{ color: '#ff6b6b' }} />}
                  >
                    <div>
                      <Text strong>{session.title}</Text>
                      <br />
                      <Text type="secondary">
                        {dayjs(session.sessionDate).format('MM-DD HH:mm')}
                      </Text>
                      <br />
                      <Text type="secondary">
                        时长：{session.duration}分钟
                      </Text>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <Empty description="暂无即将上课的安排" />
            )}
          </Card>
        </Col>

        {/* 最新评价 */}
        <Col xs={24} lg={8}>
          <Card 
            title="💬 最新评价" 
            extra={
              <Button 
                type="link" 
                onClick={() => navigate('/evaluations')}
              >
                查看全部
              </Button>
            }
          >
            {recentEvaluations.length > 0 ? (
              <List
                dataSource={recentEvaluations}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={<MessageOutlined />} />}
                      title={
                        <Space>
                          <Text>{item.title}</Text>
                          <div>
                            {[...Array(5)].map((_, i) => (
                              <StarOutlined 
                                key={i} 
                                style={{ 
                                  color: i < item.rating ? '#faad14' : '#d9d9d9',
                                  fontSize: '12px'
                                }} 
                              />
                            ))}
                          </div>
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {dayjs(item.createdAt).format('MM-DD')}
                          </Text>
                          <div style={{ 
                            marginTop: '4px',
                            maxHeight: '60px',
                            overflow: 'hidden'
                          }}>
                            <MarkdownRenderer content={item.content} />
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无评价记录" />
            )}
          </Card>
        </Col>
      </Row>

      {/* 学习进度 */}
      <Card style={{ marginTop: '24px' }} title="📈 学习进度">
        <Row gutter={[16, 16]}>
          <Col xs={24} md={12}>
            <div style={{ marginBottom: '16px' }}>
              <Text>课程完成度</Text>
              <Progress 
                percent={stats.totalCourses > 0 ? Math.round((stats.completedSessions / (stats.totalCourses * 10)) * 100) : 0}
                strokeColor="#ff6b6b"
              />
            </div>
          </Col>
          <Col xs={24} md={12}>
            <div style={{ marginBottom: '16px' }}>
              <Text>评价互动率</Text>
              <Progress 
                percent={stats.totalEvaluations > 0 ? Math.min(100, stats.totalEvaluations * 20) : 0}
                strokeColor="#52c41a"
              />
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  )
}

export default StudentDashboard
