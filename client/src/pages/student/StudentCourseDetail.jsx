import { useState, useEffect } from 'react'
import { 
  Card, 
  Row, 
  Col, 
  Descriptions, 
  Timeline, 
  Badge,
  List,
  Avatar,
  Tag,
  Button,
  Space,
  Typography,
  Divider,
  Statistic,
  Progress,
  Calendar,
  Empty,
  Rate
} from 'antd'
import { 
  ArrowLeftOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  StarOutlined,
  BookOutlined,
  CheckCircleOutlined,
  PlayCircleOutlined,
  FileTextOutlined,
  MessageOutlined
} from '@ant-design/icons'
import { useParams, useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import api from '../../utils/api'
import MarkdownRenderer from '../../components/MarkdownRenderer'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const { Title, Text, Paragraph } = Typography

function StudentCourseDetail() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [course, setCourse] = useState(null)
  const [sessions, setSessions] = useState([])
  const [evaluations, setEvaluations] = useState([])
  const [loading, setLoading] = useState(true)

  // 日语课程类型映射
  const courseTypeMap = {
    'FREE_CONVERSATION': '自由会话',
    'SPEAKING': '口语课程',
    'BUSINESS': '商务日语',
    'WRITING': '写作课程',
    'GRAMMAR': '语法课程',
    'JLPT': 'JLPT N5-1 对策',
    'GRADUATE_INTERVIEW': '研究生面试对策',
    'JOB_INTERVIEW': '求职就职面试',
    'SPEECH_GUIDANCE': '演讲指导',
    'GAOKAO_JAPANESE': '高考日语',
    'OTHER': '其他课程'
  }

  // 获取课程详情
  const fetchCourseDetail = async () => {
    try {
      setLoading(true)

      // 分批获取数据，避免同时发起太多请求
      // 首先获取课程基本信息
      const courseRes = await api.get(`/courses/${id}`)
      if (courseRes.data.success) {
        setCourse(courseRes.data.data)
      }

      // 延迟获取课程安排
      await new Promise(resolve => setTimeout(resolve, 200))
      const sessionsRes = await api.get(`/sessions?courseId=${id}`)
      if (sessionsRes.data.success) {
        setSessions(sessionsRes.data.data)
      }

      // 延迟获取评价信息
      await new Promise(resolve => setTimeout(resolve, 200))
      const evaluationsRes = await api.get(`/evaluations?courseId=${id}`)
      if (evaluationsRes.data.success) {
        setEvaluations(evaluationsRes.data.data)
      }
    } catch (error) {
      console.error('获取课程详情失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (id) {
      fetchCourseDetail()
    }
  }, [id])

  // 计算学习进度
  const getStudyProgress = () => {
    const completedSessions = sessions.filter(s => s.status === 'COMPLETED').length
    const totalSessions = sessions.length
    return totalSessions > 0 ? Math.round((completedSessions / totalSessions) * 100) : 0
  }

  // 获取下次课程
  const getNextSession = () => {
    return sessions
      .filter(s => s.status === 'SCHEDULED' && dayjs(s.sessionDate).isAfter(dayjs()))
      .sort((a, b) => dayjs(a.sessionDate) - dayjs(b.sessionDate))[0]
  }

  // 日历数据处理
  const getCalendarData = (value) => {
    const dateStr = value.format('YYYY-MM-DD')
    const daySessions = sessions.filter(session => 
      dayjs(session.sessionDate).format('YYYY-MM-DD') === dateStr
    )
    
    return daySessions.map(session => ({
      type: session.status === 'COMPLETED' ? 'success' : 
            session.status === 'CANCELLED' ? 'error' : 'processing',
      content: `${dayjs(session.sessionDate).format('HH:mm')} - ${session.title}`
    }))
  }

  // 日历单元格渲染
  const dateCellRender = (value) => {
    const listData = getCalendarData(value)
    return (
      <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
        {listData.map((item, index) => (
          <li key={index}>
            <Badge 
              status={item.type} 
              text={
                <span style={{ fontSize: '10px' }}>
                  {item.content}
                </span>
              } 
            />
          </li>
        ))}
      </ul>
    )
  }

  if (loading || !course) {
    return <div>加载中...</div>
  }

  const nextSession = getNextSession()
  const studyProgress = getStudyProgress()
  const completedSessions = sessions.filter(s => s.status === 'COMPLETED').length
  const avgRating = evaluations.length > 0 
    ? (evaluations.reduce((sum, e) => sum + e.rating, 0) / evaluations.length).toFixed(1)
    : 0

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 返回按钮 */}
      <Button 
        icon={<ArrowLeftOutlined />} 
        onClick={() => navigate('/courses')}
        style={{ marginBottom: '16px' }}
      >
        返回我的课程
      </Button>

      {/* 课程基本信息 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Title level={2} style={{ margin: 0, color: '#ff6b6b' }}>
              🎌 {course.name}
            </Title>
            <Text type="secondary" style={{ fontSize: '16px' }}>
              {courseTypeMap[course.type]}
            </Text>
            
            <Divider />
            
            <Descriptions column={2}>
              <Descriptions.Item label="课程类型">
                <Tag color="blue">{courseTypeMap[course.type]}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="课程价格">
                <Text style={{ color: course.price === 0 ? '#52c41a' : '#ff6b6b', fontWeight: 'bold' }}>
                  {course.price === 0 ? '免费体验' : `¥${(course.price / 100).toFixed(0)}`}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="课程时长">
                {course.duration}分钟/节
              </Descriptions.Item>
              <Descriptions.Item label="任课教师">
                <Space>
                  <Avatar icon={<UserOutlined />} size="small" />
                  {course.teacher?.nickname || course.teacher?.username}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="开课时间">
                {dayjs(course.createdAt).format('YYYY年MM月DD日')}
              </Descriptions.Item>
              <Descriptions.Item label="课程状态">
                <Tag color={course.isActive ? 'green' : 'red'}>
                  {course.isActive ? '进行中' : '已结束'}
                </Tag>
              </Descriptions.Item>
            </Descriptions>

            {course.description && (
              <>
                <Divider />
                <div>
                  <Title level={4}>📖 课程介绍</Title>
                  <Paragraph>{course.description}</Paragraph>
                </div>
              </>
            )}
          </Col>
          
          <Col xs={24} lg={8}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Statistic
                  title="学习进度"
                  value={studyProgress}
                  prefix={<BookOutlined />}
                  suffix="%"
                />
                <Progress 
                  percent={studyProgress} 
                  strokeColor="#ff6b6b"
                  size="small"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="已完成课时"
                  value={completedSessions}
                  prefix={<CheckCircleOutlined />}
                  suffix="节"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="总课时安排"
                  value={sessions.length}
                  prefix={<CalendarOutlined />}
                  suffix="节"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="平均评分"
                  value={avgRating}
                  prefix={<StarOutlined />}
                  suffix="/ 5.0"
                />
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>

      {/* 下次课程提醒 */}
      {nextSession && (
        <Card 
          style={{ marginBottom: '24px', border: '2px solid #ff6b6b' }}
          title={
            <Space>
              <PlayCircleOutlined style={{ color: '#ff6b6b' }} />
              <span style={{ color: '#ff6b6b' }}>下次课程</span>
            </Space>
          }
        >
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} md={12}>
              <Space direction="vertical" size={4}>
                <Text strong style={{ fontSize: '16px' }}>{nextSession.title}</Text>
                <Text type="secondary">
                  <CalendarOutlined /> {dayjs(nextSession.sessionDate).format('YYYY年MM月DD日 HH:mm')}
                </Text>
                <Text type="secondary">
                  <ClockCircleOutlined /> 时长：{nextSession.duration}分钟
                </Text>
                {nextSession.location && (
                  <Text type="secondary">
                    📍 地点：{nextSession.location}
                  </Text>
                )}
              </Space>
            </Col>
            <Col xs={24} md={12}>
              <div style={{ textAlign: 'right' }}>
                <Text type="secondary">距离上课还有</Text>
                <br />
                <Text strong style={{ fontSize: '20px', color: '#ff6b6b' }}>
                  {dayjs(nextSession.sessionDate).fromNow()}
                </Text>
              </div>
            </Col>
          </Row>
        </Card>
      )}

      <Row gutter={[16, 16]}>
        {/* 课程安排时间线 */}
        <Col xs={24} lg={12}>
          <Card title="📅 课程安排" style={{ height: '500px', overflow: 'auto' }}>
            {sessions.length > 0 ? (
              <Timeline>
                {sessions
                  .sort((a, b) => dayjs(a.sessionDate) - dayjs(b.sessionDate))
                  .map(session => (
                    <Timeline.Item 
                      key={session.id}
                      dot={
                        session.status === 'COMPLETED' ? 
                          <CheckCircleOutlined style={{ color: '#52c41a' }} /> :
                        session.status === 'CANCELLED' ?
                          <ClockCircleOutlined style={{ color: '#ff4d4f' }} /> :
                          <PlayCircleOutlined style={{ color: '#1890ff' }} />
                      }
                      color={
                        session.status === 'COMPLETED' ? 'green' :
                        session.status === 'CANCELLED' ? 'red' : 'blue'
                      }
                    >
                      <div>
                        <Text strong>{session.title}</Text>
                        <br />
                        <Text type="secondary">
                          {dayjs(session.sessionDate).format('MM-DD HH:mm')} 
                          ({session.duration}分钟)
                        </Text>
                        <br />
                        <Tag color={
                          session.status === 'COMPLETED' ? 'green' :
                          session.status === 'CANCELLED' ? 'red' : 'blue'
                        }>
                          {session.status === 'COMPLETED' ? '已完成' :
                           session.status === 'CANCELLED' ? '已取消' : '已安排'}
                        </Tag>
                        {session.description && (
                          <>
                            <br />
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {session.description}
                            </Text>
                          </>
                        )}
                      </div>
                    </Timeline.Item>
                  ))}
              </Timeline>
            ) : (
              <Empty description="暂无课程安排" />
            )}
          </Card>
        </Col>

        {/* 我的评价 */}
        <Col xs={24} lg={12}>
          <Card title="💬 我的评价" style={{ height: '500px', overflow: 'auto' }}>
            {evaluations.length > 0 ? (
              <List
                dataSource={evaluations}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={<MessageOutlined />} />}
                      title={
                        <Space>
                          <Text>{item.title}</Text>
                          <Rate disabled defaultValue={item.rating} style={{ fontSize: '14px' }} />
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {dayjs(item.createdAt).format('YYYY-MM-DD')}
                          </Text>
                          <div style={{ marginTop: '8px' }}>
                            <MarkdownRenderer content={item.content} />
                          </div>
                          {item.replies && item.replies.length > 0 && (
                            <div style={{ 
                              marginTop: '12px', 
                              padding: '8px', 
                              background: '#f5f5f5', 
                              borderRadius: '4px' 
                            }}>
                              <Text strong style={{ fontSize: '12px' }}>我的回复：</Text>
                              <div style={{ marginTop: '4px' }}>
                                <MarkdownRenderer content={item.replies[item.replies.length - 1]?.content} />
                              </div>
                            </div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无评价记录" />
            )}
          </Card>
        </Col>
      </Row>

      {/* 课程日历 */}
      <Card style={{ marginTop: '24px' }} title="📅 课程日历">
        <Calendar 
          dateCellRender={dateCellRender}
          style={{ background: '#fff' }}
        />
      </Card>
    </div>
  )
}

export default StudentCourseDetail
