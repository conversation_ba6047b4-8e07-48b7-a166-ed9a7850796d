import { useState, useEffect } from 'react'
import { 
  Card, 
  List, 
  Button, 
  Tag, 
  Avatar, 
  Descriptions, 
  Timeline,
  Alert,
  Space,
  message,
  Modal,
  Empty
} from 'antd'
import {
  BookOutlined,
  UserOutlined,
  CalendarOutlined,
  FileOutlined,
  DownloadOutlined,
  NotificationOutlined,
  TrophyOutlined,
  EyeOutlined
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import api from '../../utils/api'

function CourseInfo() {
  const navigate = useNavigate()
  const [courses, setCourses] = useState([])
  const [selectedCourse, setSelectedCourse] = useState(null)
  const [courseDetails, setCourseDetails] = useState(null)
  const [loading, setLoading] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)

  // 获取我的课程列表
  const fetchMyCourses = async () => {
    setLoading(true)
    try {
      const response = await api.get('/courses')
      if (response.data.success) {
        setCourses(response.data.data)
      }
    } catch (error) {
      message.error('获取课程列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取课程详情
  const fetchCourseDetails = async (courseId) => {
    try {
      const response = await api.get(`/courses/${courseId}`)
      if (response.data.success) {
        setCourseDetails(response.data.data)
        setDetailModalVisible(true)
      }
    } catch (error) {
      message.error('获取课程详情失败')
    }
  }

  useEffect(() => {
    fetchMyCourses()
  }, [])

  // 查看课程详情
  const viewCourseDetail = (course) => {
    setSelectedCourse(course)
    fetchCourseDetails(course.id)
  }

  // 下载课程材料
  const downloadMaterial = async (materialId, fileName) => {
    try {
      const response = await api.get(`/upload/download/${materialId}`, {
        responseType: 'blob'
      })
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)
      
      message.success('下载成功')
    } catch (error) {
      message.error('下载失败')
    }
  }

  // 渲染课程卡片
  const renderCourseCard = (course) => (
    <Card
      key={course.id}
      style={{ marginBottom: '16px' }}
      actions={[
        <Button
          type="primary"
          icon={<EyeOutlined />}
          onClick={() => navigate(`/courses/${course.id}`)}
        >
          详细信息
        </Button>,
        <Button
          type="link"
          onClick={() => viewCourseDetail(course)}
        >
          快速预览
        </Button>
      ]}
    >
      <Card.Meta
        avatar={
          <Avatar 
            size={64} 
            icon={<BookOutlined />} 
            style={{ backgroundColor: '#1890ff' }}
          />
        }
        title={
          <div>
            <h3 style={{ margin: 0 }}>{course.name}</h3>
            <Tag color="blue">{course.code}</Tag>
          </div>
        }
        description={
          <div>
            <p style={{ margin: '8px 0', color: '#666' }}>
              {course.description || '暂无课程描述'}
            </p>
            <Space size="middle">
              <span>
                <UserOutlined style={{ marginRight: '4px' }} />
                {course.teacher?.nickname || course.teacher?.username}
              </span>
              <span>
                <TrophyOutlined style={{ marginRight: '4px' }} />
                ¥{(course.price / 100).toFixed(0)} / {course.duration}分钟
              </span>
              <span>
                <CalendarOutlined style={{ marginRight: '4px' }} />
                {course.type}
              </span>
            </Space>
          </div>
        }
      />
    </Card>
  )

  return (
    <div>
      {/* 页面标题 */}
      <div className="page-header">
        <h2>🎌 我的日语课程</h2>
        <p>查看您选择的日语课程详情、学习安排和教学材料</p>
      </div>

      {/* 课程列表 */}
      <div>
        {courses.length > 0 ? (
          courses.map(renderCourseCard)
        ) : (
          <Card className="content-card">
            <Empty 
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="暂未选修任何课程"
            />
          </Card>
        )}
      </div>

      {/* 课程详情模态框 */}
      <Modal
        title={selectedCourse?.name}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {courseDetails && (
          <div>
            {/* 课程基本信息 */}
            <Card title="课程信息" style={{ marginBottom: '16px' }}>
              <Descriptions column={2}>
                <Descriptions.Item label="课程代码">
                  {courseDetails.code}
                </Descriptions.Item>
                <Descriptions.Item label="学分">
                  {courseDetails.credits}
                </Descriptions.Item>
                <Descriptions.Item label="任课教师">
                  {courseDetails.teacher?.nickname || courseDetails.teacher?.username}
                </Descriptions.Item>
                <Descriptions.Item label="学期">
                  {courseDetails.year} {courseDetails.semester}
                </Descriptions.Item>
                <Descriptions.Item label="选课人数">
                  {courseDetails._count?.enrollments} 人
                </Descriptions.Item>
                <Descriptions.Item label="评价数量">
                  {courseDetails._count?.evaluations} 条
                </Descriptions.Item>
              </Descriptions>
              {courseDetails.description && (
                <div style={{ marginTop: '16px' }}>
                  <h4>课程描述：</h4>
                  <p>{courseDetails.description}</p>
                </div>
              )}
            </Card>

            {/* 课程公告 */}
            {courseDetails.announcements && courseDetails.announcements.length > 0 && (
              <Card 
                title={
                  <span>
                    <NotificationOutlined style={{ marginRight: '8px' }} />
                    课程公告
                  </span>
                } 
                style={{ marginBottom: '16px' }}
              >
                {courseDetails.announcements.map((announcement) => (
                  <Alert
                    key={announcement.id}
                    message={announcement.title}
                    description={announcement.content}
                    type={
                      announcement.priority === 'HIGH' ? 'warning' :
                      announcement.priority === 'URGENT' ? 'error' : 'info'
                    }
                    showIcon
                    style={{ marginBottom: '12px' }}
                  />
                ))}
              </Card>
            )}

            {/* 课程材料 */}
            {courseDetails.materials && courseDetails.materials.length > 0 && (
              <Card 
                title={
                  <span>
                    <FileOutlined style={{ marginRight: '8px' }} />
                    课程材料
                  </span>
                } 
                style={{ marginBottom: '16px' }}
              >
                <List
                  dataSource={courseDetails.materials}
                  renderItem={(material) => (
                    <List.Item
                      actions={[
                        <Button
                          type="link"
                          icon={<DownloadOutlined />}
                          onClick={() => downloadMaterial(material.id, material.fileName)}
                        >
                          下载
                        </Button>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<Avatar icon={<FileOutlined />} />}
                        title={material.title}
                        description={
                          <div>
                            <div>文件名：{material.fileName}</div>
                            <div style={{ color: '#999', fontSize: '12px' }}>
                              大小：{(material.fileSize / 1024 / 1024).toFixed(2)} MB · 
                              上传时间：{new Date(material.createdAt).toLocaleString()}
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            )}

            {/* 课程安排 */}
            {courseDetails.sessions && courseDetails.sessions.length > 0 && (
              <Card 
                title={
                  <span>
                    <CalendarOutlined style={{ marginRight: '8px' }} />
                    课程安排
                  </span>
                }
              >
                <Timeline
                  items={courseDetails.sessions.map((session) => ({
                    children: (
                      <div>
                        <h4>{session.title}</h4>
                        <p>{session.description}</p>
                        <div style={{ color: '#999', fontSize: '12px' }}>
                          时间：{new Date(session.sessionDate).toLocaleString()}
                          {session.location && ` · 地点：${session.location}`}
                          {session.duration && ` · 时长：${session.duration}分钟`}
                        </div>
                      </div>
                    )
                  }))}
                />
              </Card>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default CourseInfo
