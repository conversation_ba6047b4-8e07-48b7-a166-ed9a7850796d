import { useState, useRef } from 'react'
import { Upload, Button, message } from 'antd'
import { 
  PictureOutlined, 
  FileOutlined, 
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  OrderedListOutlined,
  UnorderedListOutlined
} from '@ant-design/icons'
import api from '../utils/api'

function RichTextEditor({ value, onChange, placeholder = "请输入内容..." }) {
  const [uploading, setUploading] = useState(false)
  const editorRef = useRef(null)

  // 插入文本到光标位置
  const insertAtCursor = (text) => {
    const editor = editorRef.current
    if (!editor) return

    const start = editor.selectionStart
    const end = editor.selectionEnd
    const currentValue = value || ''
    
    const newValue = currentValue.substring(0, start) + text + currentValue.substring(end)
    onChange(newValue)
    
    // 设置光标位置
    setTimeout(() => {
      editor.selectionStart = editor.selectionEnd = start + text.length
      editor.focus()
    }, 0)
  }

  // 包装选中文本
  const wrapSelectedText = (prefix, suffix = '') => {
    const editor = editorRef.current
    if (!editor) return

    const start = editor.selectionStart
    const end = editor.selectionEnd
    const currentValue = value || ''
    const selectedText = currentValue.substring(start, end)
    
    if (selectedText) {
      const wrappedText = prefix + selectedText + (suffix || prefix)
      const newValue = currentValue.substring(0, start) + wrappedText + currentValue.substring(end)
      onChange(newValue)
      
      setTimeout(() => {
        editor.selectionStart = start + prefix.length
        editor.selectionEnd = start + prefix.length + selectedText.length
        editor.focus()
      }, 0)
    } else {
      insertAtCursor(prefix + (suffix || prefix))
    }
  }

  // 格式化按钮
  const formatButtons = [
    {
      icon: <BoldOutlined />,
      title: '粗体',
      action: () => wrapSelectedText('**')
    },
    {
      icon: <ItalicOutlined />,
      title: '斜体',
      action: () => wrapSelectedText('*')
    },
    {
      icon: <UnderlineOutlined />,
      title: '下划线',
      action: () => wrapSelectedText('<u>', '</u>')
    },
    {
      icon: <OrderedListOutlined />,
      title: '有序列表',
      action: () => insertAtCursor('\n1. ')
    },
    {
      icon: <UnorderedListOutlined />,
      title: '无序列表',
      action: () => insertAtCursor('\n- ')
    }
  ]

  // 上传图片
  const handleImageUpload = async (file) => {
    setUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'image')

      const response = await api.post('/upload/material', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      if (response.data.success) {
        const imageUrl = response.data.data.url
        insertAtCursor(`\n![图片](${imageUrl})\n`)
        message.success('图片上传成功')
      }
    } catch (error) {
      message.error('图片上传失败')
    } finally {
      setUploading(false)
    }
    return false // 阻止默认上传行为
  }

  // 上传文件
  const handleFileUpload = async (file) => {
    setUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'document')

      const response = await api.post('/upload/material', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      if (response.data.success) {
        const fileUrl = response.data.data.url
        const fileName = file.name
        insertAtCursor(`\n[📎 ${fileName}](${fileUrl})\n`)
        message.success('文件上传成功')
      }
    } catch (error) {
      message.error('文件上传失败')
    } finally {
      setUploading(false)
    }
    return false // 阻止默认上传行为
  }

  return (
    <div className="rich-text-editor" style={{ border: '1px solid #d9d9d9', borderRadius: '6px' }}>
      {/* 工具栏 */}
      <div style={{ 
        padding: '8px 12px', 
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        gap: '8px',
        alignItems: 'center',
        flexWrap: 'wrap'
      }}>
        {/* 格式化按钮 */}
        {formatButtons.map((btn, index) => (
          <Button
            key={index}
            type="text"
            size="small"
            icon={btn.icon}
            title={btn.title}
            onClick={btn.action}
            style={{ minWidth: '32px' }}
          />
        ))}
        
        <div style={{ width: '1px', height: '20px', backgroundColor: '#f0f0f0', margin: '0 4px' }} />
        
        {/* 图片上传 */}
        <Upload
          accept="image/*"
          showUploadList={false}
          beforeUpload={handleImageUpload}
          disabled={uploading}
        >
          <Button
            type="text"
            size="small"
            icon={<PictureOutlined />}
            title="插入图片"
            loading={uploading}
            style={{ minWidth: '32px' }}
          />
        </Upload>

        {/* 文件上传 */}
        <Upload
          accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.zip,.rar"
          showUploadList={false}
          beforeUpload={handleFileUpload}
          disabled={uploading}
        >
          <Button
            type="text"
            size="small"
            icon={<FileOutlined />}
            title="插入文件"
            loading={uploading}
            style={{ minWidth: '32px' }}
          />
        </Upload>
      </div>

      {/* 编辑区域 */}
      <textarea
        ref={editorRef}
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        style={{
          width: '100%',
          minHeight: '200px',
          padding: '12px',
          border: 'none',
          outline: 'none',
          resize: 'vertical',
          fontFamily: 'inherit',
          fontSize: '14px',
          lineHeight: '1.5'
        }}
      />

      {/* 帮助提示 */}
      <div style={{ 
        padding: '8px 12px', 
        borderTop: '1px solid #f0f0f0',
        fontSize: '12px',
        color: '#999',
        backgroundColor: '#fafafa'
      }}>
        💡 支持Markdown格式：**粗体** *斜体* 以及图片、文件上传
      </div>
    </div>
  )
}

export default RichTextEditor
