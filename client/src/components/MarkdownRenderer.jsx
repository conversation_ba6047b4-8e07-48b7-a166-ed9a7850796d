import { useState } from 'react'
import { But<PERSON>, Image } from 'antd'
import { DownloadOutlined, FileOutlined } from '@ant-design/icons'

function MarkdownRenderer({ content }) {
  const [imageError, setImageError] = useState({})

  if (!content) return null

  // 简单的Markdown解析器
  const parseMarkdown = (text) => {
    let html = text
    
    // 处理图片 ![alt](url)
    html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, (match, alt, url) => {
      return `<img src="${url}" alt="${alt}" class="markdown-image" />`
    })
    
    // 处理链接 [text](url)
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
      // 如果是文件链接（以📎开头）
      if (text.startsWith('📎')) {
        const fileName = text.replace('📎 ', '')
        return `<a href="${url}" class="file-link" download="${fileName}">
          <span class="file-icon">📎</span> ${fileName}
        </a>`
      }
      return `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`
    })
    
    // 处理粗体 **text**
    html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
    
    // 处理斜体 *text*
    html = html.replace(/\*([^*]+)\*/g, '<em>$1</em>')
    
    // 处理下划线 <u>text</u>
    html = html.replace(/<u>([^<]+)<\/u>/g, '<u>$1</u>')
    
    // 处理有序列表
    html = html.replace(/^\d+\.\s+(.+)$/gm, '<li class="ordered-list-item">$1</li>')
    
    // 处理无序列表
    html = html.replace(/^-\s+(.+)$/gm, '<li class="unordered-list-item">$1</li>')
    
    // 处理换行
    html = html.replace(/\n/g, '<br>')
    
    return html
  }

  // 渲染组件
  const renderContent = () => {
    const html = parseMarkdown(content)
    
    return (
      <div 
        className="markdown-content"
        dangerouslySetInnerHTML={{ __html: html }}
        style={{
          lineHeight: '1.6',
          wordBreak: 'break-word'
        }}
      />
    )
  }

  return (
    <div className="markdown-renderer">
      {renderContent()}
      
      <style jsx>{`
        .markdown-content {
          font-size: 14px;
          color: #333;
        }
        
        .markdown-content strong {
          font-weight: 600;
          color: #262626;
        }
        
        .markdown-content em {
          font-style: italic;
          color: #595959;
        }
        
        .markdown-content u {
          text-decoration: underline;
        }
        
        .markdown-content .markdown-image {
          max-width: 100%;
          height: auto;
          border-radius: 6px;
          margin: 8px 0;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .markdown-content .file-link {
          display: inline-flex;
          align-items: center;
          padding: 8px 12px;
          background: #f6f8fa;
          border: 1px solid #e1e4e8;
          border-radius: 6px;
          text-decoration: none;
          color: #0366d6;
          margin: 4px 0;
          transition: all 0.2s;
        }
        
        .markdown-content .file-link:hover {
          background: #e1f5fe;
          border-color: #1890ff;
          color: #1890ff;
        }
        
        .markdown-content .file-icon {
          margin-right: 6px;
          font-size: 16px;
        }
        
        .markdown-content .ordered-list-item,
        .markdown-content .unordered-list-item {
          margin: 4px 0;
          padding-left: 8px;
        }
        
        .markdown-content .ordered-list-item {
          list-style-type: decimal;
          margin-left: 20px;
        }
        
        .markdown-content .unordered-list-item {
          list-style-type: disc;
          margin-left: 20px;
        }
        
        .markdown-content a {
          color: #1890ff;
          text-decoration: none;
        }
        
        .markdown-content a:hover {
          text-decoration: underline;
        }
      `}</style>
    </div>
  )
}

export default MarkdownRenderer
