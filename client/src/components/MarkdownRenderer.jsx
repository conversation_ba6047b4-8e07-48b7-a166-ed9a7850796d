import { Typography } from 'antd'

const { Paragraph } = Typography

function MarkdownRenderer({ content }) {
  if (!content) return null

  // 简单的 Markdown 渲染，可以根据需要扩展
  const renderContent = (text) => {
    // 处理换行
    const lines = text.split('\n')
    return lines.map((line, index) => (
      <div key={index}>
        {line || <br />}
      </div>
    ))
  }

  return (
    <div style={{ whiteSpace: 'pre-wrap' }}>
      {renderContent(content)}
    </div>
  )
}

export default MarkdownRenderer
