import { useState } from 'react'
import { Layout as AntLayout, Menu, Button, Avatar, Dropdown, Space, Typography } from 'antd'
import { 
  MenuFoldOutlined, 
  MenuUnfoldOutlined,
  DashboardOutlined,
  BookOutlined,
  UserOutlined,
  StarOutlined,
  CalendarOutlined,
  LogoutOutlined,
  SettingOutlined
} from '@ant-design/icons'
import { useLocation, useNavigate } from 'react-router-dom'
import { useAuthStore } from '../store/authStore'

const { Header, Sider, Content } = AntLayout
const { Text } = Typography

function Layout({ children }) {
  const [collapsed, setCollapsed] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()
  const { user, logout } = useAuthStore()

  // 教师菜单
  const teacherMenuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘'
    },
    {
      key: '/courses',
      icon: <BookOutlined />,
      label: '课程管理'
    },
    {
      key: '/students',
      icon: <UserOutlined />,
      label: '学生管理'
    },
    {
      key: '/evaluations',
      icon: <StarOutlined />,
      label: '评价管理'
    },
    {
      key: '/sessions',
      icon: <CalendarOutlined />,
      label: '课程安排'
    }
  ]

  // 学生菜单
  const studentMenuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '我的仪表盘'
    },
    {
      key: '/courses',
      icon: <BookOutlined />,
      label: '我的课程'
    },
    {
      key: '/evaluations',
      icon: <StarOutlined />,
      label: '我的评价'
    }
  ]

  const menuItems = user?.role === 'TEACHER' ? teacherMenuItems : studentMenuItems

  const handleMenuClick = ({ key }) => {
    navigate(key)
  }

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置'
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ]

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)'
        }}
      >
        <div style={{ 
          height: '64px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <div style={{ 
            fontSize: collapsed ? '20px' : '16px', 
            fontWeight: 'bold', 
            color: '#ff6b6b',
            transition: 'all 0.3s'
          }}>
            {collapsed ? '🎌' : '🎌 日语教学'}
          </div>
        </div>
        
        <Menu
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ 
            border: 'none',
            marginTop: '16px'
          }}
        />
      </Sider>
      
      <AntLayout>
        <Header style={{ 
          padding: '0 24px', 
          background: '#fff',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space>
            <Text strong>
              {user?.role === 'TEACHER' ? '教师' : '学生'}: {user?.nickname || user?.username}
            </Text>
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
            >
              <Avatar 
                style={{ 
                  backgroundColor: '#ff6b6b',
                  cursor: 'pointer'
                }}
                icon={<UserOutlined />}
              />
            </Dropdown>
          </Space>
        </Header>
        
        <Content style={{ 
          margin: '24px',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 112px)'
        }}>
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  )
}

export default Layout
