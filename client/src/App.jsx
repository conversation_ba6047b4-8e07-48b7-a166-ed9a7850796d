import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider, App as AntdApp } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { useAuthStore } from './store/authStore'
import LoginPage from './pages/LoginPage'
import Layout from './components/Layout'

// 教师页面
import TeacherDashboard from './pages/teacher/TeacherDashboard'
import CourseManagement from './pages/teacher/CourseManagement'
import StudentManagement from './pages/teacher/StudentManagement'
import EvaluationManagement from './pages/teacher/EvaluationManagement'
import SessionManagement from './pages/teacher/SessionManagement'

// 学生页面
import StudentDashboard from './pages/student/StudentDashboard'
import MyEvaluations from './pages/student/MyEvaluations'
import CourseInfo from './pages/student/CourseInfo'
import StudentCourseDetail from './pages/student/StudentCourseDetail'

import './App.css'

function App() {
  const { user, isAuthenticated } = useAuthStore()

  if (!isAuthenticated) {
    return (
      <ConfigProvider locale={zhCN}>
        <AntdApp>
          <LoginPage />
        </AntdApp>
      </ConfigProvider>
    )
  }

  return (
    <ConfigProvider locale={zhCN}>
      <AntdApp>
        <Router>
          <Layout>
            <Routes>
              {user?.role === 'TEACHER' ? (
                <>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="dashboard" element={<TeacherDashboard />} />
                  <Route path="courses" element={<CourseManagement />} />
                  <Route path="students" element={<StudentManagement />} />
                  <Route path="evaluations" element={<EvaluationManagement />} />
                  <Route path="sessions" element={<SessionManagement />} />
                </>
              ) : (
                <>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="dashboard" element={<StudentDashboard />} />
                  <Route path="evaluations" element={<MyEvaluations />} />
                  <Route path="courses" element={<CourseInfo />} />
                  <Route path="courses/:id" element={<StudentCourseDetail />} />
                </>
              )}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </Layout>
        </Router>
      </AntdApp>
    </ConfigProvider>
  )
}

export default App
