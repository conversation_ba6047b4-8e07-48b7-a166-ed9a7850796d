import { Routes, Route, Navigate } from 'react-router-dom'
import { useAuthStore } from './store/authStore'
import LoginPage from './pages/LoginPage'
import TeacherLayout from './layouts/TeacherLayout'
import StudentLayout from './layouts/StudentLayout'
import TeacherDashboard from './pages/teacher/TeacherDashboard'
import CourseManagement from './pages/teacher/CourseManagement'
import CourseDetail from './pages/teacher/CourseDetail'
import SessionManagement from './pages/teacher/SessionManagement'
import EvaluationManagement from './pages/teacher/EvaluationManagement'
import StudentManagement from './pages/teacher/StudentManagement'
import StudentDashboard from './pages/student/StudentDashboard'
import MyEvaluations from './pages/student/MyEvaluations'
import CourseInfo from './pages/student/CourseInfo'

function App() {
  const { user, isAuthenticated } = useAuthStore()

  // 如果未登录，显示登录页面
  if (!isAuthenticated) {
    return <LoginPage />
  }

  // 根据用户角色显示不同的布局
  return (
    <Routes>
      {user?.role === 'TEACHER' ? (
        <Route path="/" element={<TeacherLayout />}>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<TeacherDashboard />} />
          <Route path="courses" element={<CourseManagement />} />
          <Route path="courses/:id" element={<CourseDetail />} />
          <Route path="sessions" element={<SessionManagement />} />
          <Route path="evaluations" element={<EvaluationManagement />} />
          <Route path="students" element={<StudentManagement />} />
        </Route>
      ) : (
        <Route path="/" element={<StudentLayout />}>
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<StudentDashboard />} />
          <Route path="evaluations" element={<MyEvaluations />} />
          <Route path="courses" element={<CourseInfo />} />
        </Route>
      )}
      {/* 404 页面 */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  )
}

export default App
