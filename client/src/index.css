/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
}

#root {
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 登录页面样式 - 日语教学主题 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 50%, #42a5f5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
  color: white;
  text-align: center;
  padding: 40px 20px;
}

.login-header h1 {
  font-size: 28px;
  margin-bottom: 8px;
  font-weight: 600;
}

.login-header p {
  opacity: 0.9;
  font-size: 14px;
}

.login-form {
  padding: 40px 30px;
}

/* 主布局样式 */
.main-layout {
  min-height: 100vh;
}

.main-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #ff6b6b;
}

.logo .anticon {
  font-size: 24px;
  margin-right: 8px;
}

/* 侧边栏样式 */
.main-sider {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.main-sider .ant-menu {
  border-right: none;
}

/* 内容区域样式 */
.main-content {
  padding: 24px;
  background: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 24px;
  margin-bottom: 24px;
}

/* 页面标题样式 */
.page-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.page-header h2 {
  margin: 0;
  color: #262626;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 8px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

/* 表格样式优化 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
}

/* 卡片样式 */
.stat-card {
  text-align: center;
  padding: 24px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-card .anticon {
  font-size: 32px;
  margin-bottom: 16px;
}

.stat-card h3 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}

.stat-card p {
  color: #8c8c8c;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .content-card {
    padding: 16px;
  }
  
  .login-form {
    padding: 30px 20px;
  }
}
