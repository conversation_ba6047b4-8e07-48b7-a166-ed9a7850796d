/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  overflow: hidden;
}

.login-header {
  text-align: center;
  padding: 20px 0;
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  color: white;
  margin: -24px -24px 24px -24px;
}

.login-header h1 {
  color: white;
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.login-header p {
  color: rgba(255, 255, 255, 0.9);
  margin: 8px 0 0 0;
  font-size: 14px;
}

.login-form {
  padding: 0 8px;
}

/* 布局样式 */
.layout-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.layout-logo {
  font-size: 20px;
  font-weight: bold;
  color: #ff6b6b;
}

.layout-content {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  min-height: calc(100vh - 112px);
}

/* 仪表盘样式 */
.dashboard-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dashboard-card .ant-card-head {
  border-bottom: 2px solid #ff6b6b;
}

.dashboard-card .ant-card-head-title {
  color: #ff6b6b;
  font-weight: bold;
}

/* 统计卡片样式 */
.stat-card {
  text-align: center;
  padding: 20px;
  border-radius: 8px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  color: white;
}

.stat-card .ant-statistic-title {
  color: rgba(255, 255, 255, 0.9);
}

.stat-card .ant-statistic-content {
  color: white;
}

/* 课程卡片样式 */
.course-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.course-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.course-card .ant-card-head {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  border: none;
}

.course-card .ant-card-head-title {
  color: white;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }
  
  .login-card {
    max-width: 100%;
  }
  
  .layout-content {
    margin: 12px;
    padding: 16px;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #ff6b6b;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ff5252;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 日语主题色彩 */
.japanese-theme {
  --primary-color: #ff6b6b;
  --secondary-color: #ffa500;
  --accent-color: #4ecdc4;
  --text-color: #333;
  --bg-color: #f8f9fa;
}

/* 特殊标记 */
.japanese-flag {
  display: inline-block;
  margin-right: 8px;
  font-size: 1.2em;
}

.course-type-tag {
  border-radius: 12px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 评价系统样式 */
.evaluation-card {
  margin-bottom: 16px;
  border-left: 4px solid #ff6b6b;
}

.evaluation-rating {
  color: #ffa500;
}

/* 课程详情页面样式 */
.course-detail-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
  color: white;
  padding: 24px;
  border-radius: 8px 8px 0 0;
  margin: -24px -24px 24px -24px;
}

.progress-card {
  text-align: center;
  padding: 16px;
  border-radius: 8px;
  background: #f8f9fa;
}

.next-session-card {
  border: 2px solid #ff6b6b;
  border-radius: 8px;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(255, 165, 0, 0.1) 100%);
}

/* 时间线样式 */
.session-timeline .ant-timeline-item-head {
  border-color: #ff6b6b;
}

.session-timeline .ant-timeline-item-tail {
  border-left-color: #ff6b6b;
}

/* 日历样式 */
.course-calendar .ant-picker-calendar-date-today {
  border-color: #ff6b6b;
}

.course-calendar .ant-picker-calendar-date-selected {
  background: #ff6b6b;
}

/* 加载动画 */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.empty-state .anticon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 16px;
}
