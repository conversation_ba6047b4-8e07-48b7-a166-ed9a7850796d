import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import api from '../utils/api'

export const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false,

      // 登录
      login: async (credentials) => {
        set({ loading: true })
        try {
          const response = await api.post('/auth/login', credentials)
          const { user, token } = response.data
          
          // 设置API默认header
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`
          
          set({
            user,
            token,
            isAuthenticated: true,
            loading: false
          })
          
          return { success: true }
        } catch (error) {
          set({ loading: false })
          return {
            success: false,
            message: error.response?.data?.message || '登录失败'
          }
        }
      },

      // 注册
      register: async (userData) => {
        set({ loading: true })
        try {
          const response = await api.post('/auth/register', userData)
          const { user, token } = response.data
          
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`
          
          set({
            user,
            token,
            isAuthenticated: true,
            loading: false
          })
          
          return { success: true }
        } catch (error) {
          set({ loading: false })
          return {
            success: false,
            message: error.response?.data?.message || '注册失败'
          }
        }
      },

      // 登出
      logout: () => {
        delete api.defaults.headers.common['Authorization']
        set({
          user: null,
          token: null,
          isAuthenticated: false
        })
      },

      // 更新用户信息
      updateUser: (userData) => {
        set(state => ({
          user: { ...state.user, ...userData }
        }))
      },

      // 初始化认证状态
      initAuth: () => {
        const { token } = get()
        if (token) {
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// 初始化认证状态
useAuthStore.getState().initAuth()
