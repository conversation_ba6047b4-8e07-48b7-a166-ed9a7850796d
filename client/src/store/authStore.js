import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import api from '../utils/api'

export const useAuthStore = create(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      loading: false,

      // 登录
      login: async (credentials) => {
        try {
          set({ loading: true })
          const response = await api.post('/auth/login', credentials)
          
          if (response.data.success) {
            const { user, token } = response.data.data
            set({
              user,
              token,
              isAuthenticated: true,
              loading: false
            })
            
            // 设置 API 默认 token
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`
            
            return { success: true }
          } else {
            set({ loading: false })
            return { success: false, message: response.data.message }
          }
        } catch (error) {
          set({ loading: false })
          return { 
            success: false, 
            message: error.response?.data?.message || '登录失败，请重试' 
          }
        }
      },

      // 注册
      register: async (userData) => {
        try {
          set({ loading: true })
          const response = await api.post('/auth/register', userData)
          
          if (response.data.success) {
            set({ loading: false })
            return { success: true, message: '注册成功，请登录' }
          } else {
            set({ loading: false })
            return { success: false, message: response.data.message }
          }
        } catch (error) {
          set({ loading: false })
          return { 
            success: false, 
            message: error.response?.data?.message || '注册失败，请重试' 
          }
        }
      },

      // 登出
      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false
        })
        
        // 清除 API 默认 token
        delete api.defaults.headers.common['Authorization']
      },

      // 初始化认证状态
      initAuth: () => {
        const { token } = get()
        if (token) {
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)

// 初始化认证状态
useAuthStore.getState().initAuth()
