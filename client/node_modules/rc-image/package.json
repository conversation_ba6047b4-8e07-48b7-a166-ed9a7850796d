{"name": "rc-image", "version": "7.12.0", "description": "React easy to use image component", "keywords": ["react", "react-component", "react-image", "image"], "homepage": "http://github.com/react-component/image", "bugs": {"url": "http://github.com/react-component/image/issues"}, "repository": {"type": "git", "url": "**************:react-component/image.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "types": "./lib/index.d.ts", "files": ["assets/*.css", "es", "lib"], "scripts": {"compile": "father build && lessc assets/index.less assets/index.css", "coverage": "rc-test --coverage", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "lint": "eslint src/ --ext .ts,.tsx,.jsx,.js,.md", "now-build": "npm run docs:build", "prepublishOnly": "npm run compile && np --yolo --no-publish --any-branch", "prettier": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "start": "dumi dev", "test": "rc-test"}, "dependencies": {"@babel/runtime": "^7.11.2", "@rc-component/portal": "^1.0.2", "classnames": "^2.2.6", "rc-dialog": "~9.6.0", "rc-motion": "^2.6.2", "rc-util": "^5.34.1"}, "devDependencies": {"@ant-design/icons": "^5.0.1", "@rc-component/father-plugin": "^1.0.2", "@testing-library/jest-dom": "^6.4.0", "@testing-library/react": "^15.0.6", "@types/classnames": "^2.2.10", "@types/jest": "^29.5.11", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^4.0.1", "cross-env": "^7.0.2", "dumi": "^2.1.4", "eslint": "^8.57.0", "father": "^4.0.0", "glob": "^7.1.6", "less": "^4.1.3", "np": "^7.0.0", "rc-test": "^7.0.3", "react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.3.3"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}