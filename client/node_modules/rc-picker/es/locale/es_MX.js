import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'es_MX',
  today: 'Hoy',
  now: 'Ahora',
  backToToday: 'Volver a hoy',
  ok: 'Aceptar',
  clear: 'Limpiar',
  week: 'Semana',
  month: 'Mes',
  year: 'Año',
  timeSelect: 'elegir hora',
  dateSelect: 'elegir fecha',
  weekSelect: 'elegir semana',
  monthSelect: 'Seleccionar mes',
  yearSelect: 'Seleccionar año',
  decadeSelect: 'Seleccionar década',
  dateFormat: 'D/M/YYYY',
  dateTimeFormat: 'D/M/YYYY HH:mm:ss',
  previousMonth: 'Mes anterior (PageUp)',
  nextMonth: '<PERSON><PERSON> siguiente (PageDown)',
  previousYear: 'Año anterior (Control + Left)',
  nextYear: 'Año siguiente (Control + Right)',
  previousDecade: 'Década anterior',
  nextDecade: 'Década siguiente',
  previousCentury: 'Siglo anterior',
  nextCentury: 'Siglo siguiente'
});
export default locale;