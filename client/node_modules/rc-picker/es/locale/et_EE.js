import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'et_EE',
  today: 'Täna',
  now: '<PERSON><PERSON><PERSON>',
  backToToday: '<PERSON>asi tänase juurde',
  ok: 'OK',
  clear: '<PERSON><PERSON><PERSON><PERSON>',
  week: 'Nädal',
  month: 'Kuu',
  year: 'Aasta',
  timeSelect: 'Vali aeg',
  dateSelect: 'Vali kuupäev',
  monthSelect: 'Vali kuu',
  yearSelect: 'Vali aasta',
  decadeSelect: 'Vali dekaad',
  dateFormat: 'D.M.YYYY',
  dateTimeFormat: 'D.M.YYYY HH:mm:ss',
  previousMonth: '<PERSON><PERSON><PERSON> kuu (PageUp)',
  nextMonth: '<PERSON><PERSON><PERSON><PERSON> kuu (PageDown)',
  previousYear: '<PERSON><PERSON><PERSON> aasta (Control + left)',
  nextYear: '<PERSON><PERSON><PERSON><PERSON> aasta (Control + right)',
  previousDecade: '<PERSON><PERSON><PERSON> dekaad',
  nextDecade: 'J<PERSON>rgmine dekaad',
  previousCentury: 'E<PERSON>mine sajand',
  nextCentury: 'Järgmine sajand'
});
export default locale;