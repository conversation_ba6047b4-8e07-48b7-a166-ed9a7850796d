import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import { commonLocale } from "./common";
var locale = _objectSpread(_objectSpread({}, commonLocale), {}, {
  locale: 'fi_FI',
  today: 'Tän<PERSON>än',
  now: 'Nyt',
  backToToday: 'Tämä päivä',
  ok: 'OK',
  clear: '<PERSON>h<PERSON>nn<PERSON>',
  week: 'Viikko',
  month: '<PERSON>ukaus<PERSON>',
  year: 'Vuosi',
  timeSelect: 'Valise aika',
  dateSelect: 'Valitse päivä',
  monthSelect: 'Valitse kuukausi',
  yearSelect: 'Valitse vuosi',
  decadeSelect: 'Valitse vuosikymmen',
  dateFormat: 'D.M.YYYY',
  dateTimeFormat: 'D.M.YYYY HH:mm:ss',
  previousMonth: 'Edellinen kuukausi (PageUp)',
  nextMonth: '<PERSON>uraava kuukausi (PageDown)',
  previousYear: 'Edellinen vuosi (Control + left)',
  nextYear: 'Seura<PERSON> vuosi (Control + right)',
  previousDecade: 'Edellinen vuosikymmen',
  nextDecade: '<PERSON>uraava vuosikymmen',
  previousCentury: 'Edellinen vuosisata',
  nextCentury: 'Seuraava vuosisata'
});
export default locale;