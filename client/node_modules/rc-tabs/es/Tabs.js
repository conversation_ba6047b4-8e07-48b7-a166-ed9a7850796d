import _extends from "@babel/runtime/helpers/esm/extends";
import _defineProperty from "@babel/runtime/helpers/esm/defineProperty";
import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import _typeof from "@babel/runtime/helpers/esm/typeof";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["id", "prefixCls", "className", "items", "direction", "activeKey", "defaultActiveKey", "editable", "animated", "tabPosition", "tabBarGutter", "tabBarStyle", "tabBarExtraContent", "locale", "more", "destroyInactiveTabPane", "renderTabBar", "onChange", "onTabClick", "onTabScroll", "getPopupContainer", "popupClassName", "indicator"];
// Accessibility https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Roles/Tab_Role
import classNames from 'classnames';
import useMergedState from "rc-util/es/hooks/useMergedState";
import isMobile from "rc-util/es/isMobile";
import * as React from 'react';
import { useEffect, useState } from 'react';
import TabContext from "./TabContext";
import TabNavListWrapper from "./TabNavList/Wrapper";
import TabPanelList from "./TabPanelList";
import useAnimateConfig from "./hooks/useAnimateConfig";
/**
 * Should added antd:
 * - type
 *
 * Removed:
 * - onNextClick
 * - onPrevClick
 * - keyboard
 */

// Used for accessibility
var uuid = 0;
var Tabs = /*#__PURE__*/React.forwardRef(function (props, ref) {
  var id = props.id,
    _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-tabs' : _props$prefixCls,
    className = props.className,
    items = props.items,
    direction = props.direction,
    activeKey = props.activeKey,
    defaultActiveKey = props.defaultActiveKey,
    editable = props.editable,
    animated = props.animated,
    _props$tabPosition = props.tabPosition,
    tabPosition = _props$tabPosition === void 0 ? 'top' : _props$tabPosition,
    tabBarGutter = props.tabBarGutter,
    tabBarStyle = props.tabBarStyle,
    tabBarExtraContent = props.tabBarExtraContent,
    locale = props.locale,
    more = props.more,
    destroyInactiveTabPane = props.destroyInactiveTabPane,
    renderTabBar = props.renderTabBar,
    onChange = props.onChange,
    onTabClick = props.onTabClick,
    onTabScroll = props.onTabScroll,
    getPopupContainer = props.getPopupContainer,
    popupClassName = props.popupClassName,
    indicator = props.indicator,
    restProps = _objectWithoutProperties(props, _excluded);
  var tabs = React.useMemo(function () {
    return (items || []).filter(function (item) {
      return item && _typeof(item) === 'object' && 'key' in item;
    });
  }, [items]);
  var rtl = direction === 'rtl';
  var mergedAnimated = useAnimateConfig(animated);

  // ======================== Mobile ========================
  var _useState = useState(false),
    _useState2 = _slicedToArray(_useState, 2),
    mobile = _useState2[0],
    setMobile = _useState2[1];
  useEffect(function () {
    // Only update on the client side
    setMobile(isMobile());
  }, []);

  // ====================== Active Key ======================
  var _useMergedState = useMergedState(function () {
      var _tabs$;
      return (_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key;
    }, {
      value: activeKey,
      defaultValue: defaultActiveKey
    }),
    _useMergedState2 = _slicedToArray(_useMergedState, 2),
    mergedActiveKey = _useMergedState2[0],
    setMergedActiveKey = _useMergedState2[1];
  var _useState3 = useState(function () {
      return tabs.findIndex(function (tab) {
        return tab.key === mergedActiveKey;
      });
    }),
    _useState4 = _slicedToArray(_useState3, 2),
    activeIndex = _useState4[0],
    setActiveIndex = _useState4[1];

  // Reset active key if not exist anymore
  useEffect(function () {
    var newActiveIndex = tabs.findIndex(function (tab) {
      return tab.key === mergedActiveKey;
    });
    if (newActiveIndex === -1) {
      var _tabs$newActiveIndex;
      newActiveIndex = Math.max(0, Math.min(activeIndex, tabs.length - 1));
      setMergedActiveKey((_tabs$newActiveIndex = tabs[newActiveIndex]) === null || _tabs$newActiveIndex === void 0 ? void 0 : _tabs$newActiveIndex.key);
    }
    setActiveIndex(newActiveIndex);
  }, [tabs.map(function (tab) {
    return tab.key;
  }).join('_'), mergedActiveKey, activeIndex]);

  // ===================== Accessibility ====================
  var _useMergedState3 = useMergedState(null, {
      value: id
    }),
    _useMergedState4 = _slicedToArray(_useMergedState3, 2),
    mergedId = _useMergedState4[0],
    setMergedId = _useMergedState4[1];

  // Async generate id to avoid ssr mapping failed
  useEffect(function () {
    if (!id) {
      setMergedId("rc-tabs-".concat(process.env.NODE_ENV === 'test' ? 'test' : uuid));
      uuid += 1;
    }
  }, []);

  // ======================== Events ========================
  function onInternalTabClick(key, e) {
    onTabClick === null || onTabClick === void 0 || onTabClick(key, e);
    var isActiveChanged = key !== mergedActiveKey;
    setMergedActiveKey(key);
    if (isActiveChanged) {
      onChange === null || onChange === void 0 || onChange(key);
    }
  }

  // ======================== Render ========================
  var sharedProps = {
    id: mergedId,
    activeKey: mergedActiveKey,
    animated: mergedAnimated,
    tabPosition: tabPosition,
    rtl: rtl,
    mobile: mobile
  };
  var tabNavBarProps = _objectSpread(_objectSpread({}, sharedProps), {}, {
    editable: editable,
    locale: locale,
    more: more,
    tabBarGutter: tabBarGutter,
    onTabClick: onInternalTabClick,
    onTabScroll: onTabScroll,
    extra: tabBarExtraContent,
    style: tabBarStyle,
    panes: null,
    getPopupContainer: getPopupContainer,
    popupClassName: popupClassName,
    indicator: indicator
  });
  return /*#__PURE__*/React.createElement(TabContext.Provider, {
    value: {
      tabs: tabs,
      prefixCls: prefixCls
    }
  }, /*#__PURE__*/React.createElement("div", _extends({
    ref: ref,
    id: id,
    className: classNames(prefixCls, "".concat(prefixCls, "-").concat(tabPosition), _defineProperty(_defineProperty(_defineProperty({}, "".concat(prefixCls, "-mobile"), mobile), "".concat(prefixCls, "-editable"), editable), "".concat(prefixCls, "-rtl"), rtl), className)
  }, restProps), /*#__PURE__*/React.createElement(TabNavListWrapper, _extends({}, tabNavBarProps, {
    renderTabBar: renderTabBar
  })), /*#__PURE__*/React.createElement(TabPanelList, _extends({
    destroyInactiveTabPane: destroyInactiveTabPane
  }, sharedProps, {
    animated: mergedAnimated
  }))));
});
if (process.env.NODE_ENV !== 'production') {
  Tabs.displayName = 'Tabs';
}
export default Tabs;