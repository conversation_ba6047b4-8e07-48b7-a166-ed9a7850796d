import type { CSSInterpolation } from '@ant-design/cssinjs';
import { Keyframes } from '@ant-design/cssinjs';
import type { AliasToken, TokenWithCommonCls } from '../../theme/internal';
export declare const moveDownIn: Keyframes;
export declare const moveDownOut: Keyframes;
export declare const moveLeftIn: Keyframes;
export declare const moveLeftOut: Keyframes;
export declare const moveRightIn: Keyframes;
export declare const moveRightOut: Keyframes;
export declare const moveUpIn: Keyframes;
export declare const moveUpOut: Keyframes;
type MoveMotionTypes = 'move-up' | 'move-down' | 'move-left' | 'move-right';
export declare const initMoveMotion: (token: TokenWithCommonCls<AliasToken>, motionName: MoveMotionTypes) => CSSInterpolation;
export {};
