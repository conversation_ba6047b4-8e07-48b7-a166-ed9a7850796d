{"name": "@rc-component/trigger", "version": "2.2.7", "description": "base abstract trigger component for react", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-trigger", "trigger"], "homepage": "https://github.com/react-component/trigger", "author": "", "repository": {"type": "git", "url": "https://github.com/react-component/trigger.git"}, "bugs": {"url": "https://github.com/react-component/trigger/issues"}, "files": ["es", "lib", "assets/**/*.css", "assets/**/*.less"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father build && lessc assets/index.less assets/index.css", "prepublishOnly": "npm run compile && np --yolo --no-publish --branch antd-v5", "lint": "eslint src/ docs/examples/ --ext .tsx,.ts,.jsx,.js", "test": "rc-test", "prettier": "prettier --write .", "coverage": "rc-test --coverage", "now-build": "npm run build"}, "devDependencies": {"@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^16.0.0", "@types/classnames": "^2.2.10", "@types/jest": "^29.5.2", "@types/node": "^22.0.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.11", "@umijs/fabric": "^4.0.1", "cross-env": "^7.0.1", "dumi": "^2.1.0", "eslint": "^8.51.0", "father": "^4.0.0", "less": "^4.2.0", "np": "^10.0.5", "prettier": "^3.3.3", "rc-test": "^7.0.13", "react": "^18.0.0", "react-dom": "^18.0.0", "regenerator-runtime": "^0.14.0", "typescript": "^5.1.6"}, "dependencies": {"@babel/runtime": "^7.23.2", "@rc-component/portal": "^1.1.0", "classnames": "^2.3.2", "rc-motion": "^2.0.0", "rc-resize-observer": "^1.3.1", "rc-util": "^5.44.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}